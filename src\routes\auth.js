// src/routes/auth.js
const express = require('express');
const path = require('path');
const fs = require('fs');
const router = express.Router();

/**
 * Simple authentication middleware for documentation access
 */
const simpleAuth = (req, res, next) => {
  // Skip auth for API endpoints, health checks, and OPTIONS requests
  if (req.path.startsWith('/api/') || req.path === '/health' || req.path === '/login' || req.path === '/auth-login' || req.method === 'OPTIONS') {
    return next();
  }

  // Check for simple auth parameter in URL
  if (req.query.access === 'granted') {
    return next();
  }

  // Redirect to login page
  return res.redirect('/login');
};

/**
 * Login page route
 */
router.get('/login', (req, res) => {
  try {
    const loginPath = path.join(__dirname, '../views/login.html');
    let loginHtml = fs.readFileSync(loginPath, 'utf8');
    
    // Replace error message placeholder
    const errorMessage = req.query.error ? 
      '<div class="error">Invalid password. Please try again.</div>' : '';
    loginHtml = loginHtml.replace('{{ERROR_MESSAGE}}', errorMessage);
    
    res.send(loginHtml);
  } catch (error) {
    console.error('Error serving login page:', error);
    res.status(500).send('Internal server error');
  }
});

/**
 * Login authentication route
 */
router.post('/auth-login', (req, res) => {
  try {
    const { password } = req.body;
    const correctPassword = (process.env.DOCS_ACCESS_PASSWORD || 'admin123').trim();
    const providedPassword = password?.trim();

    console.log('Login attempt:', {
      providedPassword: providedPassword,
      expectedPassword: correctPassword,
      match: providedPassword === correctPassword
    });

    if (providedPassword === correctPassword) {
      // Simply redirect with access parameter
      res.redirect('/?access=granted');
    } else {
      res.redirect('/login?error=1');
    }
  } catch (error) {
    console.error('Login error:', error);
    res.redirect('/login?error=1');
  }
});

/**
 * Logout route
 */
router.get('/logout', (req, res) => {
  res.redirect('/login');
});

module.exports = {
  router,
  simpleAuth
};
