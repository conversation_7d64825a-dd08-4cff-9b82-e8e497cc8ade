# 📋 Available Subdomains API - Updated Implementation

## 🎯 **Key Change Made**

**BEFORE:** API filtered organizations by status (only active/pending by default)
**AFTER:** API returns **ALL organizations regardless of status** by default

## 🚀 **API Endpoint**

```
GET /api/v1/organizations/subdomains
```

## ✅ **Default Behavior (Updated)**

- **Returns ALL organizations** from the database
- **No status filtering** applied by default
- **Includes:** active, pending, inactive, blocked organizations
- **All subdomains** are returned unless explicitly filtered

## 🔧 **Query Parameters**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number for pagination |
| `limit` | integer | 50 | Items per page (max 100) |
| `status` | string | 'all' | **Optional** status filter |
| `search` | string | '' | Search subdomain pattern |
| `sortBy` | string | 'subdomain' | Field to sort by |
| `sortOrder` | integer | 1 | Sort direction (1=asc, -1=desc) |

**Note:** Removed `includeInactive` parameter - no longer needed since ALL organizations are returned by default.

## 📊 **Example Responses**

### **Default Request (ALL Organizations):**
```bash
GET /api/v1/organizations/subdomains
```

**Returns:** All organizations (active, pending, inactive, blocked)

### **Filtered Request (Specific Status):**
```bash
GET /api/v1/organizations/subdomains?status=active
```

**Returns:** Only active organizations

### **Search Request:**
```bash
GET /api/v1/organizations/subdomains?search=acme
```

**Returns:** All organizations with "acme" in subdomain name

## 🔐 **Authentication & Authorization**

- **Authentication:** Required (JWT token)
- **Authorization:** `org_object:read` with system-wide access
- **RBAC:** Full role-based access control applied

## 📝 **Response Format**

```json
{
  "success": true,
  "data": {
    "subdomains": [
      {
        "id": "507f1f77bcf86cd799439011",
        "name": "Acme Corporation",
        "subdomain": "acme-corp",
        "status": "active",
        "memberCount": 25,
        "createdAt": "2025-06-04T10:30:00.000Z",
        "updatedAt": "2025-06-04T10:30:00.000Z",
        "url": "https://acme-corp.digimeet.live"
      },
      {
        "id": "507f1f77bcf86cd799439012",
        "name": "Beta Company",
        "subdomain": "beta-co",
        "status": "pending",
        "memberCount": 5,
        "createdAt": "2025-06-03T10:30:00.000Z",
        "updatedAt": "2025-06-03T10:30:00.000Z",
        "url": "https://beta-co.digimeet.live"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "pages": 3,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "metadata": {
      "totalOrganizations": 150,
      "statusDistribution": {
        "active": 120,
        "pending": 25,
        "inactive": 4,
        "blocked": 1
      },
      "searchTerm": null,
      "filters": {
        "status": null,
        "search": null
      },
      "note": "Returns ALL organizations regardless of status by default",
      "timestamp": "2025-06-04T12:30:00.000Z",
      "rootDomain": "digimeet.live"
    }
  }
}
```

## 🧪 **Testing**

### **1. Get All Subdomains (Default):**
```bash
curl -X GET 'http://localhost:3000/api/v1/organizations/subdomains' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

### **2. Filter by Status:**
```bash
curl -X GET 'http://localhost:3000/api/v1/organizations/subdomains?status=active' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

### **3. Search Subdomains:**
```bash
curl -X GET 'http://localhost:3000/api/v1/organizations/subdomains?search=acme' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

## 🎯 **Use Cases**

1. **Complete Subdomain List:** Get all subdomains for system overview
2. **Subdomain Validation:** Check if a subdomain exists before creation
3. **Dropdown Population:** Populate frontend dropdowns with all available subdomains
4. **Bulk Operations:** Export or process all organization subdomains
5. **Status-Specific Lists:** Filter by specific status when needed

## ✅ **Summary of Changes**

1. ✅ **Removed status filtering by default** - now returns ALL organizations
2. ✅ **Removed `includeInactive` parameter** - no longer needed
3. ✅ **Updated documentation** to reflect new behavior
4. ✅ **Updated response metadata** with explanatory note
5. ✅ **Updated test cases** to match new behavior
6. ✅ **Maintained all other functionality** (pagination, search, sorting)

## 🚀 **Result**

The API now returns **ALL organization subdomains** from the database by default, regardless of their status (active, pending, inactive, blocked). This provides complete visibility into all subdomains in the system without requiring any special parameters.

**Perfect for:** Subdomain validation, system overview, and comprehensive subdomain listings! 🎯
