{"dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-openid-connect": "^2.18.1", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "firebase": "^11.6.1", "firebase-admin": "^13.3.0", "mongoose": "^8.14.1", "node-cache": "^5.1.2", "nodemailer": "^7.0.3", "querystring": "^0.2.1", "rate-limit-redis": "^4.2.0", "redis": "^5.1.1", "serverless-http": "^3.2.0", "shortid": "^2.2.17", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo \"No build step\"", "env:local": "cp .env.local .env && echo \"Switched to LOCAL environment\"", "env:production": "cp .env.production .env && echo \"Switched to PRODUCTION environment\"", "start:local": "npm run env:local && npm start", "dev:local": "npm run env:local && npm run dev", "start:production": "npm run env:production && npm start", "dev:production": "npm run env:production && npm run dev", "init:roles": "node src/scripts/initializeRoles.js", "init:org-roles": "node src/scripts/initializeRoles.js org"}, "devDependencies": {"nodemon": "^3.1.10"}}