#!/usr/bin/env node

/**
 * Fix Script: Repair Null Role References in User Records
 * 
 * This script identifies users with null role references in their roles array
 * and fixes them by finding the appropriate RBAC role ID from the organization's
 * members array or by determining the intended role based on organization context.
 * 
 * Usage:
 *   node src/scripts/fix-null-role-references.js
 *   
 * Options:
 *   --dry-run    Show what would be fixed without making changes
 *   --verbose    Show detailed logging
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Organization = require('../models/Organization');
const RBACRole = require('../models/RBACRole');
const logger = require('../services/logger');

class NullRoleReferenceFixer {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.verbose = options.verbose || false;
    this.stats = {
      totalUsersChecked: 0,
      usersWithNullRoles: 0,
      rolesFixed: 0,
      rolesFailed: 0,
      organizationsProcessed: 0
    };
  }

  /**
   * Connect to MongoDB
   */
  async connect() {
    try {
      await mongoose.connect(process.env.MONGO_URI);
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error.message);
      process.exit(1);
    }
  }

  /**
   * Disconnect from MongoDB
   */
  async disconnect() {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }

  /**
   * Find users with null role references
   */
  async findUsersWithNullRoles() {
    const users = await User.find({
      'roles.role': null
    }).populate('roles.org', 'name subdomain members');

    return users.filter(user => 
      user.roles.some(roleEntry => roleEntry.role === null && roleEntry.org)
    );
  }

  /**
   * Find the appropriate RBAC role for a user in an organization
   */
  async findAppropriateRole(user, organization) {
    // First, check if the organization has this user in its members array
    const orgDoc = await Organization.findById(organization._id).populate('members.role');
    const memberEntry = orgDoc.members.find(member => 
      member.user.toString() === user._id.toString()
    );

    if (memberEntry && memberEntry.role) {
      if (this.verbose) {
        console.log(`   Found role in org members: ${memberEntry.role.name}`);
      }
      return memberEntry.role._id;
    }

    // If not found in members, try to determine role based on organization context
    // Check if user is the creator (should be super_user)
    if (orgDoc.createdBy && orgDoc.createdBy.toString() === user._id.toString()) {
      const superUserRole = await RBACRole.findOne({
        name: 'org_super_user',
        organization: organization._id
      });
      
      if (superUserRole) {
        if (this.verbose) {
          console.log(`   User is org creator, assigning super_user role`);
        }
        return superUserRole._id;
      }
    }

    // Default to member role if available
    const memberRole = await RBACRole.findOne({
      name: 'org_member',
      organization: organization._id
    });

    if (memberRole) {
      if (this.verbose) {
        console.log(`   Defaulting to member role`);
      }
      return memberRole._id;
    }

    // If no organization-specific roles exist, try to create them
    return await this.createMissingRole(organization._id, 'org_member');
  }

  /**
   * Create missing RBAC role for organization
   */
  async createMissingRole(organizationId, roleName) {
    try {
      const systemRoles = RBACRole.getSystemRoles();
      const roleTemplate = systemRoles.find(r => r.name === roleName);

      if (!roleTemplate) {
        console.log(`   ⚠️  No template found for role: ${roleName}`);
        return null;
      }

      if (!this.dryRun) {
        const newRole = await RBACRole.create({
          ...roleTemplate,
          organization: organizationId,
          audit: {
            createdBy: null, // System created
            createdAt: new Date(),
            usageCount: 0,
            changeHistory: [{
              action: 'created',
              timestamp: new Date(),
              performedBy: null,
              reason: 'Auto-created during null role reference fix'
            }]
          }
        });

        console.log(`   ✅ Created missing role: ${roleName} for organization`);
        return newRole._id;
      } else {
        console.log(`   🔍 Would create missing role: ${roleName} for organization`);
        return 'mock-role-id';
      }
    } catch (error) {
      console.log(`   ❌ Failed to create role ${roleName}:`, error.message);
      return null;
    }
  }

  /**
   * Fix null role reference for a user
   */
  async fixUserRoles(user) {
    let fixedCount = 0;
    let failedCount = 0;

    for (const roleEntry of user.roles) {
      if (roleEntry.role === null && roleEntry.org) {
        this.stats.organizationsProcessed++;
        
        try {
          const appropriateRoleId = await this.findAppropriateRole(user, roleEntry.org);
          
          if (appropriateRoleId) {
            if (!this.dryRun) {
              roleEntry.role = appropriateRoleId;
              await user.save();

              // Log the fix
              logger.info('Fixed null role reference', {
                component: 'fix-script',
                operation: 'fix_null_role_reference',
                metadata: {
                  user_id: user._id,
                  user_email: user.email,
                  organization_id: roleEntry.org._id,
                  organization_name: roleEntry.org.name,
                  new_role_id: appropriateRoleId,
                  assigned_at: roleEntry.assignedAt
                }
              });
            }

            console.log(`   ${this.dryRun ? '🔍 Would fix' : '✅ Fixed'} role for org: ${roleEntry.org.name}`);
            fixedCount++;
          } else {
            console.log(`   ❌ Could not determine appropriate role for org: ${roleEntry.org.name}`);
            failedCount++;
          }
        } catch (error) {
          console.log(`   ❌ Error fixing role for org ${roleEntry.org.name}:`, error.message);
          failedCount++;
        }
      }
    }

    return { fixedCount, failedCount };
  }

  /**
   * Run the fix process
   */
  async run() {
    console.log('🚀 Starting Null Role Reference Fix');
    console.log(`Mode: ${this.dryRun ? 'DRY RUN' : 'LIVE FIX'}`);
    console.log('');

    try {
      // Find users with null role references
      const usersWithNullRoles = await this.findUsersWithNullRoles();
      this.stats.usersWithNullRoles = usersWithNullRoles.length;

      console.log(`📊 Found ${usersWithNullRoles.length} users with null role references`);
      console.log('');

      if (usersWithNullRoles.length === 0) {
        console.log('✅ No users need fixing');
        return;
      }

      // Process each user
      for (const user of usersWithNullRoles) {
        this.stats.totalUsersChecked++;
        
        console.log(`👤 Processing user: ${user.email} (${user._id})`);
        
        const { fixedCount, failedCount } = await this.fixUserRoles(user);
        this.stats.rolesFixed += fixedCount;
        this.stats.rolesFailed += failedCount;

        if (this.verbose || fixedCount > 0 || failedCount > 0) {
          console.log(`   Fixed: ${fixedCount}, Failed: ${failedCount}`);
        }
        console.log('');
      }

      // Print summary
      console.log('📊 Fix Summary:');
      console.log(`   Users checked: ${this.stats.totalUsersChecked}`);
      console.log(`   Users with null roles: ${this.stats.usersWithNullRoles}`);
      console.log(`   Roles ${this.dryRun ? 'would be fixed' : 'fixed'}: ${this.stats.rolesFixed}`);
      console.log(`   Roles failed: ${this.stats.rolesFailed}`);
      console.log(`   Organizations processed: ${this.stats.organizationsProcessed}`);
      
      if (this.dryRun) {
        console.log('');
        console.log('🔍 This was a dry run. To apply fixes, run without --dry-run flag');
      }

    } catch (error) {
      console.error('❌ Fix process failed:', error.message);
      process.exit(1);
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');
const verbose = args.includes('--verbose');

// Run fix
async function main() {
  const fixer = new NullRoleReferenceFixer({ dryRun, verbose });
  
  await fixer.connect();
  await fixer.run();
  await fixer.disconnect();
  
  console.log('✅ Fix process completed');
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = NullRoleReferenceFixer;
