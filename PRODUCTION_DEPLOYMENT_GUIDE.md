# Production Deployment Guide for digimeet.live

## ✅ Configuration Status

Your RBAC API codebase has been successfully configured for production deployment with `digimeet.live` domain support.

## 🔧 Environment Configuration

### Updated .env Variables
```bash
# Domain Configuration
ROOT_DOMAIN=digimeet.live
PRODUCTION_SERVER_URL=https://digimeet.live
WILDCARD_DOMAIN=*.digimeet.live

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,https://auth-03-0.vercel.app,https://digimeet.live,https://*.digimeet.live
CORS_WILDCARD_ENABLED=true
CORS_SUBDOMAIN_PATTERN=*.digimeet.live

# OAuth Redirect URIs
REDIRECT_URI_WILDCARD=https://*.digimeet.live/api/v1/oauth/google/callback
REDIRECT_URI_ROOT=https://digimeet.live/api/v1/oauth/google/callback

# Email Callback URLs
EMAIL_LINK_CALLBACK_URL_WILDCARD=https://*.digimeet.live/api/v1/auth/email/callback
EMAIL_LINK_CALLBACK_URL_ROOT=https://digimeet.live/api/v1/auth/email/callback

# Production Security Settings
NODE_ENV=production
FORCE_HTTPS=true
SECURE_COOKIES=true
COOKIE_DOMAIN=.digimeet.live
SESSION_DOMAIN=.digimeet.live
SESSION_SECURE=true
SESSION_SAME_SITE=lax
TRUST_PROXY=true
```

## 🚀 Features Implemented

### ✅ 1. Domain-based Routing & Subdomain Handling
- **Subdomain extraction middleware** automatically detects organization subdomains
- **Organization mapping** resolves subdomains to organizations in database
- **Flexible routing** supports both `digimeet.live` and `*.digimeet.live` patterns
- **Caching system** for organization lookups (5-minute cache)

### ✅ 2. CORS Configuration
- **Wildcard domain support** for `*.digimeet.live`
- **Static origins** including your domain and development URLs
- **Credentials support** enabled for cross-origin requests
- **Dynamic CORS headers** based on subdomain context

### ✅ 3. Cookie/Session Management
- **Cross-subdomain cookies** with domain `.digimeet.live`
- **Secure session configuration** with MongoDB store
- **HttpOnly and Secure flags** for production security
- **SameSite policy** configured for cross-subdomain access

### ✅ 4. SSL/HTTPS Requirements
- **HTTPS redirect middleware** forces SSL in production
- **Security headers** including HSTS, CSP, and XSS protection
- **Secure cookie configuration** for production environment
- **Trust proxy settings** for SSL termination

### ✅ 5. Database Configuration
- **Production MongoDB URIs** properly configured
- **Separate audit database** for compliance logging
- **Connection pooling** and retry logic implemented
- **Graceful shutdown** handling for database connections

## 🔐 Security Features

### SSL/HTTPS Configuration
- Automatic HTTPS redirect in production
- HTTP Strict Transport Security (HSTS) headers
- Secure cookie flags enabled
- Content Security Policy (CSP) headers

### Session Security
- Cross-subdomain session support
- MongoDB session store with TTL
- Secure session cookies
- Session timeout configuration

### CORS Security
- Wildcard domain validation
- Origin verification
- Credentials handling
- Request header validation

## 📋 Pre-Deployment Checklist

### ✅ Environment Variables
- [x] ROOT_DOMAIN set to `digimeet.live`
- [x] PRODUCTION_SERVER_URL configured
- [x] CORS_ORIGINS updated with your domains
- [x] OAuth redirect URIs updated
- [x] Session secrets configured
- [x] Security flags enabled

### ⚠️ OAuth Provider Configuration
You need to update your OAuth provider settings:

#### Google OAuth Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Update your OAuth 2.0 Client ID with these redirect URIs:
   ```
   https://digimeet.live/api/v1/oauth/google/callback
   https://*.digimeet.live/api/v1/oauth/google/callback
   ```

#### LinkedIn OAuth (if used)
1. Update LinkedIn app settings with new redirect URIs
2. Configure callback URLs for your domain

### 🌐 DNS Configuration
Ensure your DNS is configured for wildcard subdomains:
```
A     digimeet.live          → Your server IP
CNAME *.digimeet.live        → digimeet.live
```

### 🔒 SSL Certificate
Ensure you have a wildcard SSL certificate for `*.digimeet.live`

## 🚀 Deployment Steps

### 1. Environment Setup
```bash
# Set production environment
export NODE_ENV=production

# Verify all environment variables are set
node -e "console.log(require('./src/config/productionConfig').validateConfiguration())"
```

### 2. Database Migration
```bash
# Ensure MongoDB is accessible
# Run any pending migrations
# Verify audit database connection
```

### 3. Application Deployment
```bash
# Install dependencies
npm install --production

# Start application
npm start
```

### 4. Health Check
```bash
# Test main domain
curl https://digimeet.live/health

# Test subdomain (replace 'test' with actual subdomain)
curl https://test.digimeet.live/health
```

## 🧪 Testing Subdomain Functionality

### Test Organization Subdomain
1. Create an organization with subdomain `test`
2. Access `https://test.digimeet.live`
3. Verify organization context is properly resolved
4. Test OAuth flow with subdomain

### Test CORS
```javascript
// Test from browser console on your domain
fetch('https://api.digimeet.live/health', {
  credentials: 'include'
}).then(r => r.json()).then(console.log);
```

## 🔍 Monitoring & Logging

### Health Endpoints
- `GET /health` - Basic health check
- `GET /api/v1/audit-logs/stats/summary` - System statistics

### Logging
- All requests logged with correlation IDs
- Audit logs stored in separate database
- Error tracking with context information

## 🛠️ Troubleshooting

### Common Issues
1. **CORS errors**: Check CORS_ORIGINS includes your domain
2. **OAuth failures**: Verify redirect URIs in provider console
3. **Session issues**: Check cookie domain configuration
4. **SSL errors**: Ensure HTTPS redirect is working

### Debug Commands
```bash
# Check configuration
node -e "console.log(process.env.ROOT_DOMAIN)"

# Test database connection
node -e "require('./src/services/auditDatabase').testConnection()"

# Validate CORS configuration
node -e "console.log(require('./src/config/corsConfig').validateConfiguration())"
```

## 📞 Support

If you encounter any issues during deployment:
1. Check the application logs
2. Verify environment variables
3. Test individual components
4. Review the troubleshooting section

Your RBAC API is now ready for production deployment with full subdomain support! 🎉
