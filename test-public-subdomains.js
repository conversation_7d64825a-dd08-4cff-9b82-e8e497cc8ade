#!/usr/bin/env node

/**
 * Test Script for Public Subdomains API
 * 
 * This script tests the new public subdomain endpoints to ensure they work
 * without authentication and return the expected response format.
 */

const http = require('http');

console.log('🧪 Testing Public Subdomains API Endpoints...\n');

// Test configuration
const baseUrl = 'http://localhost:3000';
const endpoints = [
  '/api/v1/subdomains'
];

/**
 * Make HTTP GET request
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Test an endpoint
 */
async function testEndpoint(endpoint) {
  const url = `${baseUrl}${endpoint}`;
  console.log(`🔍 Testing: ${endpoint}`);
  
  try {
    const response = await makeRequest(url);
    
    // Check status code
    if (response.statusCode === 200) {
      console.log('  ✅ Status Code: 200 OK');
    } else {
      console.log(`  ❌ Status Code: ${response.statusCode}`);
      return false;
    }
    
    // Check response structure
    if (response.data && typeof response.data === 'object') {
      console.log('  ✅ Valid JSON Response');
      
      // Check required fields
      if (response.data.success === true) {
        console.log('  ✅ Success field: true');
      } else {
        console.log('  ❌ Success field missing or false');
        return false;
      }
      
      if (response.data.data && Array.isArray(response.data.data.subdomains)) {
        console.log(`  ✅ Subdomains array found (${response.data.data.subdomains.length} items)`);
        
        // Show first few subdomains
        const subdomains = response.data.data.subdomains;
        if (subdomains.length > 0) {
          console.log(`  📋 Sample subdomains: ${subdomains.slice(0, 3).join(', ')}${subdomains.length > 3 ? '...' : ''}`);
        } else {
          console.log('  📋 No subdomains found in database');
        }
      } else {
        console.log('  ❌ Subdomains array missing or invalid');
        return false;
      }
      
      if (typeof response.data.data.total === 'number') {
        console.log(`  ✅ Total count: ${response.data.data.total}`);
      } else {
        console.log('  ❌ Total count missing or invalid');
        return false;
      }
      
      if (response.data.data.timestamp) {
        console.log(`  ✅ Timestamp: ${response.data.data.timestamp}`);
      } else {
        console.log('  ❌ Timestamp missing');
        return false;
      }
      
    } else {
      console.log('  ❌ Invalid JSON Response');
      console.log('  📄 Raw response:', response.data);
      return false;
    }
    
    console.log('  🎉 Endpoint test PASSED\n');
    return true;
    
  } catch (error) {
    console.log(`  ❌ Request failed: ${error.message}\n`);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting Public Subdomains API Tests\n');
  
  let passedTests = 0;
  let totalTests = endpoints.length;
  
  for (const endpoint of endpoints) {
    const passed = await testEndpoint(endpoint);
    if (passed) {
      passedTests++;
    }
  }
  
  console.log('📊 Test Results:');
  console.log(`   Passed: ${passedTests}/${totalTests}`);
  console.log(`   Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests PASSED! Public Subdomains API is working correctly.');
    console.log('\n📋 Summary:');
    console.log('   ✅ Single endpoint is accessible without authentication');
    console.log('   ✅ Response format is correct');
    console.log('   ✅ All required fields are present');
    console.log('   ✅ Subdomains are returned as expected');
    console.log('   ✅ Clean, single endpoint design');

    console.log('\n🧪 Manual Test Command:');
    console.log(`   curl -X GET '${baseUrl}/api/v1/subdomains'`);
    
  } else {
    console.log('\n❌ Some tests FAILED. Please check the implementation.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(console.error);
