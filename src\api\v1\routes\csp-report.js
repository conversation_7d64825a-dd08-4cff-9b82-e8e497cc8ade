// src/api/v1/routes/csp-report.js
const express = require('express');
const logger = require('../../../services/logger');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: CSP
 *   description: Content Security Policy violation reporting
 */

/**
 * @swagger
 * /api/v1/csp-report:
 *   post:
 *     summary: CSP Violation Report
 *     tags: [CSP]
 *     description: |
 *       Endpoint for receiving Content Security Policy violation reports.
 *       This endpoint is automatically called by browsers when CSP violations occur.
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/csp-report:
 *           schema:
 *             type: object
 *             properties:
 *               csp-report:
 *                 type: object
 *                 properties:
 *                   document-uri:
 *                     type: string
 *                   referrer:
 *                     type: string
 *                   violated-directive:
 *                     type: string
 *                   effective-directive:
 *                     type: string
 *                   original-policy:
 *                     type: string
 *                   blocked-uri:
 *                     type: string
 *                   status-code:
 *                     type: integer
 *     responses:
 *       204:
 *         description: CSP violation report received successfully
 *       400:
 *         description: Invalid CSP report format
 */
router.post('/', express.json({ type: 'application/csp-report' }), (req, res) => {
  try {
    const report = req.body['csp-report'];
    
    if (!report) {
      return res.status(400).json({
        error: 'Invalid CSP report format',
        message: 'Expected csp-report object in request body'
      });
    }

    // Log CSP violation for monitoring
    logger.warn('CSP Violation Detected', {
      component: 'csp-report',
      operation: 'violation_report',
      metadata: {
        document_uri: report['document-uri'],
        referrer: report['referrer'],
        violated_directive: report['violated-directive'],
        effective_directive: report['effective-directive'],
        blocked_uri: report['blocked-uri'],
        status_code: report['status-code'],
        user_agent: req.get('user-agent'),
        timestamp: new Date().toISOString()
      }
    });

    // Also log to console for immediate visibility
    console.warn('🚨 CSP Violation:', {
      page: report['document-uri'],
      directive: report['violated-directive'],
      blocked: report['blocked-uri'],
      timestamp: new Date().toISOString()
    });

    // Return 204 No Content as per CSP specification
    res.status(204).end();
  } catch (error) {
    logger.error('CSP report processing error', {
      component: 'csp-report',
      operation: 'report_processing_error',
      error: error.message,
      metadata: {
        request_body: req.body,
        user_agent: req.get('user-agent')
      }
    });

    res.status(500).json({
      error: 'Failed to process CSP report',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/csp-report/stats:
 *   get:
 *     summary: CSP Violation Statistics
 *     tags: [CSP]
 *     description: |
 *       Returns statistics about CSP violations (if logging is enabled).
 *       This endpoint helps monitor CSP policy effectiveness.
 *     security: []
 *     responses:
 *       200:
 *         description: CSP violation statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 note:
 *                   type: string
 */
router.get('/stats', (req, res) => {
  try {
    // For now, just return a message indicating CSP reporting is active
    res.json({
      message: 'CSP violation reporting is active',
      note: 'Violations are logged to the application logs',
      timestamp: new Date().toISOString(),
      csp_reporting_endpoint: '/api/v1/csp-report',
      debug_endpoint: '/debug/csp'
    });
  } catch (error) {
    logger.error('CSP stats endpoint error', {
      component: 'csp-report',
      operation: 'stats_error',
      error: error.message
    });

    res.status(500).json({
      error: 'Failed to get CSP stats',
      message: error.message
    });
  }
});

module.exports = router;
