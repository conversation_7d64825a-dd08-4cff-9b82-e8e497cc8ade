// src/index.js - Main application entry point
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const session = require('express-session');
const cookieParser = require('cookie-parser');

// Configuration
const config = require('./config/environment');
const productionConfig = require('./config/productionConfig');

// Services
const auditDatabase = require('./services/auditDatabase');
const logger = require('./services/logger');

// Middleware
const auditMiddleware = require('./middleware/auditMiddleware');
const subdomainMiddleware = require('./middleware/subdomainMiddleware');
const rateLimitMiddleware = require('./middleware/rateLimitMiddleware');
const auth0Config = require('./config/auth0Config');
const auth0Middleware = require('./middleware/auth0Middleware');

// Routes
const docsRoutes = require('./routes/docs');
const debugRoutes = require('./routes/debug');
const { router: authRoutes, simpleAuth } = require('./routes/auth');
const userRoutes = require('./api/v1/routes/users');
const apiAuthRoutes = require('./api/v1/routes/auth');
const oauthRoutes = require('./api/v1/routes/oauth');
const configRoutes = require('./api/v1/routes/config');
const organizationRoutes = require('./api/v1/routes/organizations');
const refreshMw = require('./middleware/refreshToken');
const roleRoutes = require('./api/v1/routes/roles-crud');
const privilegeRoutes = require('./api/v1/routes/privilege-management');
const auditLogRoutes = require('./api/v1/routes/audit-logs');
const auth0Routes = require('./api/v1/routes/auth0');
const cspReportRoutes = require('./api/v1/routes/csp-report');

// Models
const Config = require('./models/Config');
const Role = require('./models/Role');
const RBACRole = require('./models/RBACRole');

const app = express();

/**
 * Configure application middleware
 */
function configureMiddleware(app) {
  // Apply production configuration (HTTPS redirects, security headers, etc.)
  if (config.deployment.isProduction) {
    productionConfig.configureApp(app);
  }

  // Session configuration (only in development)
  if (!config.deployment.isProduction) {
    app.use(session({
      secret: config.server.sessionSecret,
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: false,
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      }
    }));

    // Cookie parser for auth tokens (in development)
    app.use(cookieParser());
  }

  // JSON + URL-encoded parsing
  app.use(express.urlencoded({ extended: true }));
  app.use(express.json());

  // Rate limiting (before CORS and other middleware)
  app.use(rateLimitMiddleware.dynamicLimit());

  // Simple and effective CORS configuration
  app.use(cors({
    origin: [
      'https://digimeet.live',
      'https://digimeet.biz',
      'https://digimeet.net',
      /^https:\/\/.*\.digimeet\.live$/, // Allow all subdomains of digimeet.live
      /^https:\/\/.*\.digimeet\.biz$/,  // Allow all subdomains of digimeet.biz
      /^https:\/\/.*\.digimeet\.net$/,  // Allow all subdomains of digimeet.net
      // Development origins
      'http://localhost:3000',
      'http://localhost:4200',
      /^https:\/\/.*\.vercel\.app$/ // Allow Vercel deployments
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Organization-Id',
      'X-Subdomain'
    ]
  }));

  // Handle preflight OPTIONS requests properly (before any redirects)
  app.use((req, res, next) => {
    if (req.method === 'OPTIONS') {
      res.status(204).end();
    } else {
      next();
    }
  });

  // Subdomain extraction middleware (before other middleware)
  app.use(subdomainMiddleware.middleware());

  // Auth0 subdomain configuration middleware (after subdomain extraction)
  app.use(auth0Config.subdomainConfigMiddleware());

  // Auth0 authentication middleware (with subdomain support)
  app.use(auth0Middleware.initialize());

  // Auth0 user sync middleware (sync Auth0 users with local database)
  app.use(auth0Middleware.syncUserMiddleware());

  // Note: Subdomain CORS is now handled by the main CORS configuration above

  // Audit logging middleware (after subdomain extraction)
  app.use(auditMiddleware.middleware());
}

/**
 * Configure application routes
 */
function configureRoutes(app) {
  // Authentication routes (login/logout)
  app.use('/', authRoutes);

  // Documentation and health routes
  app.use('/', docsRoutes);

  // Debug routes
  app.use('/debug', debugRoutes);

  // Environment check endpoint
  app.get('/env-check', (req, res) => {
    res.json({
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || 3000,
      rootDomain: process.env.ROOT_DOMAIN || 'localhost',
      serverUrl: process.env.SERVER_URL || 'http://localhost:3000',
      isProduction: process.env.NODE_ENV === 'production',
      corsOrigins: process.env.ADDITIONAL_CORS_ORIGINS || 'none set',
      timestamp: new Date().toISOString()
    });
  });

  // API routes
  app.use('/api/v1/users', userRoutes);
  app.use('/api/v1/auth', apiAuthRoutes);
  app.use('/api/v1/auth0', auth0Routes);
  app.use('/api/v1/oauth', oauthRoutes);
  app.use('/api/v1/organizations', organizationRoutes);
  app.use('/api/v1/config', configRoutes);
  app.use('/api/v1/middleware', refreshMw);
  app.use('/api/v1/roles', roleRoutes);
  app.use('/api/v1/privileges', privilegeRoutes);
  app.use('/api/v1/audit-logs', auditLogRoutes);
  app.use('/api/v1/csp-report', cspReportRoutes);
}

/**
 * Initialize database and seed default data
 */
async function initializeDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.database.mongoUri);
    console.log('connected_to_mongo');

    // Initialize audit database connection
    try {
      await auditDatabase.connect();
      await auditDatabase.createIndexes();
      logger.info('Audit database initialized successfully', {
        component: 'app-startup',
        operation: 'audit_db_init'
      });
    } catch (auditError) {
      logger.error('Failed to initialize audit database', {
        component: 'app-startup',
        operation: 'audit_db_init_error',
        error: auditError.message
      });
      console.warn('Audit database initialization failed - continuing without audit logging');
    }

    // Config seeding
    const existing = await Config.findOne();
    if (!existing) {
      await Config.create({
        smtp: {
          user: config.email.smtpUser,
          pass: config.email.smtpPass,
          fromAddress: config.email.smtpFrom
        }
      });
      console.log('SMTP settings initialized from environment');
    }

    // Role seeding
    await seedDefaultRoles();

    // Initialize RBAC system roles
    await initializeRBACRoles();

  } catch (err) {
    console.error('mongo_connection_error:', err);
    process.exit(1);
  }
}

/**
 * Initialize RBAC system roles
 */
async function initializeRBACRoles() {
  try {
    const systemRoles = RBACRole.getSystemRoles();
    const systemScopedRoles = systemRoles.filter(r => r.scope === 'system');

    let createdCount = 0;

    for (const roleTemplate of systemScopedRoles) {
      const existingRole = await RBACRole.findOne({
        name: roleTemplate.name,
        scope: 'system'
      });

      if (!existingRole) {
        await RBACRole.create({
          ...roleTemplate,
          audit: {
            createdBy: null,
            createdAt: new Date(),
            usageCount: 0,
            changeHistory: [{
              action: 'created',
              timestamp: new Date(),
              performedBy: null,
              reason: 'System startup initialization'
            }]
          }
        });
        createdCount++;
        console.log(`Created RBAC system role: ${roleTemplate.name}`);
      }
    }

    if (createdCount > 0) {
      console.log(`Initialized ${createdCount} RBAC system roles`);
    } else {
      console.log('RBAC system roles already initialized');
    }
  } catch (error) {
    console.warn('Failed to initialize RBAC roles:', error.message);
  }
}

/**
 * Seed default roles for the application
 */
async function seedDefaultRoles() {
  const DEFAULT_ORG_ID = config.development.defaultOrgId;
  const defaultRoles = [
    {
      org: DEFAULT_ORG_ID,
      name: 'superadmin',
      description: 'Superuser for org (all permissions)',
      hierarchy: { level: 1, type: 'organization' },
      permissions: [
        { resource: 'user_object', actions: ['create', 'read', 'update', 'delete'] },
        { resource: 'org_object', actions: ['create', 'read', 'update', 'delete'] },
        { resource: 'role_object', actions: ['create', 'read', 'update', 'delete', 'assign'] },
        { resource: 'event', actions: ['create', 'read', 'update', 'delete'] },
        { resource: 'ticket', actions: ['create', 'read', 'update', 'delete'] }
      ],
      metadata: { isActive: true, isSystem: false }
    },
    {
      org: DEFAULT_ORG_ID,
      name: 'orgadmin',
      description: 'Organization admin',
      hierarchy: { level: 2, type: 'organization' },
      permissions: [
        { resource: 'user_object', actions: ['create', 'read', 'update'] },
        { resource: 'org_object', actions: ['read', 'update'] },
        { resource: 'role_object', actions: ['read', 'assign'] },
        { resource: 'event', actions: ['create', 'read', 'update'] },
        { resource: 'ticket', actions: ['create', 'read', 'update'] }
      ],
      metadata: { isActive: true, isSystem: false }
    },
    {
      org: DEFAULT_ORG_ID,
      name: 'orgmember',
      description: 'Regular org member',
      hierarchy: { level: 5, type: 'organization' },
      permissions: [
        { resource: 'user_object', actions: ['read'] },
        { resource: 'event', actions: ['read'] },
        { resource: 'ticket', actions: ['create', 'read'] }
      ],
      metadata: { isActive: true, isSystem: false }
    }
  ];

  try {
    const seeded = await Role.findOne({ org: DEFAULT_ORG_ID });
    if (!seeded) {
      await Role.insertMany(defaultRoles);
      console.log(`Seeded default roles for org ${DEFAULT_ORG_ID}`);
    } else {
      console.log(`Roles already seeded for org ${DEFAULT_ORG_ID}`);
    }
  } catch (roleError) {
    console.warn('Role seeding error:', roleError.message);
    console.log('Continuing without role seeding...');
  }
}

/**
 * Setup graceful shutdown handling
 */
function setupGracefulShutdown() {
  process.on('SIGINT', async () => {
    console.log('\nGraceful shutdown initiated...');

    try {
      // Flush any pending audit logs
      const auditLogger = require('./services/auditLogger');
      await auditLogger.forceFlush();

      // Close audit database connection
      await auditDatabase.close();

      // Close main database connection
      await mongoose.connection.close();

      console.log('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      console.error('Error during graceful shutdown:', error.message);
      process.exit(1);
    }
  });
}

/**
 * Start the server
 */
function startServer() {
  if (require.main === module) {
    const port = config.server.port;
    app.listen(port, () => {
      console.log(`server_running_on_port_${port}`);
      console.log(`Audit logging: ${config.audit.loggingEnabled ? 'ENABLED' : 'DISABLED'}`);
      console.log(`Swagger UI: http://localhost:${port}/api-docs-ui`);
      console.log(`API Docs: http://localhost:${port}/api-docs`);
    });
  }
}

// Initialize application
async function initializeApp() {
  configureMiddleware(app);
  configureRoutes(app);
  await initializeDatabase();
  setupGracefulShutdown();
  startServer();
}

// Start the application
initializeApp().catch(console.error);

module.exports = app;
