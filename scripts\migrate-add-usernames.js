#!/usr/bin/env node

/**
 * Migration Script: Add usernames to existing users
 * 
 * This script adds the new user_name field to all existing users in the database.
 * It generates unique usernames based on existing name or email fields.
 * 
 * Usage: node scripts/migrate-add-usernames.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../src/models/User');
const UsernameGenerator = require('../src/utils/usernameGenerator');

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
}

// Migration function
async function migrateUsernames() {
  try {
    console.log('🔍 Starting username migration...\n');

    // Find all users without user_name field
    const usersWithoutUsername = await User.find({
      $or: [
        { user_name: { $exists: false } },
        { user_name: null },
        { user_name: '' }
      ]
    }).select('_id name email profile.fullName');

    console.log(`📊 Found ${usersWithoutUsername.length} users without usernames`);

    if (usersWithoutUsername.length === 0) {
      console.log('✅ All users already have usernames. Migration not needed.');
      return;
    }

    let successCount = 0;
    let errorCount = 0;

    // Process each user
    for (const user of usersWithoutUsername) {
      try {
        // Determine the name to use for username generation
        const nameForUsername = user.profile?.fullName || user.name || user.email.split('@')[0];
        
        // Generate unique username
        const generatedUsername = await UsernameGenerator.generateUsername(nameForUsername);
        
        // Update user with generated username
        await User.findByIdAndUpdate(user._id, { user_name: generatedUsername });
        
        console.log(`✅ User ${user.email}: ${nameForUsername} → ${generatedUsername}`);
        successCount++;
        
      } catch (error) {
        console.error(`❌ Failed to generate username for user ${user.email}:`, error.message);
        errorCount++;
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Successfully migrated: ${successCount} users`);
    console.log(`   ❌ Failed: ${errorCount} users`);
    console.log(`   📈 Total processed: ${successCount + errorCount} users`);

    if (errorCount > 0) {
      console.log('\n⚠️  Some users failed to migrate. Please check the errors above.');
    } else {
      console.log('\n🎉 All users successfully migrated!');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  }
}

// Validation function
async function validateMigration() {
  try {
    console.log('\n🔍 Validating migration...');

    // Check for users without usernames
    const usersWithoutUsername = await User.countDocuments({
      $or: [
        { user_name: { $exists: false } },
        { user_name: null },
        { user_name: '' }
      ]
    });

    // Check for duplicate usernames
    const duplicateUsernames = await User.aggregate([
      { $group: { _id: '$user_name', count: { $sum: 1 } } },
      { $match: { count: { $gt: 1 } } }
    ]);

    // Check total users with usernames
    const totalUsersWithUsernames = await User.countDocuments({
      user_name: { $exists: true, $ne: null, $ne: '' }
    });

    console.log('📊 Validation Results:');
    console.log(`   👥 Total users with usernames: ${totalUsersWithUsernames}`);
    console.log(`   ❌ Users without usernames: ${usersWithoutUsername}`);
    console.log(`   🔄 Duplicate usernames: ${duplicateUsernames.length}`);

    if (usersWithoutUsername === 0 && duplicateUsernames.length === 0) {
      console.log('✅ Migration validation passed!');
      return true;
    } else {
      console.log('❌ Migration validation failed!');
      if (duplicateUsernames.length > 0) {
        console.log('   Duplicate usernames found:', duplicateUsernames);
      }
      return false;
    }

  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    return false;
  }
}

// Rollback function (for testing)
async function rollbackMigration() {
  try {
    console.log('🔄 Rolling back migration (removing user_name field)...');
    
    const result = await User.updateMany(
      {},
      { $unset: { user_name: 1 } }
    );

    console.log(`✅ Rollback complete. Removed user_name from ${result.modifiedCount} users.`);
    
  } catch (error) {
    console.error('❌ Rollback failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await connectDB();

    // Check command line arguments
    const args = process.argv.slice(2);
    
    if (args.includes('--rollback')) {
      await rollbackMigration();
    } else if (args.includes('--validate')) {
      await validateMigration();
    } else {
      // Run migration
      await migrateUsernames();
      
      // Validate results
      const isValid = await validateMigration();
      
      if (!isValid) {
        console.log('\n⚠️  Migration completed but validation failed. Please review the results.');
        process.exit(1);
      }
    }

  } catch (error) {
    console.error('💥 Script failed:', error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⏹️  Script interrupted. Cleaning up...');
  await mongoose.disconnect();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  migrateUsernames,
  validateMigration,
  rollbackMigration
};
