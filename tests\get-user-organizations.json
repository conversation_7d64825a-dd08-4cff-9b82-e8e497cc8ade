{"test_name": "Get User Organizations", "description": "Test retrieving user's organizations with various filters", "tests": [{"case": "Get all user organizations", "endpoint": "GET /api/v1/organizations/my-organizations", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "expected_response": {"status": 200, "body": {"success": true, "data": {"organizations": [{"organization": {"_id": "string", "name": "string", "subdomain": "string", "status": "string", "branding": {}}, "role": "string", "joinedAt": "string", "status": "string"}], "total": "number"}, "metadata": {"type": "user_organizations", "retrieved_at": "string", "duration_ms": "number"}}}, "curl_command": "curl -X GET 'https://your-domain.vercel.app/api/v1/organizations/my-organizations' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE'"}, {"case": "Get current organization", "endpoint": "GET /api/v1/organizations/my-organizations?current=true", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "expected_response": {"status": 200, "body": {"success": true, "data": {"_id": "string", "name": "string", "subdomain": "string", "status": "string", "branding": {}, "userRole": "string", "joinedAt": "string", "userStatus": "string"}, "metadata": {"type": "current_organization", "retrieved_at": "string", "duration_ms": "number"}}}, "curl_command": "curl -X GET 'https://your-domain.vercel.app/api/v1/organizations/my-organizations?current=true' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE'"}, {"case": "Filter by subdomain", "endpoint": "GET /api/v1/organizations/my-organizations?subdomain=test-org-001", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "curl_command": "curl -X GET 'https://your-domain.vercel.app/api/v1/organizations/my-organizations?subdomain=test-org-001' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE'"}, {"case": "Filter by status", "endpoint": "GET /api/v1/organizations/my-organizations?status=active", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "curl_command": "curl -X GET 'https://your-domain.vercel.app/api/v1/organizations/my-organizations?status=active' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE'"}]}