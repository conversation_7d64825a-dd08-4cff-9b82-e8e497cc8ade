const express = require('express');
const router = express.Router();
const { authenticate } = require('../../../middleware/auth');
const { rbac } = require('../../../middleware/rbac');
const organizationController = require('../controllers/organizationController');
const rateLimitMiddleware = require('../../../middleware/rateLimitMiddleware');



/**
 * @swagger
 * tags:
 *   - name: Organizations
 *     description: Organization management CRUD (RBAC)
 */

// /**
//  * @swagger
//  * /api/v1/organizations:
//  *   post:
//  *     summary: Create a new organization (subdomain is unique)
//  *     tags: [Organizations]
//  *     security:
//  *       - bearerAuth: []
//  *       - OAuth2PasswordBearer: []
//  *     requestBody:
//  *       required: true
//  *       content:
//  *         application/json:
//  *           schema:
//  *             type: object
//  *             required: [name, subdomain]
//  *             properties:
//  *               name:
//  *                 type: string
//  *                 example: Acme Corporation
//  *               subdomain:
//  *                 type: string
//  *                 example: acme
//  *               branding:
//  *                 type: object
//  *                 properties:
//  *                   logoUrl:
//  *                     type: string
//  *                     example: https://acme.com/logo.png
//  *                   primaryColor:
//  *                     type: string
//  *                     example: '#336699'
//  *     responses:
//  *       201:
//  *         description: Organization created
//  *       409:
//  *         description: Subdomain already taken
//  *       400:
//  *         description: Bad request
//  */
// router.post(
//   '/',
//   authenticate,
//   rbac('org:create'),
//   organizationController.createOrganization.bind(organizationController)
// );

/**
 * @swagger
 * /api/v1/organizations:
 *   get:
 *     summary: Get all organizations (paginated)
 *     tags: [Organizations]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: List of organizations
 */
router.get('/', organizationController.getOrganizations.bind(organizationController));

/**
 * @swagger
 * /api/v1/organizations/pending:
 *   get:
 *     summary: Get all pending organizations awaiting approval (God Super User only)
 *     description: |
 *       Retrieve a paginated list of organizations with "pending" status that are awaiting approval.
 *       This endpoint is restricted to users with God Super User privileges only.
 *
 *       **Administrative Operation**: This endpoint is logged for audit purposes as it provides
 *       access to sensitive organizational data requiring the highest privilege level.
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *         example: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of organizations per page
 *         example: 10
 *     responses:
 *       200:
 *         description: Pending organizations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     organizations:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           _id:
 *                             type: string
 *                             example: "6838bfd6278ccc88e4179049"
 *                           name:
 *                             type: string
 *                             example: "Acme Corporation"
 *                           subdomain:
 *                             type: string
 *                             example: "acme-corp"
 *                           branding:
 *                             type: object
 *                             properties:
 *                               logoUrl:
 *                                 type: string
 *                                 example: "https://acme.com/logo.png"
 *                               primaryColor:
 *                                 type: string
 *                                 example: "#336699"
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: "2025-05-29T20:13:10.956Z"
 *                           createdBy:
 *                             type: object
 *                             properties:
 *                               _id:
 *                                 type: string
 *                                 example: "68330577e2576bff416a2ae7"
 *                               name:
 *                                 type: string
 *                                 example: "John Doe"
 *                               email:
 *                                 type: string
 *                                 example: "<EMAIL>"
 *                           memberCount:
 *                             type: integer
 *                             example: 1
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 10
 *                         total:
 *                           type: integer
 *                           example: 25
 *                         pages:
 *                           type: integer
 *                           example: 3
 *                         hasNext:
 *                           type: boolean
 *                           example: true
 *                         hasPrev:
 *                           type: boolean
 *                           example: false
 *                 metadata:
 *                   type: object
 *                   properties:
 *                     retrieved_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-05-29T20:20:52.975Z"
 *                     duration_ms:
 *                       type: integer
 *                       example: 57
 *                     privilege_level:
 *                       type: string
 *                       example: "god_super_user"
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *       403:
 *         description: Insufficient privileges (God Super User required)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Only God Super Users can access pending organizations"
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "INSUFFICIENT_PRIVILEGES"
 *                     details:
 *                       type: string
 *                       example: "This operation requires God Super User privileges"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to fetch pending organizations"
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "PENDING_ORGS_FETCH_ERROR"
 *                     details:
 *                       type: string
 *                       example: "An error occurred while retrieving pending organizations"
 */
router.get(
  '/pending',
  authenticate,
  rbac('org_object:read', { allowSystemWide: true, requireOrgContext: false }),
  organizationController.getPendingOrganizations.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/{idOrSub}:
 *   get:
 *     summary: Get organization by ID or subdomain
 *     tags: [Organizations]
 *     parameters:
 *       - in: path
 *         name: idOrSub
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ObjectID or subdomain (e.g., "663041cf7a14c7c000a3f999" or "acme")
 *     responses:
 *       200:
 *         description: Organization found
 *       404:
 *         description: Not found
 */
router.get('/:idOrSub', organizationController.getOrganization.bind(organizationController));

/**
 * @swagger
 * /api/v1/organizations/{idOrSub}:
 *   put:
 *     summary: Update organization by ID or subdomain
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: idOrSub
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ObjectID or subdomain
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               branding:
 *                 type: object
 *                 properties:
 *                   logoUrl:
 *                     type: string
 *                   primaryColor:
 *                     type: string
 *     responses:
 *       200:
 *         description: Organization updated
 *       404:
 *         description: Not found
 */
router.put(
  '/:idOrSub',
  authenticate,
  (req, _res, next) => { req.orgId = req.params.idOrSub; next(); },
  rbac('org:update'),
  organizationController.updateOrganization.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/{idOrSub}:
 *   delete:
 *     summary: Delete organization by ID or subdomain
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: idOrSub
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ObjectID or subdomain
 *     responses:
 *       200:
 *         description: Organization deleted
 *       404:
 *         description: Not found
 */
router.delete(
  '/:idOrSub',
  authenticate,
  (req, _res, next) => { req.orgId = req.params.idOrSub; next(); },
  rbac('org:delete'),
  organizationController.deleteOrganization.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/{idOrSubdomain}/add-user:
 *   patch:
 *     summary: Add a user to an organization (by org ObjectID or subdomain, RBAC-aware)
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: idOrSubdomain
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ObjectID or subdomain
 *         example: "663041cf7a14c7c000a3f999"  # Or: "acme"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [email]
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               role:
 *                 type: string
 *                 enum: [superadmin, orgadmin, orgmanager, orgmember, guest]
 *                 example: orgmember
 *     responses:
 *       200:
 *         description: User added to organization with role
 *       404:
 *         description: User or Organization not found
 *       500:
 *         description: Failed to add user
 */
/**
 * @swagger
 * /api/v1/organizations/{idOrSubdomain}/add-user:
 *   patch:
 *     summary: Add user to organization with RBAC role assignment
 *     tags: [Organizations]
 *     parameters:
 *       - in: path
 *         name: idOrSubdomain
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID or subdomain
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email of user to add
 *               roleId:
 *                 type: string
 *                 description: RBAC Role ObjectId (optional, defaults to 'member' role)
 *               roleName:
 *                 type: string
 *                 description: Role name if roleId not provided (optional, defaults to 'member')
 *     responses:
 *       200:
 *         description: User successfully added to organization
 *       400:
 *         description: Invalid input or validation errors
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization or user not found
 */
router.patch(
  '/:idOrSubdomain/add-user',
  authenticate,
  (req, _res, next) => { req.orgId = req.params.idOrSubdomain; next(); },
  rbac('user_object:assign'),
  organizationController.addUserToOrganization.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/{idOrSubdomain}/remove-user:
 *   patch:
 *     summary: Remove user from organization
 *     tags: [Organizations]
 *     parameters:
 *       - in: path
 *         name: idOrSubdomain
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID or subdomain
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email of user to remove
 *     responses:
 *       200:
 *         description: User successfully removed from organization
 *       400:
 *         description: Invalid input or validation errors
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization or user not found
 */
router.patch(
  '/:idOrSubdomain/remove-user',
  authenticate,
  (req, _res, next) => { req.orgId = req.params.idOrSubdomain; next(); },
  rbac('user_object:assign'),
  organizationController.removeUserFromOrganization.bind(organizationController)
);



/**
 * @swagger
 * /api/v1/organizations/{orgIdOrSubdomain}/details:
 *   get:
 *     summary: Get organization details with member information using flexible identifiers
 *     description: |
 *       Retrieve comprehensive organization details including member information and roles.
 *       Supports flexible identifier format for organization lookup.
 *
 *       **Supported identifier formats:**
 *       - MongoDB ObjectId: `663041cf7a14c7c000a3f999`
 *       - Subdomain: `acme-corp`
 *
 *       **Enhanced features:**
 *       - Complete member list with role information
 *       - RBAC role details for each member
 *       - Organization metadata and branding
 *       - Audit logging integration
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: orgIdOrSubdomain
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization MongoDB ObjectId or subdomain
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           subdomain:
 *             value: "acme-corp"
 *             summary: Organization subdomain
 *     responses:
 *       200:
 *         description: Organization details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: "663041cf7a14c7c000a3f999"
 *                     name:
 *                       type: string
 *                       example: "Acme Corporation"
 *                     subdomain:
 *                       type: string
 *                       example: "acme-corp"
 *                     branding:
 *                       type: object
 *                       properties:
 *                         logoUrl:
 *                           type: string
 *                           example: "https://acme.com/logo.png"
 *                         primaryColor:
 *                           type: string
 *                           example: "#336699"
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                     members:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           _id:
 *                             type: string
 *                           email:
 *                             type: string
 *                           name:
 *                             type: string
 *                           status:
 *                             type: string
 *                           role:
 *                             type: object
 *                             properties:
 *                               _id:
 *                                 type: string
 *                               name:
 *                                 type: string
 *                               displayName:
 *                                 type: string
 *                               hierarchy:
 *                                 type: object
 *                                 properties:
 *                                   level:
 *                                     type: integer
 *                           assignedAt:
 *                             type: string
 *                             format: date-time
 *                 metadata:
 *                   type: object
 *                   properties:
 *                     lookup_method:
 *                       type: string
 *                       enum: [objectId, subdomain]
 *                     member_count:
 *                       type: integer
 *                     retrieved_at:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Invalid organization identifier
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "INVALID_ORG_IDENTIFIER"
 *                     details:
 *                       type: string
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:orgIdOrSubdomain/details',
  authenticate,
  rbac('org_object:read'),
  organizationController.getOrganizationDetailsWithMembers.bind(organizationController)
);

// ===== NEW ENHANCED ORGANIZATION MANAGEMENT ROUTES =====

/**
 * @swagger
 * /api/v1/organizations/create:
 *   post:
 *     summary: Create a new organization (Any authenticated user)
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     description: |
 *       Creates a new organization with the requesting user as super_user.
 *       Organization status is set to 'pending' and requires God Super User approval.
 *       Only one super_user per organization is allowed.
 *
 *       **User Type Update:** When a user creates an organization and becomes a super_user,
 *       their user type is automatically updated from 'individual' (or null) to 'organization'
 *       to reflect their new role as an organization administrator.
 *
 *       **Required fields:** name, subdomain, contactEmail, industryTag, organizationRoles
 *       **Optional fields:** legalName, branding, teamInvitations
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name, subdomain, contactEmail, industryTag, organizationRoles]
 *             properties:
 *               name:
 *                 type: string
 *                 description: Organization display name
 *                 example: "Acme Corporation"
 *               legalName:
 *                 type: string
 *                 description: Legal name of the organization (optional)
 *                 example: "Acme Corporation LLC"
 *               subdomain:
 *                 type: string
 *                 description: Unique subdomain for the organization
 *                 pattern: '^[a-z0-9-]+$'
 *                 example: "acme-corp"
 *               contactEmail:
 *                 type: string
 *                 format: email
 *                 description: Primary contact email for the organization
 *                 example: "<EMAIL>"
 *               industryTag:
 *                 type: string
 *                 description: Industry or sector tag
 *                 example: "Technology"
 *               organizationRoles:
 *                 type: array
 *                 description: Organization capabilities (at least one required)
 *                 minItems: 1
 *                 items:
 *                   type: string
 *                   enum: [sponsor, organizer]
 *                 example: ["sponsor", "organizer"]
 *               branding:
 *                 type: object
 *                 description: Organization branding information (optional)
 *                 properties:
 *                   logoUrl:
 *                     type: string
 *                     format: uri
 *                     description: URL to organization logo
 *                     example: "https://acme.com/logo.png"
 *                   primaryColor:
 *                     type: string
 *                     description: Primary brand color (hex code)
 *                     example: "#336699"
 *               teamInvitations:
 *                 type: array
 *                 description: Team member invitations (optional)
 *                 items:
 *                   type: object
 *                   required: [email]
 *                   properties:
 *                     email:
 *                       type: string
 *                       format: email
 *                       description: Email address of the person to invite
 *                       example: "<EMAIL>"
 *                     role:
 *                       type: string
 *                       enum: [admin, member]
 *                       default: member
 *                       description: Role to assign to the invited member
 *                       example: "member"
 *     responses:
 *       201:
 *         description: Organization created successfully (pending approval)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Organization created successfully. Pending approval from system administrator."
 *                 organization:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     name:
 *                       type: string
 *                       example: "Acme Corporation"
 *                     subdomain:
 *                       type: string
 *                       example: "acme-corp"
 *                     status:
 *                       type: string
 *                       example: "pending"
 *                     createdByRole:
 *                       type: string
 *                       example: "super_user"
 *                 userUpdates:
 *                   type: object
 *                   description: Information about user changes made during organization creation
 *                   properties:
 *                     type:
 *                       type: string
 *                       example: "organization"
 *                       description: Updated user type
 *                     defaultOrganization:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                       description: ID of the default organization (if set)
 *                     roleAssigned:
 *                       type: string
 *                       example: "super_user"
 *                       description: Role assigned to the user in the organization
 *                     message:
 *                       type: string
 *                       example: "User type updated to organization"
 *                       description: Summary of user changes
 *       400:
 *         description: Invalid input (missing fields, invalid subdomain format)
 *       409:
 *         description: Subdomain already taken
 *       500:
 *         description: Server error
 */
router.post(
  '/create',
  rateLimitMiddleware.getMiddleware('organizationCreation'),
  authenticate,
  organizationController.createOrganization.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/{id}/approve:
 *   put:
 *     summary: Approve organization (God Super User only)
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     description: |
 *       Approves a pending organization, changing its status from 'pending' to 'active'.
 *       Only God Super Users can perform this operation.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Organization approved successfully
 *       400:
 *         description: Organization is not in pending status
 *       403:
 *         description: Insufficient privileges (God Super User required)
 *       404:
 *         description: Organization not found
 *       500:
 *         description: Server error
 */
router.put(
  '/:id/approve',
  authenticate,
  organizationController.approveOrganization.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/my-organizations:
 *   get:
 *     summary: Get current user's organizations with advanced filtering (Unified Endpoint)
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     description: |
 *       **Unified endpoint** that replaces both `/current` and `/my-organizations` functionality.
 *       Returns organizations the current user belongs to, with powerful filtering options.
 *
 *       **Use Cases:**
 *       - Get all user organizations: `/my-organizations`
 *       - Get current/primary organization: `/my-organizations?current=true`
 *       - Get specific organization: `/my-organizations?subdomain=acme-corp`
 *       - Filter by status: `/my-organizations?status=active`
 *       - Filter by role: `/my-organizations?role=super_user`
 *
 *       **Migration from `/current`:**
 *       - Old: `GET /organizations/current`
 *       - New: `GET /organizations/my-organizations?current=true`
 *     parameters:
 *       - in: query
 *         name: current
 *         schema:
 *           type: boolean
 *         description: Return only the current/primary organization
 *         example: true
 *       - in: query
 *         name: subdomain
 *         schema:
 *           type: string
 *         description: Filter by organization subdomain
 *         example: "acme-corp"
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, pending, inactive]
 *         description: Filter by organization status
 *         example: "active"
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [super_user, admin, member]
 *         description: "Filter by user's role in organization (organization member roles: super_user, admin, member)"
 *         example: "super_user"
 *       - in: query
 *         name: include_details
 *         schema:
 *           type: boolean
 *         description: Include additional details (member count, timestamps)
 *         example: true
 *     responses:
 *       200:
 *         description: Organizations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   oneOf:
 *                     - type: object
 *                       description: Single organization (when current=true)
 *                       properties:
 *                         _id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         subdomain:
 *                           type: string
 *                         userRole:
 *                           type: string
 *                         branding:
 *                           type: object
 *                     - type: object
 *                       description: Multiple organizations (default)
 *                       properties:
 *                         organizations:
 *                           type: array
 *                           items:
 *                             type: object
 *                         total:
 *                           type: integer
 *                 metadata:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                     filters_applied:
 *                       type: object
 *                     available_filters:
 *                       type: array
 *       404:
 *         description: User not found or no organizations match criteria
 *       500:
 *         description: Server error
 */
router.get(
  '/my-organizations',
  authenticate,
  organizationController.getMyOrganizations.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/{id}/members:
 *   put:
 *     summary: Manage organization members (Add/Remove/Update roles)
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     description: |
 *       Add, remove, or update member roles in an organization.
 *       Only organization super_user or admin can perform these operations.
 *       super_user role cannot be assigned or modified through this endpoint.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [action, userEmail]
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [add, remove, update_role]
 *                 description: Action to perform
 *               userEmail:
 *                 type: string
 *                 format: email
 *                 description: Email of the user to manage
 *               role:
 *                 type: string
 *                 enum: [admin, member]
 *                 description: Role to assign (required for add/update_role actions)
 *     responses:
 *       200:
 *         description: Member management operation successful
 *       400:
 *         description: Invalid input or business rule violation
 *       403:
 *         description: Insufficient permissions (super_user or admin required)
 *       404:
 *         description: Organization or user not found
 *       500:
 *         description: Server error
 */
router.put(
  '/:id/members',
  authenticate,
  organizationController.manageMembers.bind(organizationController)
);

/**
 * @swagger
 * /api/v1/organizations/validate-user-roles/{userId}:
 *   get:
 *     summary: Validate user role references (Debug/Admin endpoint)
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to validate role references for
 *     responses:
 *       200:
 *         description: Role validation results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     email:
 *                       type: string
 *                     totalRoles:
 *                       type: number
 *                     validRoles:
 *                       type: number
 *                     nullRoles:
 *                       type: number
 *                     invalidRoles:
 *                       type: number
 *                     roleDetails:
 *                       type: array
 *                       items:
 *                         type: object
 *                 summary:
 *                   type: object
 *                   properties:
 *                     hasIssues:
 *                       type: boolean
 *                     issueCount:
 *                       type: number
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/validate-user-roles/:userId', authenticate, organizationController.validateUserRoleReferences);

module.exports = router;
