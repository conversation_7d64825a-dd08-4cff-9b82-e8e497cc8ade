const mongoose = require('mongoose');

const permissionSchema = new mongoose.Schema({
  resource: { type: String, required: true }, // e.g., 'user_object', 'event'
  actions:  [{ type: String, required: true }] // e.g., ['create', 'read', 'delete']
}, { _id: false });

const roleSchema = new mongoose.Schema({
  org:         { type: mongoose.Schema.Types.ObjectId, ref: 'Organization', required: true },
  name:        { type: String, required: true }, // e.g., 'AcmeEditor', 'Manager'
  description: { type: String },
  permissions: [permissionSchema]
}, { timestamps: true });

module.exports = mongoose.model('Role', roleSchema);
