// middleware/rbac.js
const logger = require('../services/logger');
const { hasPermission } = require('../services/rbac-engine');

/**
 * Enhanced RBAC middleware with hierarchical privilege support
 * Supports God Super User bypass, Organization Super User, and structured error responses
 *
 * @param {string} permission - Permission in format "resource:action" or "resource_object:action"
 * @param {object} options - Additional options for RBAC checking
 * @returns {function} Express middleware function
 *
 * @example
 * // Basic usage
 * rbac('user_object:read')
 *
 * // With options
 * rbac('user_object:update', { allowSystemWide: true, requireOrgContext: false })
 */
function rbac(permission, options = {}) {
  return async (req, res, next) => {
    const startTime = Date.now();
    const correlationId = logger.generateCorrelationId();

    // Attach correlation ID to request for tracking
    req.rbacContext = {
      correlationId,
      permission,
      startTime
    };

    try {
      // 1. Validate user context
      if (!req.user) {
        return sendRbacError(res, {
          code: 'RBAC_MISSING_USER_CONTEXT',
          message: 'Authentication required for this operation',
          details: 'User must be authenticated to access this resource',
          statusCode: 401,
          correlationId,
          duration: Date.now() - startTime
        });
      }

      // 2. God Super User bypass - highest privilege level
      if (req.user?.isGodSuperUser?.()) {
        logger.info('God Super User bypass granted', {
          component: 'rbac-middleware',
          operation: 'god_super_user_check',
          correlation_id: correlationId,
          metadata: {
            user_id: req.user._id,
            permission_bypassed: permission,
            privilege_level: 'god_super_user',
            duration_ms: Date.now() - startTime
          }
        });

        req.rbacContext.bypass = 'god_super_user';
        return next();
      }

      // 3. Extract organization context (flexible sources)
      const orgId = extractOrgContext(req);

      // 4. Handle system-wide operations (no org context required)
      const { allowSystemWide = false, requireOrgContext = true } = options;

      // Skip org context requirement if explicitly disabled or system-wide allowed
      if (!orgId && requireOrgContext && !allowSystemWide) {
        logger.warn('RBAC validation failed: Missing organization context', {
          component: 'rbac-middleware',
          operation: 'orgid_validation',
          correlation_id: correlationId,
          metadata: {
            validation_type: 'org_context',
            failure_reason: 'missing_org_id',
            checked_sources: ['req.orgId', 'req.body.orgId', 'req.query.orgId', 'req.params.orgId'],
            method: req.method,
            url: req.originalUrl
          }
        });

        return sendRbacError(res, {
          code: 'RBAC_MISSING_ORG_CONTEXT',
          message: 'Organization context is required for this operation',
          details: 'Provide orgId in request body, query parameters, or URL path',
          statusCode: 400,
          correlationId,
          duration: Date.now() - startTime
        });
      }

      // 5. Organization Super User bypass (org-scoped)
      if (orgId && req.user?.isOrgSuperUser?.(orgId)) {
        logger.info('Organization Super User bypass granted', {
          component: 'rbac-middleware',
          operation: 'org_super_user_check',
          correlation_id: correlationId,
          metadata: {
            user_id: req.user._id,
            org_id: orgId,
            permission_bypassed: permission,
            privilege_level: 'org_super_user',
            duration_ms: Date.now() - startTime
          }
        });

        req.rbacContext.bypass = 'org_super_user';
        req.rbacContext.orgId = orgId;
        return next();
      }

      // 6. System privilege check (for system-level operations)
      if (req.user?.hasSystemPrivileges?.()) {
        const systemPrivileges = req.user.systemPrivileges || [];
        const hasRequiredSystemPrivilege = systemPrivileges.some(p =>
          ['system_admin', 'support_admin'].includes(p.level)
        );

        if (hasRequiredSystemPrivilege) {
          logger.info('System privilege bypass granted', {
            component: 'rbac-middleware',
            operation: 'system_privilege_check',
            correlation_id: correlationId,
            metadata: {
              user_id: req.user._id,
              permission_bypassed: permission,
              privilege_level: 'system_admin',
              duration_ms: Date.now() - startTime
            }
          });

          req.rbacContext.bypass = 'system_privilege';
          return next();
        }
      }

      // 7. Legacy superadmin bypass (backward compatibility)
      const isLegacySuperAdmin = checkLegacySuperAdmin(req.user, orgId);
      if (isLegacySuperAdmin) {
        logger.info('Legacy superadmin bypass granted', {
          component: 'rbac-middleware',
          operation: 'legacy_superadmin_check',
          correlation_id: correlationId,
          metadata: {
            user_id: req.user._id,
            org_id: orgId,
            permission_bypassed: permission,
            privilege_level: 'legacy_superadmin',
            duration_ms: Date.now() - startTime
          }
        });

        req.rbacContext.bypass = 'legacy_superadmin';
        return next();
      }

      // 8. Standard RBAC permission check
      const hasRequiredPermission = await checkStandardPermission(req.user, orgId, permission);

      if (!hasRequiredPermission) {
        logger.warn('RBAC permission denied', {
          component: 'rbac-middleware',
          operation: 'permission_check',
          correlation_id: correlationId,
          metadata: {
            user_id: req.user._id,
            org_id: orgId,
            permission_required: permission,
            denial_reason: 'insufficient_permissions',
            duration_ms: Date.now() - startTime
          }
        });

        return sendRbacError(res, {
          code: 'RBAC_PERMISSION_DENIED',
          message: 'Insufficient permissions for this operation',
          details: `Required permission: ${permission}`,
          statusCode: 403,
          correlationId,
          duration: Date.now() - startTime
        });
      }

      // 9. Permission granted - proceed
      logger.info('RBAC permission granted', {
        component: 'rbac-middleware',
        operation: 'permission_granted',
        correlation_id: correlationId,
        metadata: {
          user_id: req.user._id,
          org_id: orgId,
          permission_granted: permission,
          duration_ms: Date.now() - startTime
        }
      });

      req.rbacContext.granted = true;
      req.rbacContext.orgId = orgId;
      next();

    } catch (error) {
      logger.error('RBAC middleware error', error, {
        component: 'rbac-middleware',
        operation: 'error_handling',
        correlation_id: correlationId,
        metadata: {
          user_id: req.user?._id,
          permission: permission,
          error_type: error.constructor.name,
          duration_ms: Date.now() - startTime
        }
      });

      return sendRbacError(res, {
        code: 'RBAC_INTERNAL_ERROR',
        message: 'Internal error during permission check',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Contact system administrator',
        statusCode: 500,
        correlationId,
        duration: Date.now() - startTime
      });
    }
  };
}

/**
 * Extract organization context from various request sources
 * @param {object} req - Express request object
 * @returns {string|null} Organization ID or null if not found
 */
function extractOrgContext(req) {
  return req.orgId ||
         req.body?.orgId ||
         req.query?.orgId ||
         req.params?.orgId ||
         req.headers?.['x-org-id'] ||
         null;
}

/**
 * Check legacy superadmin patterns for backward compatibility
 * @param {object} user - User object
 * @param {string} orgId - Organization ID
 * @returns {boolean} True if user has legacy superadmin privileges
 */
function checkLegacySuperAdmin(user, orgId) {
  if (!user.roles) return false;

  return user.roles.some(r => {
    const roleName = r.role?.name || r.role;
    return ['superadmin', 'superuser', 'global_admin'].includes(roleName) &&
           (!orgId || !r.org || r.org.toString() === orgId?.toString() || r.org === 'global');
  }) || user.isSuperAdmin === true;
}

/**
 * Perform standard RBAC permission check
 * @param {object} user - User object
 * @param {string} orgId - Organization ID
 * @param {string} permission - Required permission
 * @returns {boolean} True if user has required permission
 */
async function checkStandardPermission(user, orgId, permission) {
  try {
    // Use the RBAC engine for permission checking
    return await hasPermission(user, orgId, permission);
  } catch (error) {
    logger.error('Standard permission check failed', error, {
      component: 'rbac-middleware',
      operation: 'standard_permission_check',
      metadata: {
        user_id: user._id,
        org_id: orgId,
        permission: permission
      }
    });
    return false;
  }
}

/**
 * Send structured RBAC error response
 * @param {object} res - Express response object
 * @param {object} errorInfo - Error information
 */
function sendRbacError(res, errorInfo) {
  const { code, message, details, statusCode, correlationId, duration } = errorInfo;

  logger.warn(`RBAC Error: ${code}`, {
    component: 'rbac-middleware',
    operation: 'error_handling',
    correlation_id: correlationId,
    metadata: {
      error_code: code,
      status_code: statusCode,
      duration_ms: duration,
      error_message: message
    }
  });

  res.status(statusCode).json({
    success: false,
    error: {
      code,
      message,
      details
    },
    metadata: {
      correlation_id: correlationId,
      timestamp: new Date().toISOString(),
      duration_ms: duration
    }
  });
}

module.exports = { rbac };
