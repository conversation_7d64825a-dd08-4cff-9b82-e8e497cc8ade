// models/User.js
const mongoose = require('mongoose');

// System privilege schema for hierarchical privileges
const systemPrivilegeSchema = new mongoose.Schema({
  level: {
    type: String,
    enum: ['god_super_user', 'system_admin', 'support_admin', 'audit_viewer'],
    required: true
  },
  grantedAt: { type: Date, default: Date.now },
  grantedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  security: {
    allowedIPs: [{ type: String, default: '*' }],
    requireMFA: { type: Boolean, default: null },
    sessionTimeout: { type: Number, default: 60 } // minutes
  },
  auditLog: [{
    action: String,
    timestamp: { type: Date, default: Date.now },
    ipAddress: String,
    userAgent: String,
    details: mongoose.Schema.Types.Mixed
  }]
}, { _id: false });

// Role assignment schema for organization-scoped roles
const roleAssignmentSchema = new mongoose.Schema({
  org: { type: mongoose.Schema.Types.ObjectId, ref: 'Organization', required: true },
  role: { type: mongoose.Schema.Types.ObjectId, ref: 'Role', required: true },
  assignedAt: { type: Date, default: Date.now },
  assignedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  permissions: {
    overrides: [{
      resource: String,
      actions: [String],
      granted: Boolean
    }],
    expiresAt: Date
  }
}, { _id: false });

// Enhanced user schema with hierarchical privileges
const userSchema = new mongoose.Schema({
  firebase_uid: { type: String }, // Made optional for Auth0 users
  auth0_sub: { type: String }, // Auth0 subject ID
  email: { type: String, required: true },
  name: { type: String, required: false },
  phone_number: { type: String, default: null },
  email_verified: { type: Boolean, default: false },
  auth_provider: { type: String, enum: ['firebase', 'auth0', 'custom'], default: 'custom' },

  // User type for organization management
  type: {
    type: String,
    enum: ['individual', 'organization'],
    default: null
  },

  // System-wide privileges (God Super User, System Admin, etc.)
  systemPrivileges: [systemPrivilegeSchema],

  // Organization-scoped roles
  roles: [roleAssignmentSchema],

  // Default organization for user context
  defaultOrganization: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    default: null,
    validate: {
      validator: async function(orgId) {
        if (!orgId) return true; // Allow null/empty values

        // Check if organization exists
        const Organization = mongoose.model('Organization');
        const org = await Organization.findById(orgId);
        if (!org) return false;

        // Check if user is a member of this organization
        return this.roles.some(role => role.org.toString() === orgId.toString());
      },
      message: 'User must be a member of the default organization'
    }
  },

  // User profile and settings
  profile: {
    avatar: String,
    fullName: String, // Full display name (can be different from 'name')
    jobTitle: String,
    companyName: String,
    bio: {
      type: String,
      maxlength: [200, 'Bio cannot exceed 200 characters'],
      trim: true
    },
    industryTags: [{
      type: String,
      trim: true
    }],
    networkingGoal: {
      type: String,
      trim: true
    },
    delegateEmail: {
      type: String,
      trim: true,
      lowercase: true,
      validate: {
        validator: function(email) {
          if (!email) return true; // Optional field
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        },
        message: 'Invalid delegate email format'
      }
    },
    timezone: { type: String, default: 'UTC' },
    language: { type: String, default: 'en' },
    preferences: mongoose.Schema.Types.Mixed
  },

  // Security and audit
  security: {
    lastLogin: Date,
    loginAttempts: { type: Number, default: 0 },
    lockedUntil: Date,
    passwordChangedAt: Date,
    mfaEnabled: { type: Boolean, default: false }
  },

  status: {
    type: String,
    enum: ['active', 'inactive', 'banned', 'blocked'],
    default: 'active'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for organizations (for backward compatibility)
userSchema.virtual('organizations', {
  ref: 'Organization',
  localField: 'roles.org',
  foreignField: '_id'
});

// Instance methods for privilege checking
userSchema.methods.isGodSuperUser = function() {
  return this.systemPrivileges?.some(p => p.level === 'god_super_user') || false;
};

userSchema.methods.hasSystemPrivileges = function() {
  return this.systemPrivileges?.length > 0 || false;
};

userSchema.methods.isOrgSuperUser = function(orgId) {
  if (!orgId || !this.roles) return false;
  return this.roles.some(r =>
    r.org.toString() === orgId.toString() &&
    r.role?.name === 'org_super_user'
  );
};

userSchema.methods.getRoleInOrganization = function(orgId) {
  if (!orgId || !this.roles) return null;
  return this.roles.find(r => r.org.toString() === orgId.toString());
};

userSchema.methods.getHighestPrivilegeLevel = function() {
  if (this.isGodSuperUser()) return 0;
  if (this.hasSystemPrivileges()) return 1;

  // Find highest role level in organizations
  if (this.roles?.length > 0) {
    const roleLevels = this.roles
      .filter(r => r.role?.hierarchy?.level)
      .map(r => r.role.hierarchy.level);
    return roleLevels.length > 0 ? Math.min(...roleLevels) : 10;
  }

  return 10; // Lowest privilege level
};

userSchema.methods.getOrganizationsWithRoles = async function() {
  const Organization = require('./Organization');

  const organizations = await Organization.find({
    'members.user': this._id,
    'members.status': 'active'
  });

  return organizations.map(org => {
    const member = org.members.find(m => m.user.toString() === this._id.toString());
    return {
      organization: {
        _id: org._id,
        name: org.name,
        subdomain: org.subdomain,
        status: org.status,
        branding: org.branding
      },
      role: member ? member.role : null,
      joinedAt: member ? member.joinedAt : null,
      status: member ? member.status : null
    };
  }).filter(item => item.role !== null); // Filter out null roles
};

userSchema.methods.isOrgSuperUserNew = function(orgId) {
  const Organization = require('./Organization');
  return Organization.findOne({
    _id: orgId,
    'members.user': this._id,
    'members.role': 'super_user',
    'members.status': 'active'
  });
};

userSchema.methods.canCreateOrganization = function() {
  // Any authenticated user can create an organization
  return true;
};

// Default organization management methods
userSchema.methods.setDefaultOrganization = async function(orgId, skipMembershipCheck = false) {
  if (!orgId) {
    this.defaultOrganization = null;
    return await this.save();
  }

  // Skip membership check for organization creation scenarios
  if (!skipMembershipCheck) {
    // Validate that user is a member of the organization
    const isMember = this.roles.some(role => role.org.toString() === orgId.toString());
    if (!isMember) {
      throw new Error('User must be a member of the organization to set it as default');
    }
  }

  this.defaultOrganization = orgId;
  return await this.save();
};

userSchema.methods.getDefaultOrganization = async function() {
  if (!this.defaultOrganization) return null;

  const Organization = require('./Organization');
  return await Organization.findById(this.defaultOrganization);
};

userSchema.methods.autoAssignDefaultOrganization = async function() {
  // If user already has a default organization, don't change it
  if (this.defaultOrganization) return this;

  // Find the first organization the user belongs to
  if (this.roles && this.roles.length > 0) {
    const firstOrgId = this.roles[0].org;
    this.defaultOrganization = firstOrgId;
    return await this.save();
  }

  return this;
};

userSchema.methods.reassignDefaultOrganization = async function(removedOrgId) {
  // If the removed organization was not the default, no action needed
  if (!this.defaultOrganization || this.defaultOrganization.toString() !== removedOrgId.toString()) {
    return this;
  }

  // Find another organization to set as default
  const remainingOrgs = this.roles.filter(role => role.org.toString() !== removedOrgId.toString());

  if (remainingOrgs.length > 0) {
    this.defaultOrganization = remainingOrgs[0].org;
  } else {
    this.defaultOrganization = null;
  }

  return await this.save();
};

// Static method to check if any God Super User exists in the system
userSchema.statics.hasGodSuperUser = async function() {
  const count = await this.countDocuments({
    'systemPrivileges.level': 'god_super_user'
  });
  return count > 0;
};

// Static method to get current God Super User
userSchema.statics.getCurrentGodSuperUser = async function() {
  return await this.findOne({
    'systemPrivileges.level': 'god_super_user'
  }).populate('roles.org', 'name subdomain')
    .populate('roles.role', 'name description');
};

// Static method to ensure only one God Super User exists
userSchema.statics.ensureSingleGodSuperUser = async function(newGodUserId) {
  // Remove God Super User privileges from all other users
  await this.updateMany(
    { _id: { $ne: newGodUserId } },
    { $pull: { systemPrivileges: { level: 'god_super_user' } } }
  );
};

// Static method for atomic God Super User privilege transfer
userSchema.statics.transferGodSuperUserPrivileges = async function(fromUserId, toUserId, transferredBy, securitySettings = {}) {
  const session = await this.db.startSession();

  try {
    await session.withTransaction(async () => {
      // 1. Remove God Super User privileges from previous user
      if (fromUserId) {
        const previousGodUser = await this.findById(fromUserId).session(session);
        if (previousGodUser) {
          // Remove God Super User privilege
          previousGodUser.systemPrivileges = previousGodUser.systemPrivileges?.filter(
            p => p.level !== 'god_super_user'
          ) || [];

          // Add audit log entry for privilege removal
          previousGodUser.systemPrivileges.push({
            level: 'audit_viewer',
            grantedAt: new Date(),
            grantedBy: transferredBy,
            security: {
              allowedIPs: ['*'],
              requireMFA: false,
              sessionTimeout: 120
            },
            auditLog: [{
              action: 'privilege_demoted_from_god_super_user',
              timestamp: new Date(),
              details: {
                transferred_to: toUserId,
                transferred_by: transferredBy,
                reason: 'god_super_user_privilege_transfer'
              }
            }]
          });

          await previousGodUser.save({ session });
        }
      }

      // 2. Assign God Super User privileges to new user
      const newGodUser = await this.findById(toUserId).session(session);
      if (!newGodUser) {
        throw new Error('Target user not found');
      }

      // Remove any existing God Super User privileges and add new one
      newGodUser.systemPrivileges = newGodUser.systemPrivileges?.filter(
        p => p.level !== 'god_super_user'
      ) || [];

      newGodUser.systemPrivileges.push({
        level: 'god_super_user',
        grantedAt: new Date(),
        grantedBy: transferredBy,
        security: {
          allowedIPs: securitySettings.allowedIPs || ['*'],
          requireMFA: securitySettings.requireMFA !== false,
          sessionTimeout: securitySettings.sessionTimeout || 60
        },
        auditLog: [{
          action: fromUserId ? 'privilege_transferred' : 'privilege_bootstrap_assigned',
          timestamp: new Date(),
          details: {
            transferred_from: fromUserId,
            transferred_by: transferredBy,
            is_bootstrap: !fromUserId,
            security_settings: securitySettings
          }
        }]
      });

      await newGodUser.save({ session });
    });

    return { success: true };
  } finally {
    await session.endSession();
  }
};

// Indexes for performance
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ firebase_uid: 1 }, { unique: true, sparse: true });
userSchema.index({ auth0_sub: 1 }, { unique: true, sparse: true });
userSchema.index({ auth_provider: 1 });
userSchema.index({ 'systemPrivileges.level': 1 });
userSchema.index({ 'roles.org': 1 });
userSchema.index({ defaultOrganization: 1 });
userSchema.index({ status: 1 });

module.exports = mongoose.models.User || mongoose.model('User', userSchema);
