// src/utils/usernameGenerator.js
const User = require('../models/User');
const logger = require('../services/logger');

/**
 * Username Generation Utility
 * 
 * Generates unique usernames based on user's full name with the following features:
 * - URL-safe format (lowercase letters, numbers, hyphens, underscores only)
 * - Unique across all users
 * - Maximum 50 characters
 * - Must start with a letter
 * - Handles edge cases (empty names, special characters, very long names)
 */

class UsernameGenerator {
  
  /**
   * Generate a unique username based on full name
   * @param {string} fullName - User's full name
   * @param {string} fallbackName - Fallback name if fullName is empty
   * @returns {Promise<string>} - Generated unique username
   */
  static async generateUsername(fullName, fallbackName = 'user') {
    try {
      // Step 1: Process the name
      const processedName = this.processName(fullName || fallbackName);
      
      // Step 2: Generate base username
      const baseUsername = this.createBaseUsername(processedName);
      
      // Step 3: Ensure uniqueness
      const uniqueUsername = await this.ensureUniqueness(baseUsername);
      
      logger.info('Username generated successfully', {
        component: 'username-generator',
        operation: 'generate_username',
        metadata: {
          original_name: fullName,
          processed_name: processedName,
          base_username: baseUsername,
          final_username: uniqueUsername
        }
      });
      
      return uniqueUsername;
      
    } catch (error) {
      logger.error('Username generation failed', error, {
        component: 'username-generator',
        operation: 'generate_username',
        metadata: {
          full_name: fullName,
          fallback_name: fallbackName
        }
      });
      
      // Fallback to a simple unique username
      return await this.generateFallbackUsername();
    }
  }
  
  /**
   * Process name to be URL-safe
   * @param {string} name - Input name
   * @returns {string} - Processed name
   */
  static processName(name) {
    if (!name || typeof name !== 'string') {
      return 'user';
    }
    
    return name
      .trim()
      .toLowerCase()
      // Remove accents and diacritics
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      // Replace spaces and special characters with hyphens
      .replace(/[^a-z0-9]+/g, '-')
      // Remove leading/trailing hyphens
      .replace(/^-+|-+$/g, '')
      // Collapse multiple hyphens
      .replace(/-+/g, '-')
      // Ensure it starts with a letter
      .replace(/^[^a-z]+/, '')
      // Limit length (leave room for unique suffix)
      .substring(0, 35);
  }
  
  /**
   * Create base username from processed name
   * @param {string} processedName - Processed name
   * @returns {string} - Base username
   */
  static createBaseUsername(processedName) {
    // If processed name is empty or invalid, use fallback
    if (!processedName || processedName.length === 0) {
      return 'user';
    }
    
    // Ensure it starts with a letter
    if (!/^[a-z]/.test(processedName)) {
      return `user-${processedName}`;
    }
    
    return processedName;
  }
  
  /**
   * Ensure username uniqueness by adding suffix
   * @param {string} baseUsername - Base username
   * @returns {Promise<string>} - Unique username
   */
  static async ensureUniqueness(baseUsername) {
    // First, try the base username without suffix
    if (await this.isUsernameAvailable(baseUsername)) {
      return baseUsername;
    }
    
    // Try with random alphanumeric suffix (preferred approach)
    for (let attempt = 0; attempt < 10; attempt++) {
      const suffix = this.generateRandomSuffix();
      const candidateUsername = `${baseUsername}-${suffix}`;
      
      // Ensure total length doesn't exceed 50 characters
      const finalUsername = candidateUsername.length > 50 
        ? candidateUsername.substring(0, 50)
        : candidateUsername;
      
      if (await this.isUsernameAvailable(finalUsername)) {
        return finalUsername;
      }
    }
    
    // Fallback to sequential numbers if random approach fails
    return await this.generateSequentialUsername(baseUsername);
  }
  
  /**
   * Generate random alphanumeric suffix
   * @returns {string} - Random suffix (4 characters)
   */
  static generateRandomSuffix() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 4; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  /**
   * Generate username with sequential number suffix
   * @param {string} baseUsername - Base username
   * @returns {Promise<string>} - Username with sequential suffix
   */
  static async generateSequentialUsername(baseUsername) {
    // Find existing usernames with this base
    const existingUsernames = await User.find({
      user_name: new RegExp(`^${baseUsername}(-\\d+)?$`, 'i')
    }).select('user_name').lean();
    
    const existingNumbers = existingUsernames
      .map(user => {
        const match = user.user_name.match(/-(\d+)$/);
        return match ? parseInt(match[1]) : 0;
      })
      .filter(num => !isNaN(num));
    
    // Find the next available number
    let nextNumber = 1;
    while (existingNumbers.includes(nextNumber)) {
      nextNumber++;
    }
    
    const sequentialUsername = `${baseUsername}-${nextNumber.toString().padStart(3, '0')}`;
    
    // Ensure total length doesn't exceed 50 characters
    return sequentialUsername.length > 50 
      ? sequentialUsername.substring(0, 50)
      : sequentialUsername;
  }
  
  /**
   * Check if username is available
   * @param {string} username - Username to check
   * @returns {Promise<boolean>} - True if available
   */
  static async isUsernameAvailable(username) {
    const existingUser = await User.findOne({ user_name: username }).lean();
    return !existingUser;
  }
  
  /**
   * Generate fallback username when all else fails
   * @returns {Promise<string>} - Fallback username
   */
  static async generateFallbackUsername() {
    const timestamp = Date.now().toString().slice(-8);
    const randomSuffix = this.generateRandomSuffix();
    return `user-${timestamp}-${randomSuffix}`;
  }
  
  /**
   * Validate username format
   * @param {string} username - Username to validate
   * @returns {boolean} - True if valid
   */
  static validateUsername(username) {
    if (!username || typeof username !== 'string') {
      return false;
    }
    
    // Check length
    if (username.length < 1 || username.length > 50) {
      return false;
    }
    
    // Check format: must start with letter, contain only lowercase letters, numbers, hyphens, underscores
    const usernameRegex = /^[a-z][a-z0-9_-]*$/;
    return usernameRegex.test(username);
  }
}

module.exports = UsernameGenerator;
