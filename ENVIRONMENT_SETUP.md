# 🌍 Environment Configuration Guide

This project uses separate environment configurations for **local development** and **production** deployment.

## 📁 Environment Files

- **`.env.local`** - Local development configuration
- **`.env.production`** - Production deployment configuration  
- **`.env`** - Active environment file (auto-generated, don't edit directly)

## 🚀 Quick Start

### For Local Development:
```bash
# Method 1: Using npm scripts (Recommended)
npm run start:local    # Switch to local env and start server
npm run dev:local      # Switch to local env and start with nodemon

# Method 2: Manual switching
npm run env:local      # Switch to local environment
npm start              # Start server

# Method 3: Using scripts (Linux/Mac)
./scripts/use-local-env.sh

# Method 4: Using scripts (Windows)
scripts\use-local-env.bat
```

### For Production:
```bash
# Switch to production environment
npm run env:prod
npm start

# Or use scripts
./scripts/use-prod-env.sh    # Linux/Mac
scripts\use-prod-env.bat     # Windows
```

## 🔧 Environment Differences

### Local Development (`.env.local`)
- **Server**: `http://localhost:3000`
- **Database**: `dev_db` (separate development database)
- **CORS**: Permissive for localhost
- **HTTPS**: Disabled
- **Subdomain routing**: Disabled
- **Cookies**: Non-secure
- **Audit logs**: More frequent flushing for debugging

### Production (`.env.production`)
- **Server**: `https://digimeet.live`
- **Database**: Production database
- **CORS**: Restricted to `digimeet.live` domains
- **HTTPS**: Enabled and enforced
- **Subdomain routing**: Enabled
- **Cookies**: Secure, domain-scoped
- **Audit logs**: Optimized batch processing

## 📋 Available NPM Scripts

```bash
# Environment switching
npm run env:local      # Switch to local environment
npm run env:prod       # Switch to production environment

# Start with specific environment
npm run start:local    # Local development server
npm run start:prod     # Production server
npm run dev:local      # Local development with nodemon

# Standard scripts
npm start              # Start with current .env
npm run dev            # Development with nodemon
```

## 🔒 Security Notes

1. **Never commit `.env` files** - They're in `.gitignore`
2. **Keep secrets secure** - Use environment variables in production
3. **Rotate JWT secrets** - Use different secrets for prod/dev
4. **Database separation** - Use separate databases for dev/prod

## 🛠️ Customization

### Adding New Environment Variables:
1. Add to both `.env.local` and `.env.production`
2. Document the purpose and format
3. Update this README if needed

### Creating New Environments:
1. Create `.env.staging` or `.env.test`
2. Add corresponding npm scripts
3. Create switching scripts if needed

## 🐛 Troubleshooting

### Environment Not Switching:
```bash
# Check current environment
cat .env | head -5

# Force switch
rm .env
npm run env:local  # or env:prod
```

### CORS Issues:
- **Local**: Check `CORS_ORIGINS` includes `http://localhost:3000`
- **Production**: Verify domain is in production CORS list

### Database Connection Issues:
- **Local**: Ensure development database exists
- **Production**: Check MongoDB Atlas connection string

## 📚 Best Practices

1. **Always use environment switching** before starting development
2. **Test locally** before deploying to production
3. **Keep environment files in sync** for new variables
4. **Use descriptive comments** in environment files
5. **Backup production environment** settings securely

---

**Current Environment**: Check with `npm run env:check` or look at the first few lines of `.env`
