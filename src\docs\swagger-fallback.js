// Minimal swagger fallback for deployment
const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'Auth API',
    version: '1.0.0',
    description: 'Authentication API'
  },
  servers: [
    {
      url: process.env.SERVER_URL || 'http://localhost:3000',
      description: 'API Server'
    }
  ],
  paths: {
    '/health': {
      get: {
        summary: 'Health Check',
        responses: {
          '200': {
            description: 'Service is healthy'
          }
        }
      }
    }
  }
};

// Mock swagger UI
const swaggerUi = {
  serve: (req, res, next) => next(),
  setup: () => (req, res) => {
    res.json({
      message: 'Swagger UI not available in this deployment',
      spec: swaggerSpec
    });
  }
};

module.exports = { swaggerSpec, swaggerUi };
