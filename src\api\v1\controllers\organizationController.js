//src/api/v1/controllers/organizationController.js
const Organization = require('../../../models/Organization');
const User = require('../../../models/User');
const RBACRole = require('../../../models/RBACRole');
const rbacManager = require('../../../services/rbac-manager');
const logger = require('../../../services/logger');
const auditLogger = require('../../../services/auditLogger');
const mongoose = require('mongoose');

/**
 * Organization Controller
 * Handles all organization-related business logic including multi-tenant support
 */
class OrganizationController {

  /**
   * Validate and ensure RBAC role exists for organization
   * @param {string} roleName - Name of the role to validate
   * @param {string} organizationId - Organization ID
   * @param {string} userId - User ID for audit logging
   * @returns {Object|null} - Role object or null if failed
   */
  async validateAndEnsureRole(roleName, organizationId, userId) {
    try {
      // First, try to find existing role
      let role = await RBACRole.findOne({
        name: roleName,
        scope: 'organization',
        organization: organizationId
      });

      if (role && role._id) {
        logger.debug('Found existing role', {
          component: 'organization-controller',
          operation: 'validate_role',
          metadata: {
            role_name: roleName,
            role_id: role._id,
            organization_id: organizationId
          }
        });
        return role;
      }

      // If role doesn't exist, create it
      const systemRoles = RBACRole.getSystemRoles();
      const roleTemplate = systemRoles.find(r => r.name === roleName);

      if (!roleTemplate) {
        logger.error('Role template not found', {
          component: 'organization-controller',
          operation: 'validate_role',
          metadata: {
            role_name: roleName,
            organization_id: organizationId
          }
        });
        return null;
      }

      role = await RBACRole.create({
        ...roleTemplate,
        organization: organizationId,
        audit: {
          createdBy: userId,
          createdAt: new Date(),
          usageCount: 0,
          changeHistory: [{
            action: 'created',
            timestamp: new Date(),
            performedBy: userId,
            reason: `Auto-created during organization setup`
          }]
        }
      });

      if (!role || !role._id) {
        logger.error('Failed to create role - no ID returned', {
          component: 'organization-controller',
          operation: 'validate_role',
          metadata: {
            role_name: roleName,
            organization_id: organizationId,
            created_role: role
          }
        });
        return null;
      }

      logger.info('Created new role for organization', {
        component: 'organization-controller',
        operation: 'validate_role',
        metadata: {
          role_name: roleName,
          role_id: role._id,
          organization_id: organizationId
        }
      });

      return role;
    } catch (error) {
      logger.error('Error in validateAndEnsureRole', error, {
        component: 'organization-controller',
        operation: 'validate_role',
        metadata: {
          role_name: roleName,
          organization_id: organizationId,
          error_message: error.message
        }
      });
      return null;
    }
  }

  /**
   * Helper function to check if string is ObjectId
   * @param {string} id - String to check
   * @returns {boolean} True if valid ObjectId
   */
  isObjectId(id) {
    return mongoose.Types.ObjectId.isValid(id);
  }

  /**
   * Create a new organization (Enhanced with new requirements)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async createOrganization(req, res) {
    const {
      name,
      legalName,
      subdomain,
      contactEmail,
      industryTag,
      organizationRoles,
      branding,
      teamInvitations
    } = req.body;

    // Validate required fields
    if (!name || !subdomain || !contactEmail || !industryTag || !organizationRoles || organizationRoles.length === 0) {
      return res.status(400).json({
        message: 'Name, subdomain, contact email, industry tag, and at least one organization role are required',
        missingFields: {
          name: !name,
          subdomain: !subdomain,
          contactEmail: !contactEmail,
          industryTag: !industryTag,
          organizationRoles: !organizationRoles || organizationRoles.length === 0
        }
      });
    }

    // Validate contact email format
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contactEmail)) {
      return res.status(400).json({
        message: 'Invalid contact email format'
      });
    }

    // Validate organization roles
    const validRoles = ['sponsor', 'organizer'];
    const invalidRoles = organizationRoles.filter(role => !validRoles.includes(role));
    if (invalidRoles.length > 0) {
      return res.status(400).json({
        message: 'Invalid organization roles',
        invalidRoles,
        validRoles
      });
    }

    // Validate subdomain format
    if (!/^[a-z0-9-]+$/.test(subdomain.toLowerCase())) {
      return res.status(400).json({
        message: 'Subdomain can only contain lowercase letters, numbers, and hyphens'
      });
    }

    try {
      const exists = await Organization.findOne({ subdomain: subdomain.toLowerCase() });
      if (exists) {
        return res.status(409).json({ message: 'Subdomain already taken' });
      }

      // Process team invitations if provided
      const processedInvitations = [];
      if (teamInvitations && Array.isArray(teamInvitations)) {
        for (const invitation of teamInvitations) {
          if (invitation.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(invitation.email)) {
            processedInvitations.push({
              email: invitation.email.toLowerCase(),
              role: invitation.role || 'member',
              invitedBy: req.user._id,
              status: 'pending',
              token: require('crypto').randomBytes(32).toString('hex')
            });
          }
        }
      }

      // Create organization with new structure
      const organization = new Organization({
        name,
        legalName: legalName || null,
        subdomain: subdomain.toLowerCase(),
        contactEmail: contactEmail.toLowerCase(),
        industryTag,
        organizationRoles,
        branding: branding || {},
        teamInvitations: processedInvitations,
        createdBy: req.user._id,
        createdByRole: 'super_user',
        status: 'pending', // Default to pending
        members: [{
          user: req.user._id,
          role: 'super_user',
          joinedAt: new Date(),
          addedBy: req.user._id,
          status: 'active'
        }]
      });

      await organization.save();

      // Initialize system roles for the organization
      const roleInitResult = await rbacManager.initializeSystemRoles(organization._id, req.user._id);
      if (!roleInitResult.success) {
        logger.warn('Failed to initialize system roles for organization', {
          component: 'organization-controller',
          operation: 'create_organization',
          metadata: {
            organization_id: organization._id,
            error: roleInitResult.error
          }
        });
      }

      // Use the enhanced role validation and creation helper
      const superUserRole = await this.validateAndEnsureRole('org_super_user', organization._id, req.user._id);

      if (!superUserRole) {
        logger.error('Critical: Could not find or create org_super_user role', {
          component: 'organization-controller',
          operation: 'create_organization',
          metadata: {
            organization_id: organization._id,
            organization_name: organization.name
          }
        });
        return res.status(500).json({
          message: 'Failed to create organization roles',
          error: 'Could not find or create org_super_user role'
        });
      }

      // Validate that superUserRole has a valid _id
      if (!superUserRole._id) {
        logger.error('Critical: superUserRole missing _id', {
          component: 'organization-controller',
          operation: 'create_organization',
          metadata: {
            organization_id: organization._id,
            superUserRole: superUserRole
          }
        });
        return res.status(500).json({
          message: 'Failed to create organization roles',
          error: 'Invalid role reference - missing role ID'
        });
      }

      // Add the role to the user
      const currentUser = await User.findById(req.user._id);
      if (currentUser) {
        // Check if user already has a role in this organization
        const existingRole = currentUser.roles.find(r =>
          r.org && r.org.toString() === organization._id.toString()
        );

        if (!existingRole) {
          // Validate role assignment before saving
          const roleToAssign = {
            org: organization._id,
            role: superUserRole._id,
            assignedAt: new Date(),
            assignedBy: req.user._id
          };

          // Double-check that all required fields are present
          if (!roleToAssign.org || !roleToAssign.role) {
            logger.error('Critical: Invalid role assignment data', {
              component: 'organization-controller',
              operation: 'create_organization',
              metadata: {
                user_id: req.user._id,
                organization_id: organization._id,
                roleToAssign: roleToAssign,
                superUserRole_id: superUserRole._id
              }
            });
            return res.status(500).json({
              message: 'Failed to assign user role',
              error: 'Invalid role assignment data'
            });
          }

          currentUser.roles.push(roleToAssign);
          await currentUser.save();

          // Verify the role was saved correctly
          const savedUser = await User.findById(req.user._id);
          const savedRole = savedUser.roles.find(r =>
            r.org && r.org.toString() === organization._id.toString()
          );

          if (!savedRole || !savedRole.role) {
            logger.error('Critical: Role assignment verification failed', {
              component: 'organization-controller',
              operation: 'create_organization',
              metadata: {
                user_id: req.user._id,
                organization_id: organization._id,
                savedRole: savedRole,
                expectedRoleId: superUserRole._id
              }
            });
            return res.status(500).json({
              message: 'Failed to verify role assignment',
              error: 'Role assignment verification failed'
            });
          }

          logger.info('User role assigned and verified successfully', {
            component: 'organization-controller',
            operation: 'create_organization',
            metadata: {
              user_id: req.user._id,
              organization_id: organization._id,
              role_id: savedRole.role,
              role_name: 'org_super_user'
            }
          });

          // Update user type to 'organization' when they become a super user
          const previousUserType = currentUser.type;
          if (currentUser.type !== 'organization') {
            currentUser.type = 'organization';
            await currentUser.save();

            logger.info('User type updated to organization', {
              component: 'organization-controller',
              operation: 'create_organization',
              metadata: {
                user_id: req.user._id,
                user_email: currentUser.email,
                previous_type: previousUserType || 'null',
                new_type: 'organization',
                organization_id: organization._id,
                organization_name: organization.name,
                role_assigned: 'super_user'
              }
            });

            // Audit log for user type change
            await auditLogger.logOperation({
              user: req.user,
              operationType: 'UPDATE',
              resourceType: 'user_type_change',
              resourceId: req.user._id,
              endpoint: req.originalUrl,
              method: req.method,
              requestData: {
                trigger: 'organization_creation',
                organization_id: organization._id
              },
              responseStatus: 200,
              organizationContext: {
                organization_id: organization._id,
                organization_name: organization.name,
                organization_subdomain: organization.subdomain
              },
              customMetadata: {
                previous_user_type: previousUserType || 'null',
                new_user_type: 'organization',
                role_assigned: 'super_user',
                reason: 'User became super_user of created organization'
              }
            });
          }

          // Auto-assign as default organization if user has no default (after role is saved)
          if (!currentUser.defaultOrganization) {
            await currentUser.setDefaultOrganization(organization._id); // Now user is a member, so no need to skip check
            logger.info('Auto-assigned default organization for user', {
              component: 'organization-controller',
              operation: 'create_organization',
              metadata: {
                user_id: req.user._id,
                organization_id: organization._id,
                organization_name: organization.name
              }
            });
          }
        }
      }

      // Log organization creation
      logger.info('Organization created', {
        component: 'organization-controller',
        operation: 'create_organization',
        metadata: {
          organization_id: organization._id,
          subdomain: organization.subdomain,
          created_by: req.user._id,
          status: organization.status,
          user_role_added: !!currentUser
        }
      });

      // Log audit entry
      await auditLogger.logOperation({
        userId: req.user._id,
        operation: 'CREATE',
        resourceType: 'organization',
        resourceId: organization._id,
        details: {
          name: organization.name,
          subdomain: organization.subdomain,
          status: organization.status
        },
        ipAddress: req.ip,
        userAgent: req.get('user-agent')
      });

      // Get updated user information for response
      const updatedUser = await User.findById(req.user._id).select('type defaultOrganization');

      res.status(201).json({
        message: 'Organization created successfully. Pending approval from system administrator.',
        organization: {
          _id: organization._id,
          name: organization.name,
          legalName: organization.legalName,
          subdomain: organization.subdomain,
          contactEmail: organization.contactEmail,
          industryTag: organization.industryTag,
          organizationRoles: organization.organizationRoles,
          status: organization.status,
          branding: organization.branding,
          createdBy: organization.createdBy,
          createdByRole: organization.createdByRole,
          createdAt: organization.createdAt,
          members: organization.members.map(member => ({
            user: member.user,
            role: member.role,
            joinedAt: member.joinedAt,
            status: member.status
          })),
          teamInvitations: organization.teamInvitations.map(invitation => ({
            email: invitation.email,
            role: invitation.role,
            status: invitation.status,
            invitedAt: invitation.invitedAt,
            expiresAt: invitation.expiresAt
          }))
        },
        userUpdates: {
          type: updatedUser.type,
          defaultOrganization: updatedUser.defaultOrganization,
          roleAssigned: 'super_user',
          message: updatedUser.type === 'organization' ? 'User type updated to organization' : 'User type unchanged'
        }
      });
    } catch (err) {
      logger.error('Failed to create organization', err, {
        component: 'organization-controller',
        operation: 'create_organization',
        metadata: { name, subdomain }
      });
      res.status(500).json({ message: 'Failed to create organization', error: err.message });
    }
  }

  /**
   * Approve organization (God Super User only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async approveOrganization(req, res) {
    const { id } = req.params;

    try {
      // Check if user is God Super User
      const user = await User.findOne({ firebase_uid: req.user.uid });
      if (!user || !user.isGodSuperUser()) {
        return res.status(403).json({
          message: 'Only God Super Users can approve organizations',
          error: {
            code: 'INSUFFICIENT_PRIVILEGES',
            details: 'This operation requires God Super User privileges'
          }
        });
      }

      const organization = await Organization.findById(id);
      if (!organization) {
        return res.status(404).json({ message: 'Organization not found' });
      }

      if (organization.status !== 'pending') {
        return res.status(400).json({
          message: `Organization is already ${organization.status}`,
          error: {
            code: 'INVALID_STATUS',
            details: `Cannot approve organization with status: ${organization.status}`
          }
        });
      }

      // Update organization status
      organization.status = 'active';
      organization.approvedBy = user._id;
      organization.approvedAt = new Date();

      await organization.save();

      // Log approval
      logger.info('Organization approved', {
        component: 'organization-controller',
        operation: 'approve_organization',
        metadata: {
          organization_id: organization._id,
          approved_by: user._id,
          approved_at: organization.approvedAt
        }
      });

      // Log audit entry
      await auditLogger.logOperation({
        userId: user._id,
        operation: 'UPDATE',
        resourceType: 'organization',
        resourceId: organization._id,
        details: {
          action: 'approve',
          previous_status: 'pending',
          new_status: 'active'
        },
        ipAddress: req.ip,
        userAgent: req.get('user-agent')
      });

      res.json({
        message: 'Organization approved successfully',
        organization: {
          _id: organization._id,
          name: organization.name,
          subdomain: organization.subdomain,
          status: organization.status,
          approvedBy: organization.approvedBy,
          approvedAt: organization.approvedAt
        }
      });
    } catch (err) {
      logger.error('Failed to approve organization', err, {
        component: 'organization-controller',
        operation: 'approve_organization',
        metadata: { organization_id: id }
      });
      res.status(500).json({ message: 'Failed to approve organization', error: err.message });
    }
  }

  /**
   * Get user's organizations with enhanced filtering options
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   *
   * Query Parameters:
   * - current=true: Return only the current/primary organization
   * - subdomain=<name>: Filter by specific subdomain
   * - status=<status>: Filter by organization status (active, pending, etc.)
   * - role=<role>: Filter by user's role in organization
   */
  async getMyOrganizations(req, res) {
    const startTime = Date.now();

    try {
      // Parse query parameters
      const {
        current,
        subdomain,
        status,
        role,
        include_details
      } = req.query;

      // Try multiple ways to find the user
      let user = await User.findOne({ firebase_uid: req.user.uid });

      if (!user && req.user._id) {
        user = await User.findById(req.user._id);
      }

      if (!user) {
        logger.warn('User not found in getMyOrganizations', {
          component: 'organization-controller',
          operation: 'get_my_organizations',
          metadata: {
            firebase_uid: req.user.uid,
            user_id: req.user._id,
            query_params: req.query
          }
        });
        return res.status(404).json({
          message: 'User not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: 'User account not found in database'
          }
        });
      }

      // Get all user organizations
      let organizations = await user.getOrganizationsWithRoles();

      // Apply filters
      if (subdomain) {
        organizations = organizations.filter(item =>
          item.organization.subdomain === subdomain.toLowerCase()
        );
      }

      if (status) {
        organizations = organizations.filter(item =>
          item.organization.status === status
        );
      }

      if (role) {
        organizations = organizations.filter(item =>
          item.role === role
        );
      }

      // Handle "current" parameter - return first/primary organization
      if (current === 'true') {
        if (organizations.length === 0) {
          // Get original organizations before filtering for debug info
          const originalOrganizations = await user.getOrganizationsWithRoles();

          return res.status(404).json({
            message: 'No current organization found',
            error: {
              code: 'NO_CURRENT_ORGANIZATION',
              details: 'User does not belong to any organizations or no organization matches the criteria'
            },
            debug: {
              user_id: user._id,
              user_email: user.email,
              total_user_organizations: originalOrganizations.length,
              filters_applied: { subdomain, status, role },
              organizations_before_filtering: originalOrganizations.map(org => ({
                name: org.organization.name,
                subdomain: org.organization.subdomain,
                status: org.organization.status,
                user_role: org.role
              }))
            }
          });
        }

        const currentOrg = organizations[0];

        // Enhanced details if requested
        let responseData = {
          _id: currentOrg.organization._id,
          name: currentOrg.organization.name,
          subdomain: currentOrg.organization.subdomain,
          status: currentOrg.organization.status,
          branding: currentOrg.organization.branding,
          userRole: currentOrg.role,
          joinedAt: currentOrg.joinedAt,
          userStatus: currentOrg.status
        };

        if (include_details === 'true') {
          // Get full organization with member count
          const fullOrg = await Organization.findById(currentOrg.organization._id);
          responseData.memberCount = fullOrg?.members?.length || 0;
          responseData.createdAt = fullOrg?.createdAt;
          responseData.updatedAt = fullOrg?.updatedAt;
        }

        logger.info('Current organization retrieved', {
          component: 'organization-controller',
          operation: 'get_my_organizations_current',
          metadata: {
            user_id: user._id,
            organization_id: currentOrg.organization._id,
            subdomain: currentOrg.organization.subdomain,
            duration_ms: Date.now() - startTime
          }
        });

        return res.json({
          success: true,
          data: responseData,
          metadata: {
            type: 'current_organization',
            retrieved_at: new Date().toISOString(),
            duration_ms: Date.now() - startTime,
            filters_applied: { current: true, subdomain, status, role }
          }
        });
      }

      // Return all organizations (filtered)
      logger.info('User organizations retrieved', {
        component: 'organization-controller',
        operation: 'get_my_organizations',
        metadata: {
          user_id: user._id,
          total_organizations: organizations.length,
          filters_applied: { subdomain, status, role },
          duration_ms: Date.now() - startTime
        }
      });

      // If no organizations found after filtering, provide debug info
      if (organizations.length === 0) {
        const originalOrganizations = await user.getOrganizationsWithRoles();

        return res.status(404).json({
          success: false,
          message: 'No organizations found matching the criteria',
          error: {
            code: 'NO_ORGANIZATIONS_MATCH_CRITERIA',
            details: 'User has organizations but none match the applied filters'
          },
          debug: {
            user_id: user._id,
            user_email: user.email,
            total_user_organizations: originalOrganizations.length,
            filters_applied: { subdomain, status, role },
            organizations_before_filtering: originalOrganizations.map(org => ({
              name: org.organization.name,
              subdomain: org.organization.subdomain,
              status: org.organization.status,
              user_role: org.role
            }))
          }
        });
      }

      res.json({
        success: true,
        data: {
          organizations: organizations,
          total: organizations.length
        },
        metadata: {
          type: 'user_organizations',
          retrieved_at: new Date().toISOString(),
          duration_ms: Date.now() - startTime,
          filters_applied: { subdomain, status, role },
          available_filters: ['current', 'subdomain', 'status', 'role', 'include_details']
        }
      });
    } catch (err) {
      logger.error('Failed to get user organizations', err, {
        component: 'organization-controller',
        operation: 'get_my_organizations',
        metadata: {
          firebase_uid: req.user.uid,
          user_id: req.user._id,
          query_params: req.query,
          error_message: err.message,
          duration_ms: Date.now() - startTime
        }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to get organizations',
        error: {
          code: 'ORGANIZATIONS_FETCH_ERROR',
          details: 'An error occurred while retrieving user organizations'
        }
      });
    }
  }

  /**
   * Manage organization members (Add/Remove/Update roles)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async manageMembers(req, res) {
    const { id } = req.params;
    const { action, userEmail, role } = req.body;

    if (!action || !['add', 'remove', 'update_role'].includes(action)) {
      return res.status(400).json({
        message: 'Valid action is required (add, remove, update_role)'
      });
    }

    if (!userEmail) {
      return res.status(400).json({ message: 'User email is required' });
    }

    if ((action === 'add' || action === 'update_role') && !role) {
      return res.status(400).json({ message: 'Role is required for add/update_role actions' });
    }

    if (role && !['admin', 'member'].includes(role)) {
      return res.status(400).json({
        message: 'Role must be either "admin" or "member" (super_user cannot be assigned)'
      });
    }

    try {
      // Find organization
      const organization = await Organization.findById(id);
      if (!organization) {
        return res.status(404).json({ message: 'Organization not found' });
      }

      // Check if current user has permission (must be super_user or admin)
      const currentUser = await User.findOne({ firebase_uid: req.user.uid });
      const currentUserRole = organization.getMemberRole(currentUser._id);

      if (!currentUserRole || !['org_super_user', 'org_admin'].includes(currentUserRole)) {
        return res.status(403).json({
          message: 'Only organization super_user or admin can manage members',
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            details: 'This operation requires super_user or admin role in the organization'
          }
        });
      }

      // Find target user
      const targetUser = await User.findOne({ email: userEmail.toLowerCase() });
      if (!targetUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      let result;
      switch (action) {
        case 'add':
          try {
            await organization.addMember(targetUser._id, role, currentUser._id);
            await organization.save();
            result = { action: 'added', user: targetUser.email, role };
          } catch (error) {
            return res.status(400).json({ message: error.message });
          }
          break;

        case 'remove':
          try {
            await organization.removeMember(targetUser._id);
            await organization.save();
            result = { action: 'removed', user: targetUser.email };
          } catch (error) {
            return res.status(400).json({ message: error.message });
          }
          break;

        case 'update_role':
          try {
            await organization.updateMemberRole(targetUser._id, role);
            await organization.save();
            result = { action: 'role_updated', user: targetUser.email, new_role: role };
          } catch (error) {
            return res.status(400).json({ message: error.message });
          }
          break;
      }

      // Log the action
      logger.info('Organization member management', {
        component: 'organization-controller',
        operation: 'manage_members',
        metadata: {
          organization_id: organization._id,
          action: action,
          target_user: targetUser._id,
          performed_by: currentUser._id,
          role: role
        }
      });

      // Log audit entry
      await auditLogger.logOperation({
        userId: currentUser._id,
        operation: 'UPDATE',
        resourceType: 'organization_member',
        resourceId: organization._id,
        details: {
          action: action,
          target_user_email: userEmail,
          role: role,
          organization_name: organization.name
        },
        ipAddress: req.ip,
        userAgent: req.get('user-agent')
      });

      res.json({
        message: `Member ${action} successful`,
        result: result,
        organization: {
          _id: organization._id,
          name: organization.name,
          members_count: organization.members.length
        }
      });
    } catch (err) {
      logger.error('Failed to manage organization members', err, {
        component: 'organization-controller',
        operation: 'manage_members',
        metadata: { organization_id: id, action, userEmail }
      });
      res.status(500).json({ message: 'Failed to manage members', error: err.message });
    }
  }

  /**
   * Get organization by ID or subdomain
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getOrganization(req, res) {
    const { idOrSub } = req.params;

    try {
      const query = this.isObjectId(idOrSub)
        ? { _id: idOrSub }
        : { subdomain: idOrSub.toLowerCase() };

      const organization = await Organization.findOne(query);

      if (!organization) {
        return res.status(404).json({ message: 'Organization not found' });
      }

      res.json({
        _id: organization._id,
        name: organization.name,
        subdomain: organization.subdomain,
        branding: organization.branding,
        members: organization.members,
        createdAt: organization.createdAt,
        updatedAt: organization.updatedAt
      });
    } catch (err) {
      logger.error('Failed to fetch organization', err, {
        component: 'organization-controller',
        operation: 'get_organization',
        metadata: { identifier: idOrSub }
      });
      res.status(500).json({ message: 'Failed to fetch organization', error: err.message });
    }
  }

  /**
   * Update organization by ID or subdomain
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateOrganization(req, res) {
    const { idOrSub } = req.params;
    const update = req.body;

    try {
      const query = this.isObjectId(idOrSub)
        ? { _id: idOrSub }
        : { subdomain: idOrSub.toLowerCase() };

      const organization = await Organization.findOneAndUpdate(query, update, { new: true });

      if (!organization) {
        return res.status(404).json({ message: 'Organization not found' });
      }

      logger.info('Organization updated', {
        component: 'organization-controller',
        operation: 'update_organization',
        metadata: {
          organization_id: organization._id,
          updated_by: req.user._id,
          updated_fields: Object.keys(update)
        }
      });

      res.json({ message: 'Organization updated', organization });
    } catch (err) {
      logger.error('Failed to update organization', err, {
        component: 'organization-controller',
        operation: 'update_organization',
        metadata: { identifier: idOrSub, updates: Object.keys(update) }
      });
      res.status(500).json({ message: 'Failed to update organization', error: err.message });
    }
  }

  /**
   * Delete organization by ID or subdomain
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteOrganization(req, res) {
    const { idOrSub } = req.params;

    try {
      const query = this.isObjectId(idOrSub)
        ? { _id: idOrSub }
        : { subdomain: idOrSub.toLowerCase() };

      const organization = await Organization.findOneAndDelete(query);

      if (!organization) {
        return res.status(404).json({ message: 'Organization not found' });
      }

      logger.info('Organization deleted', {
        component: 'organization-controller',
        operation: 'delete_organization',
        metadata: {
          organization_id: organization._id,
          subdomain: organization.subdomain,
          deleted_by: req.user._id
        }
      });

      res.json({ message: 'Organization deleted' });
    } catch (err) {
      logger.error('Failed to delete organization', err, {
        component: 'organization-controller',
        operation: 'delete_organization',
        metadata: { identifier: idOrSub }
      });
      res.status(500).json({ message: 'Failed to delete organization', error: err.message });
    }
  }

  /**
   * Get all organizations with pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getOrganizations(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      const [organizations, total] = await Promise.all([
        Organization.find({}, 'name subdomain branding createdAt')
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 }),
        Organization.countDocuments()
      ]);

      res.json({
        page,
        limit,
        total,
        organizations
      });
    } catch (err) {
      logger.error('Failed to fetch organizations', err, {
        component: 'organization-controller',
        operation: 'get_organizations'
      });
      res.status(500).json({ message: 'Failed to fetch organizations', error: err.message });
    }
  }

  /**
   * Get pending organizations (God Super User only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPendingOrganizations(req, res) {
    const startTime = Date.now();

    try {
      // Check if user is God Super User
      const user = await User.findOne({ firebase_uid: req.user.uid });
      if (!user || !user.isGodSuperUser()) {
        return res.status(403).json({
          success: false,
          message: 'Only God Super Users can access pending organizations',
          error: {
            code: 'INSUFFICIENT_PRIVILEGES',
            details: 'This operation requires God Super User privileges'
          }
        });
      }

      // Parse pagination parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Fetch pending organizations with pagination
      const [pendingOrganizations, total] = await Promise.all([
        Organization.find({ status: 'pending' })
          .populate('createdBy', 'name email _id')
          .select('name subdomain branding createdAt createdBy members')
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 })
          .lean(),
        Organization.countDocuments({ status: 'pending' })
      ]);

      // Enhance organizations with member count
      const enhancedOrganizations = pendingOrganizations.map(org => ({
        _id: org._id,
        name: org.name,
        subdomain: org.subdomain,
        branding: org.branding || {},
        createdAt: org.createdAt,
        createdBy: {
          _id: org.createdBy?._id,
          name: org.createdBy?.name,
          email: org.createdBy?.email
        },
        memberCount: org.members?.length || 0
      }));

      // Log the administrative operation
      logger.info('Pending organizations retrieved by God Super User', {
        component: 'organization-controller',
        operation: 'get_pending_organizations',
        metadata: {
          user_id: user._id,
          user_email: user.email,
          total_pending: total,
          page: page,
          limit: limit,
          duration_ms: Date.now() - startTime
        }
      });

      // Log audit entry for administrative access
      await auditLogger.logOperation({
        userId: user._id,
        operation: 'READ',
        resourceType: 'pending_organizations',
        resourceId: null,
        details: {
          action: 'list_pending_organizations',
          total_pending: total,
          page: page,
          limit: limit,
          privilege_level: 'god_super_user'
        },
        ipAddress: req.ip,
        userAgent: req.get('user-agent')
      });

      res.json({
        success: true,
        data: {
          organizations: enhancedOrganizations,
          pagination: {
            page: page,
            limit: limit,
            total: total,
            pages: Math.ceil(total / limit),
            hasNext: page < Math.ceil(total / limit),
            hasPrev: page > 1
          }
        },
        metadata: {
          retrieved_at: new Date().toISOString(),
          duration_ms: Date.now() - startTime,
          privilege_level: 'god_super_user'
        }
      });
    } catch (err) {
      logger.error('Failed to fetch pending organizations', err, {
        component: 'organization-controller',
        operation: 'get_pending_organizations',
        metadata: {
          user_id: req.user._id,
          duration_ms: Date.now() - startTime
        }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to fetch pending organizations',
        error: {
          code: 'PENDING_ORGS_FETCH_ERROR',
          details: 'An error occurred while retrieving pending organizations'
        }
      });
    }
  }





  /**
   * Get organization details with member information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getOrganizationDetails(req, res) {
    const { orgIdOrSubdomain } = req.params;

    try {
      const query = this.isObjectId(orgIdOrSubdomain)
        ? { _id: orgIdOrSubdomain }
        : { subdomain: orgIdOrSubdomain.toLowerCase() };

      const organization = await Organization.findOne(query);

      if (!organization) {
        return res.status(404).json({
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: `No organization found with identifier: ${orgIdOrSubdomain}`
          }
        });
      }

      // Enhanced member information with role details
      const enhancedMembers = organization.members?.map(member => ({
        user: member.user,
        role: member.role,
        joinedAt: member.joinedAt,
        status: member.status || 'active'
      })) || [];

      logger.info('Organization details retrieved', {
        component: 'organization-controller',
        operation: 'get_organization_details',
        metadata: {
          organization_id: organization._id,
          identifier: orgIdOrSubdomain,
          member_count: enhancedMembers.length,
          requested_by: req.user._id
        }
      });

      res.json({
        _id: organization._id,
        name: organization.name,
        subdomain: organization.subdomain,
        branding: organization.branding,
        members: enhancedMembers,
        memberCount: enhancedMembers.length,
        createdAt: organization.createdAt,
        updatedAt: organization.updatedAt,
        metadata: {
          retrieved_at: new Date().toISOString(),
          identifier_type: this.isObjectId(orgIdOrSubdomain) ? 'objectid' : 'subdomain'
        }
      });
    } catch (err) {
      logger.error('Failed to fetch organization details', err, {
        component: 'organization-controller',
        operation: 'get_organization_details',
        metadata: { identifier: orgIdOrSubdomain }
      });
      res.status(500).json({
        message: 'Failed to fetch organization details',
        error: {
          code: 'ORG_DETAILS_FETCH_ERROR',
          details: 'An error occurred while retrieving organization details'
        }
      });
    }
  }

  /**
   * Add user to organization with RBAC role assignment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async addUserToOrganization(req, res) {
    try {
      const { idOrSubdomain } = req.params;
      const { email, roleId, roleName, role } = req.body;

      // Validate input
      if (!email || !email.includes('@')) {
        return res.status(400).json({
          success: false,
          message: 'Valid email address is required',
          error: {
            code: 'INVALID_EMAIL',
            details: 'Email must be a valid email address'
          }
        });
      }

      // Map legacy role names to new RBAC role names
      const legacyRoleMapping = {
        'superadmin': 'org_super_user',
        'orgadmin': 'admin',
        'orgmanager': 'manager',
        'orgmember': 'member',
        'guest': 'guest'
      };

      // Determine the role to use (priority: roleId > roleName > role > default)
      let targetRoleName = 'member'; // default
      if (roleId) {
        // If roleId is provided, we'll find the role by ID later
        targetRoleName = null;
      } else if (roleName) {
        targetRoleName = legacyRoleMapping[roleName] || roleName;
      } else if (role) {
        targetRoleName = legacyRoleMapping[role] || role;
      }

      // Find organization by ID or subdomain
      const query = this.isObjectId(idOrSubdomain)
        ? { _id: idOrSubdomain }
        : { subdomain: idOrSubdomain.toLowerCase() };

      const org = await Organization.findOne(query);
      if (!org) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: `Organization with identifier '${idOrSubdomain}' does not exist`
          }
        });
      }

      // Find user by email
      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: `User with email '${email}' does not exist`
          }
        });
      }

      // Check if user is active
      if (user.status !== 'active') {
        return res.status(400).json({
          success: false,
          message: 'Cannot assign inactive user to organization',
          error: {
            code: 'USER_INACTIVE',
            details: `User status is '${user.status}', must be 'active'`
          }
        });
      }

      // Find the appropriate RBAC role
      let rbacRole;
      if (roleId) {
        // Use provided role ID
        if (!mongoose.Types.ObjectId.isValid(roleId)) {
          return res.status(400).json({
            success: false,
            message: 'Invalid role ID',
            error: {
              code: 'INVALID_ROLE_ID',
              details: 'Role ID must be a valid MongoDB ObjectId'
            }
          });
        }

        rbacRole = await RBACRole.findOne({
          _id: roleId,
          $or: [
            { scope: 'organization', organization: org._id },
            { scope: 'system' }
          ],
          'metadata.isActive': true
        });
      } else {
        // Find role by name within organization using mapped role name
        rbacRole = await RBACRole.findOne({
          name: targetRoleName,
          $or: [
            { scope: 'organization', organization: org._id },
            { scope: 'system' }
          ],
          'metadata.isActive': true
        });

        // If role not found, try to create it
        if (!rbacRole) {
          try {
            const systemRoles = RBACRole.getSystemRoles();
            const roleTemplate = systemRoles.find(r => r.name === targetRoleName && r.scope === 'organization');

            if (roleTemplate) {
              rbacRole = await RBACRole.create({
                ...roleTemplate,
                organization: org._id,
                audit: {
                  createdBy: req.user._id,
                  createdAt: new Date(),
                  usageCount: 0,
                  changeHistory: [{
                    action: 'created',
                    timestamp: new Date(),
                    performedBy: req.user._id,
                    reason: 'Auto-created during user assignment'
                  }]
                }
              });

              logger.info('Auto-created RBAC role for organization', {
                component: 'organization-controller',
                operation: 'add_user',
                metadata: {
                  role_name: targetRoleName,
                  organization_id: org._id,
                  created_by: req.user._id
                }
              });
            }
          } catch (createError) {
            logger.warn('Failed to auto-create RBAC role', createError);
          }
        }
      }

      if (!rbacRole) {
        return res.status(404).json({
          success: false,
          message: 'Role not found and could not be created',
          error: {
            code: 'ROLE_NOT_FOUND',
            details: roleId
              ? `Role with ID '${roleId}' not found in organization`
              : `Role '${targetRoleName}' not found in organization and auto-creation failed`,
            providedRole: role || roleName || 'member',
            mappedRole: targetRoleName,
            availableRoles: ['member', 'admin', 'manager', 'guest']
          }
        });
      }

      // Initialize user roles array if needed
      if (!user.roles) user.roles = [];

      // Check if user is already in this organization
      const existingRoleIndex = user.roles.findIndex(r => r.org.toString() === org._id.toString());

      if (existingRoleIndex === -1) {
        // Add new role assignment
        user.roles.push({
          org: org._id,
          role: rbacRole._id, // Use ObjectId instead of string
          assignedAt: new Date(),
          assignedBy: req.user._id
        });
      } else {
        // Update existing role assignment
        user.roles[existingRoleIndex].role = rbacRole._id;
        user.roles[existingRoleIndex].assignedAt = new Date();
        user.roles[existingRoleIndex].assignedBy = req.user._id;
      }

      await user.save();

      // Add user to organization members using enhanced member schema
      const existingMemberIndex = org.members.findIndex(m => m.user.toString() === user._id.toString());

      if (existingMemberIndex === -1) {
        // Add new member
        org.members.push({
          user: user._id,
          role: targetRoleName,
          joinedAt: new Date(),
          addedBy: req.user._id,
          status: 'active'
        });
      } else {
        // Update existing member role
        org.members[existingMemberIndex].role = targetRoleName;
        org.members[existingMemberIndex].addedBy = req.user._id;
      }

      await org.save();

      // Auto-assign as default organization if user has no default
      if (!user.defaultOrganization) {
        await user.setDefaultOrganization(org._id);
        logger.info('Auto-assigned default organization for user', {
          component: 'organization-controller',
          operation: 'add_user',
          metadata: {
            user_id: user._id,
            organization_id: org._id,
            organization_name: org.name
          }
        });
      }

      // Log the assignment
      logger.info('User assigned to organization', {
        component: 'organizations-controller',
        operation: 'add_user',
        metadata: {
          user_id: user._id,
          user_email: email,
          organization_id: org._id,
          organization_name: org.name,
          role_id: rbacRole._id,
          role_name: rbacRole.name,
          assigned_by: req.user._id,
          is_new_assignment: existingRoleIndex === -1
        }
      });

      res.json({
        success: true,
        message: `User ${email} successfully ${existingRoleIndex === -1 ? 'added to' : 'updated in'} organization with role ${rbacRole.displayName}`,
        data: {
          user: {
            _id: user._id,
            email: user.email,
            name: user.name,
            status: user.status
          },
          organization: {
            _id: org._id,
            name: org.name,
            subdomain: org.subdomain
          },
          role: {
            _id: rbacRole._id,
            name: rbacRole.name,
            displayName: rbacRole.displayName,
            description: rbacRole.description
          },
          assignment: {
            assignedAt: new Date(),
            assignedBy: req.user._id,
            isNewAssignment: existingRoleIndex === -1
          }
        },
        metadata: {
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Failed to add user to organization', {
        component: 'organizations-controller',
        operation: 'add_user',
        error: error.message,
        metadata: {
          organization_identifier: req.params.idOrSubdomain,
          user_email: req.body.email,
          assigned_by: req.user._id
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to add user to organization',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          details: 'An error occurred while processing the user assignment'
        }
      });
    }
  }

  /**
   * Remove user from organization
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async removeUserFromOrganization(req, res) {
    try {
      const { idOrSubdomain } = req.params;
      const { email } = req.body;

      // Validate input
      if (!email || !email.includes('@')) {
        return res.status(400).json({
          success: false,
          message: 'Valid email address is required',
          error: {
            code: 'INVALID_EMAIL',
            details: 'Email must be a valid email address'
          }
        });
      }

      // Find organization by ID or subdomain
      const query = this.isObjectId(idOrSubdomain)
        ? { _id: idOrSubdomain }
        : { subdomain: idOrSubdomain.toLowerCase() };

      const org = await Organization.findOne(query);
      if (!org) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: `Organization with identifier '${idOrSubdomain}' does not exist`
          }
        });
      }

      // Find user by email
      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: `User with email '${email}' does not exist`
          }
        });
      }

      // Check if user is in this organization
      const roleIndex = user.roles?.findIndex(r => r.org.toString() === org._id.toString());
      if (roleIndex === -1 || !user.roles) {
        return res.status(400).json({
          success: false,
          message: 'User is not a member of this organization',
          error: {
            code: 'USER_NOT_MEMBER',
            details: `User '${email}' is not a member of organization '${org.name}'`
          }
        });
      }

      // Prevent removing the last admin/super user
      const userRole = await RBACRole.findById(user.roles[roleIndex].role);
      if (userRole && userRole.hierarchy?.level <= 2) { // Admin level or higher
        const adminCount = await User.countDocuments({
          'roles.org': org._id,
          'roles.role': { $in: await RBACRole.find({
            organization: org._id,
            'hierarchy.level': { $lte: 2 },
            'metadata.isActive': true
          }).distinct('_id') }
        });

        if (adminCount <= 1) {
          return res.status(400).json({
            success: false,
            message: 'Cannot remove the last administrator from organization',
            error: {
              code: 'LAST_ADMIN_REMOVAL_FORBIDDEN',
              details: 'At least one administrator must remain in the organization'
            }
          });
        }
      }

      // Remove role from user
      user.roles.splice(roleIndex, 1);

      // Handle default organization reassignment if this was the user's default
      await user.reassignDefaultOrganization(org._id);

      await user.save();

      // Remove user from organization members
      org.members = org.members.filter(m => m.toString() !== user._id.toString());
      await org.save();

      // Log the removal
      logger.info('User removed from organization', {
        component: 'organizations-controller',
        operation: 'remove_user',
        metadata: {
          user_id: user._id,
          user_email: email,
          organization_id: org._id,
          organization_name: org.name,
          removed_by: req.user._id,
          previous_role: userRole?.name
        }
      });

      res.json({
        success: true,
        message: `User ${email} successfully removed from organization ${org.name}`,
        data: {
          user: {
            _id: user._id,
            email: user.email,
            name: user.name
          },
          organization: {
            _id: org._id,
            name: org.name,
            subdomain: org.subdomain
          },
          removal: {
            removedAt: new Date(),
            removedBy: req.user._id,
            previousRole: userRole?.displayName
          }
        },
        metadata: {
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Failed to remove user from organization', {
        component: 'organizations-controller',
        operation: 'remove_user',
        error: error.message,
        metadata: {
          organization_identifier: req.params.idOrSubdomain,
          user_email: req.body.email,
          removed_by: req.user._id
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to remove user from organization',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          details: 'An error occurred while processing the user removal'
        }
      });
    }
  }

  /**
   * Get organization details with comprehensive member information using flexible identifiers
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getOrganizationDetailsWithMembers(req, res) {
    const startTime = Date.now();

    try {
      const { orgIdOrSubdomain } = req.params;

      // Validate identifier format
      if (!orgIdOrSubdomain || orgIdOrSubdomain.trim() === '') {
        return res.status(400).json({
          success: false,
          message: 'Organization identifier is required',
          error: {
            code: 'INVALID_ORG_IDENTIFIER',
            details: 'Organization identifier must be either a valid MongoDB ObjectId or subdomain'
          }
        });
      }

      // Resolve organization using flexible identifier
      const organization = await this.resolveOrganization(orgIdOrSubdomain);

      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: this.isObjectId(orgIdOrSubdomain)
              ? `Organization with ID ${orgIdOrSubdomain} does not exist`
              : `Organization with subdomain ${orgIdOrSubdomain} does not exist`
          }
        });
      }

      // Get organization members with detailed role information
      const members = await User.find(
        { 'roles.org': organization._id },
        'email name status roles'
      ).populate({
        path: 'roles.role',
        select: 'name displayName description hierarchy permissions scope'
      });

      // Process members with enhanced role information
      const membersWithRoleDetails = members.map(user => {
        const orgRoleAssignment = user.roles.find(r =>
          r.org.toString() === organization._id.toString()
        );

        return {
          _id: user._id,
          email: user.email,
          name: user.name,
          status: user.status,
          role: orgRoleAssignment?.role ? {
            _id: orgRoleAssignment.role._id,
            name: orgRoleAssignment.role.name,
            displayName: orgRoleAssignment.role.displayName,
            description: orgRoleAssignment.role.description,
            hierarchy: orgRoleAssignment.role.hierarchy,
            scope: orgRoleAssignment.role.scope
          } : null,
          assignedAt: orgRoleAssignment?.assignedAt,
          assignedBy: orgRoleAssignment?.assignedBy
        };
      });

      const executionTime = Date.now() - startTime;
      const lookupMethod = this.isObjectId(orgIdOrSubdomain) ? 'objectId' : 'subdomain';

      // Log the operation for audit purposes
      logger.info('Organization details retrieved', {
        component: 'organizations-controller',
        operation: 'get_organization_details',
        metadata: {
          organization_context: {
            organization_id: organization._id,
            organization_name: organization.name,
            organization_subdomain: organization.subdomain
          },
          identifier_types: {
            organization_lookup_method: lookupMethod
          },
          execution_time_ms: executionTime,
          member_count: membersWithRoleDetails.length
        }
      });

      // Audit logging
      await auditLogger.logOperation({
        user: req.user,
        operationType: 'READ',
        resourceType: 'organization',
        resourceId: organization._id,
        endpoint: req.originalUrl,
        method: req.method,
        requestData: { orgIdOrSubdomain },
        responseStatus: 200,
        executionTime,
        organizationContext: {
          organization_id: organization._id,
          organization_name: organization.name,
          organization_subdomain: organization.subdomain
        },
        identifierTypes: {
          organization_lookup_method: lookupMethod
        },
        customMetadata: {
          member_count: membersWithRoleDetails.length,
          includes_member_details: true
        }
      });

      res.json({
        success: true,
        data: {
          ...organization.toObject(),
          members: membersWithRoleDetails
        },
        metadata: {
          lookup_method: lookupMethod,
          member_count: membersWithRoleDetails.length,
          execution_time_ms: executionTime,
          retrieved_at: new Date().toISOString()
        }
      });

    } catch (error) {
      const executionTime = Date.now() - startTime;

      logger.error('Failed to fetch organization details', {
        component: 'organizations-controller',
        operation: 'get_organization_details',
        error: error.message,
        metadata: {
          organization_identifier: req.params.orgIdOrSubdomain,
          user_id: req.user._id,
          execution_time_ms: executionTime
        }
      });

      // Audit logging for error
      await auditLogger.logOperation({
        user: req.user,
        operationType: 'READ',
        resourceType: 'organization',
        resourceId: req.params.orgIdOrSubdomain,
        endpoint: req.originalUrl,
        method: req.method,
        requestData: { orgIdOrSubdomain: req.params.orgIdOrSubdomain },
        responseStatus: 500,
        executionTime,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to fetch organization details',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          details: 'An error occurred while retrieving organization information'
        }
      });
    }
  }

  /**
   * Helper method to resolve organization by identifier
   * @param {string} orgIdentifier - Organization ObjectId or subdomain
   * @returns {Object|null} Organization document or null if not found
   */
  async resolveOrganization(orgIdentifier) {
    try {
      // If it's a valid ObjectId, find by _id
      if (this.isObjectId(orgIdentifier)) {
        return await Organization.findById(orgIdentifier);
      }

      // Otherwise, try to find by subdomain
      return await Organization.findOne({ subdomain: orgIdentifier.toLowerCase() });
    } catch (error) {
      logger.error('Failed to resolve organization identifier', {
        component: 'organizations-controller',
        operation: 'resolve_organization',
        error: error.message,
        metadata: { org_identifier: orgIdentifier }
      });
      return null;
    }
  }

  /**
   * Get all organization subdomains (PUBLIC ENDPOINT - No Authentication Required)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPublicSubdomains(req, res) {
    try {
      logger.info('Fetching public subdomains', {
        component: 'organization-controller',
        operation: 'get_public_subdomains',
        metadata: {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          endpoint: 'public'
        }
      });

      // Simple query to get ALL subdomains from ALL organizations
      const organizations = await Organization.find({})
        .select('subdomain')
        .lean();

      // Extract only subdomain values
      const subdomains = organizations
        .map(org => org.subdomain)
        .filter(subdomain => subdomain) // Remove any null/undefined subdomains
        .sort(); // Sort alphabetically for consistency

      logger.info('Successfully retrieved public subdomains', {
        component: 'organization-controller',
        operation: 'get_public_subdomains',
        metadata: {
          total_subdomains: subdomains.length,
          ip_address: req.ip
        }
      });

      res.json({
        success: true,
        data: {
          subdomains: subdomains,
          total: subdomains.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to get public subdomains', error, {
        component: 'organization-controller',
        operation: 'get_public_subdomains',
        metadata: {
          ip_address: req.ip,
          error_message: error.message
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve subdomains',
        error: {
          code: 'PUBLIC_SUBDOMAINS_FETCH_ERROR',
          details: 'Internal server error'
        }
      });
    }
  }



  /**
   * Validate user role references (Debug/Admin endpoint)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async validateUserRoleReferences(req, res) {
    try {
      const { userId } = req.params;

      // Find user
      const user = await User.findById(userId).populate('roles.org', 'name subdomain').populate('roles.role', 'name displayName');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const validation = {
        userId: user._id,
        email: user.email,
        totalRoles: user.roles.length,
        validRoles: 0,
        nullRoles: 0,
        invalidRoles: 0,
        roleDetails: []
      };

      for (const roleEntry of user.roles) {
        const detail = {
          organization: roleEntry.org ? {
            id: roleEntry.org._id,
            name: roleEntry.org.name,
            subdomain: roleEntry.org.subdomain
          } : null,
          role: roleEntry.role ? {
            id: roleEntry.role._id,
            name: roleEntry.role.name,
            displayName: roleEntry.role.displayName
          } : null,
          assignedAt: roleEntry.assignedAt,
          status: 'unknown'
        };

        if (!roleEntry.role) {
          detail.status = 'null_role';
          validation.nullRoles++;
        } else if (!roleEntry.org) {
          detail.status = 'null_organization';
          validation.invalidRoles++;
        } else {
          detail.status = 'valid';
          validation.validRoles++;
        }

        validation.roleDetails.push(detail);
      }

      res.json({
        success: true,
        data: validation,
        summary: {
          hasIssues: validation.nullRoles > 0 || validation.invalidRoles > 0,
          issueCount: validation.nullRoles + validation.invalidRoles
        }
      });

    } catch (error) {
      logger.error('Failed to validate user role references', error, {
        component: 'organization-controller',
        operation: 'validate_user_role_references',
        metadata: {
          user_id: req.params.userId
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to validate role references',
        error: error.message
      });
    }
  }
}

module.exports = new OrganizationController();
