// services/privilege-manager.js
const User = require('../models/User');
const Role = require('../models/Role');
const RBACRole = require('../models/RBACRole');
const Organization = require('../models/Organization');
const rbacManager = require('./rbac-manager');
const logger = require('./logger');
const mongoose = require('mongoose');

/**
 * Comprehensive Privilege Management Service
 *
 * Handles hierarchical user privilege system including:
 * - God Super User management
 * - Organization Super User assignment
 * - Role-based access control
 * - Privilege validation and audit logging
 */
class PrivilegeManager {

  /**
   * Assign God Super User privileges to a user with bootstrap support
   * Supports first-time setup (bootstrap) and privilege transfers
   * @param {string|ObjectId} userId - User ID to grant God privileges
   * @param {string|ObjectId} grantedBy - User ID who is granting the privilege
   * @param {Object} securitySettings - Security settings for the privilege
   * @param {boolean} forceBootstrap - Force bootstrap mode (for testing/migration)
   * @returns {Object} Result with success status and details
   */
  async assignGodSuperUser(userId, grantedBy, securitySettings = {}, forceBootstrap = false) {
    const correlationId = logger.generateCorrelationId();

    try {
      // Check if this is a bootstrap scenario (no God Super User exists)
      const hasExistingGodUser = await User.hasGodSuperUser();
      const isBootstrap = !hasExistingGodUser || forceBootstrap;

      logger.info('Starting God Super User assignment', {
        component: 'privilege-manager',
        operation: 'assign_god_super_user',
        correlation_id: correlationId,
        metadata: {
          target_user_id: userId,
          granted_by: grantedBy,
          security_settings: securitySettings,
          is_bootstrap: isBootstrap,
          existing_god_user_exists: hasExistingGodUser,
          force_bootstrap: forceBootstrap
        }
      });

      // Validate target user exists
      const targetUser = await User.findById(userId);
      if (!targetUser) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'Target user not found',
            details: `User with ID ${userId} does not exist`
          }
        };
      }

      let currentGodUser = null;

      if (isBootstrap) {
        // BOOTSTRAP SCENARIO: No God Super User exists, allow any authenticated user
        logger.info('Bootstrap God Super User assignment detected', {
          component: 'privilege-manager',
          operation: 'assign_god_super_user_bootstrap',
          correlation_id: correlationId,
          metadata: {
            target_user_id: userId,
            target_user_email: targetUser.email,
            granted_by: grantedBy,
            is_first_god_user: true
          }
        });

        // Validate granting user exists (for audit purposes)
        if (grantedBy !== 'system') {
          const grantingUser = await User.findById(grantedBy);
          if (!grantingUser) {
            return {
              success: false,
              error: {
                code: 'GRANTING_USER_NOT_FOUND',
                message: 'Granting user not found',
                details: 'The user attempting to assign privileges does not exist'
              }
            };
          }
        }

      } else {
        // PROTECTED SCENARIO: God Super User exists, require proper authorization
        logger.info('Protected God Super User assignment detected', {
          component: 'privilege-manager',
          operation: 'assign_god_super_user_protected',
          correlation_id: correlationId,
          metadata: {
            target_user_id: userId,
            granted_by: grantedBy,
            requires_god_privileges: true
          }
        });

        // Get current God Super User for privilege transfer
        currentGodUser = await User.getCurrentGodSuperUser();

        // Validate granting user has God Super User privileges
        if (grantedBy !== 'system') {
          const grantingUser = await User.findById(grantedBy);
          if (!grantingUser?.isGodSuperUser()) {
            return {
              success: false,
              error: {
                code: 'INSUFFICIENT_PRIVILEGES',
                message: 'Only God Super User can assign God Super User privileges',
                details: 'Granting user does not have sufficient privileges for this protected operation'
              }
            };
          }
        }
      }

      // Perform atomic privilege transfer using the enhanced User model method
      const transferResult = await User.transferGodSuperUserPrivileges(
        currentGodUser?._id, // fromUserId (null for bootstrap)
        userId,              // toUserId
        grantedBy === 'system' ? null : grantedBy, // transferredBy
        securitySettings
      );

      if (!transferResult.success) {
        throw new Error('Failed to transfer God Super User privileges');
      }

      // Get updated user data for response
      const updatedUser = await User.findById(userId);
      const godPrivilege = updatedUser.systemPrivileges?.find(p => p.level === 'god_super_user');

      logger.info('God Super User privilege assigned successfully', {
        component: 'privilege-manager',
        operation: 'assign_god_super_user',
        correlation_id: correlationId,
        metadata: {
          target_user_id: userId,
          target_user_email: updatedUser.email,
          granted_by: grantedBy,
          security_settings: securitySettings,
          is_bootstrap: isBootstrap,
          previous_god_user_id: currentGodUser?._id,
          previous_god_user_email: currentGodUser?.email,
          privilege_transfer_completed: true
        }
      });

      return {
        success: true,
        data: {
          user_id: userId,
          user_email: updatedUser.email,
          privilege_level: 'god_super_user',
          granted_at: godPrivilege?.grantedAt,
          security_settings: godPrivilege?.security,
          assignment_type: isBootstrap ? 'bootstrap' : 'transfer',
          previous_god_user: currentGodUser ? {
            id: currentGodUser._id,
            email: currentGodUser.email,
            demoted_to: 'audit_viewer'
          } : null
        }
      };

    } catch (error) {
      logger.error('Failed to assign God Super User privilege', error, {
        component: 'privilege-manager',
        operation: 'assign_god_super_user',
        correlation_id: correlationId,
        metadata: {
          target_user_id: userId,
          granted_by: grantedBy
        }
      });

      return {
        success: false,
        error: {
          code: 'ASSIGNMENT_FAILED',
          message: 'Failed to assign God Super User privilege',
          details: error.message
        }
      };
    }
  }

  /**
   * Assign Organization Super User role to a user
   * @param {string} userIdentifier - User email or ID
   * @param {string} orgIdentifier - Organization ID or subdomain
   * @param {string|ObjectId} assignedBy - User ID who is assigning the role
   * @returns {Object} Result with success status and details
   */
  async assignOrgSuperUser(userIdentifier, orgIdentifier, assignedBy) {
    const correlationId = logger.generateCorrelationId();

    try {
      logger.info('Starting Organization Super User assignment', {
        component: 'privilege-manager',
        operation: 'assign_org_super_user',
        correlation_id: correlationId,
        metadata: {
          user_identifier: userIdentifier,
          org_identifier: orgIdentifier,
          assigned_by: assignedBy
        }
      });

      // Find user by email or ID
      const userQuery = mongoose.Types.ObjectId.isValid(userIdentifier)
        ? { _id: userIdentifier }
        : { email: userIdentifier.toLowerCase() };

      const user = await User.findOne(userQuery);
      if (!user) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            details: `User with identifier ${userIdentifier} does not exist`
          }
        };
      }

      // Find organization by ID or subdomain
      const orgQuery = mongoose.Types.ObjectId.isValid(orgIdentifier)
        ? { _id: orgIdentifier }
        : { subdomain: orgIdentifier.toLowerCase() };

      const organization = await Organization.findOne(orgQuery);
      if (!organization) {
        return {
          success: false,
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            message: 'Organization not found',
            details: `Organization with identifier ${orgIdentifier} does not exist`
          }
        };
      }

      // Find or create org_super_user role for this organization
      let orgSuperUserRole = await Role.findOne({
        org: organization._id,
        name: 'org_super_user'
      });

      if (!orgSuperUserRole) {
        // Create the org_super_user role using system role template
        const systemRoles = Role.getSystemRoles();
        const orgSuperUserTemplate = systemRoles.find(r => r.name === 'org_super_user');

        orgSuperUserRole = await Role.create({
          org: organization._id,
          ...orgSuperUserTemplate,
          audit: {
            createdBy: assignedBy,
            usageCount: 0
          }
        });
      }

      // Remove any existing role for this organization
      user.roles = user.roles?.filter(r => r.org.toString() !== organization._id.toString()) || [];

      // Assign org super user role
      user.roles.push({
        org: organization._id,
        role: orgSuperUserRole._id,
        assignedAt: new Date(),
        assignedBy: assignedBy
      });

      await user.save();

      // Update role usage count
      orgSuperUserRole.audit.usageCount = (orgSuperUserRole.audit.usageCount || 0) + 1;
      orgSuperUserRole.audit.lastUsed = new Date();
      await orgSuperUserRole.save();

      logger.info('Organization Super User role assigned successfully', {
        component: 'privilege-manager',
        operation: 'assign_org_super_user',
        correlation_id: correlationId,
        metadata: {
          user_id: user._id,
          user_email: user.email,
          org_id: organization._id,
          org_name: organization.name,
          org_subdomain: organization.subdomain,
          assigned_by: assignedBy
        }
      });

      return {
        success: true,
        data: {
          user_id: user._id,
          user_email: user.email,
          organization_id: organization._id,
          organization_name: organization.name,
          organization_subdomain: organization.subdomain,
          role_name: 'org_super_user',
          assigned_at: new Date()
        }
      };

    } catch (error) {
      logger.error('Failed to assign Organization Super User role', error, {
        component: 'privilege-manager',
        operation: 'assign_org_super_user',
        correlation_id: correlationId,
        metadata: {
          user_identifier: userIdentifier,
          org_identifier: orgIdentifier,
          assigned_by: assignedBy
        }
      });

      return {
        success: false,
        error: {
          code: 'ASSIGNMENT_FAILED',
          message: 'Failed to assign Organization Super User role',
          details: error.message
        }
      };
    }
  }

  /**
   * Initialize system roles for an organization using centralized RBAC database
   * @param {string|ObjectId} orgId - Organization ID
   * @param {string|ObjectId} createdBy - User ID who is initializing the roles
   * @returns {Object} Result with created roles
   */
  async initializeSystemRoles(orgId, createdBy = null) {
    const correlationId = logger.generateCorrelationId();

    try {
      logger.info('Initializing system roles for organization using centralized RBAC', {
        component: 'privilege-manager',
        operation: 'initialize_system_roles',
        correlation_id: correlationId,
        metadata: {
          org_id: orgId,
          created_by: createdBy,
          using_centralized_rbac: true
        }
      });

      // Use centralized RBAC manager for role initialization
      const result = await rbacManager.initializeSystemRoles(orgId, createdBy);

      if (result.success) {
        logger.info('System roles initialized successfully via centralized RBAC', {
          component: 'privilege-manager',
          operation: 'initialize_system_roles',
          correlation_id: correlationId,
          metadata: {
            org_id: orgId,
            created_by: createdBy,
            created_roles_count: result.data.total_created,
            skipped_roles_count: result.data.total_skipped,
            organization_name: result.data.organization_name
          }
        });

        return {
          success: true,
          data: {
            organization_id: orgId,
            organization_name: result.data.organization_name,
            created_roles: result.data.roles_created,
            skipped_roles: result.data.roles_skipped,
            total_created: result.data.total_created,
            total_skipped: result.data.total_skipped,
            source: 'centralized_rbac_database'
          }
        };
      } else {
        // Fallback to legacy role creation if centralized RBAC fails
        logger.warn('Centralized RBAC initialization failed, falling back to legacy method', {
          component: 'privilege-manager',
          operation: 'initialize_system_roles',
          correlation_id: correlationId,
          metadata: {
            org_id: orgId,
            rbac_error: result.error
          }
        });

        return await this.initializeSystemRolesLegacy(orgId, correlationId);
      }

    } catch (error) {
      logger.error('Failed to initialize system roles', error, {
        component: 'privilege-manager',
        operation: 'initialize_system_roles',
        correlation_id: correlationId,
        metadata: {
          org_id: orgId,
          created_by: createdBy
        }
      });

      return {
        success: false,
        error: {
          code: 'INITIALIZATION_FAILED',
          message: 'Failed to initialize system roles',
          details: error.message
        }
      };
    }
  }

  /**
   * Legacy system role initialization (fallback method)
   * @param {string|ObjectId} orgId - Organization ID
   * @param {string} correlationId - Correlation ID for logging
   * @returns {Object} Result with created roles
   */
  async initializeSystemRolesLegacy(orgId, correlationId) {
    try {
      logger.info('Using legacy system role initialization', {
        component: 'privilege-manager',
        operation: 'initialize_system_roles_legacy',
        correlation_id: correlationId,
        metadata: { org_id: orgId }
      });

      const systemRoles = Role.getSystemRoles();
      const createdRoles = [];

      for (const roleTemplate of systemRoles) {
        const existingRole = await Role.findOne({
          org: orgId,
          name: roleTemplate.name
        });

        if (!existingRole) {
          const newRole = await Role.create({
            org: orgId,
            ...roleTemplate,
            audit: {
              createdBy: null, // System created
              usageCount: 0
            }
          });
          createdRoles.push(newRole);
        }
      }

      return {
        success: true,
        data: {
          organization_id: orgId,
          created_roles: createdRoles.map(role => ({
            id: role._id,
            name: role.name,
            description: role.description,
            hierarchy_level: role.hierarchy?.level || 10
          })),
          source: 'legacy_role_model'
        }
      };

    } catch (error) {
      logger.error('Legacy system role initialization failed', error, {
        component: 'privilege-manager',
        operation: 'initialize_system_roles_legacy',
        correlation_id: correlationId,
        metadata: { org_id: orgId }
      });

      throw error;
    }
  }
}

module.exports = new PrivilegeManager();
