#!/usr/bin/env node

/**
 * Test Script: My Organizations Endpoint
 * 
 * This script tests the unified my-organizations endpoint with different parameters
 * to help debug the "Organization not found" issue.
 * 
 * Usage: node src/scripts/testMyOrganizationsEndpoint.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Organization = require('../models/Organization');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test the specific user from the API response
const testSpecificUser = async () => {
  console.log('\n🎯 Testing specific user from API response...');
  
  // This is the user ID from the /users/me response
  const userId = '68330577e2576bff416a2ae7';
  
  const user = await User.findById(userId);
  if (!user) {
    console.log('❌ User not found');
    return null;
  }
  
  console.log(`👤 User: ${user.email}`);
  console.log(`🆔 User ID: ${user._id}`);
  console.log(`🔥 Firebase UID: ${user.firebase_uid}`);
  
  // Test getOrganizationsWithRoles method
  console.log('\n🏢 Testing getOrganizationsWithRoles method:');
  try {
    const organizationsWithRoles = await user.getOrganizationsWithRoles();
    console.log(`   Found ${organizationsWithRoles.length} organizations`);
    
    organizationsWithRoles.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.organization.name}`);
      console.log(`      ID: ${item.organization._id}`);
      console.log(`      Subdomain: ${item.organization.subdomain}`);
      console.log(`      Status: ${item.organization.status}`);
      console.log(`      User Role: ${item.role}`);
      console.log(`      Joined: ${item.joinedAt}`);
      console.log(`      User Status: ${item.status}`);
    });
    
    return { user, organizations: organizationsWithRoles };
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return { user, organizations: [] };
  }
};

// Test the controller method with different parameters
const testControllerWithParams = async (user, organizations) => {
  console.log('\n🧪 Testing controller method with different parameters...');
  
  if (!user || organizations.length === 0) {
    console.log('❌ No user or organizations to test with');
    return;
  }
  
  const firstOrg = organizations[0];
  const OrganizationController = require('../api/v1/controllers/organizationController');
  const controller = new OrganizationController();
  
  // Test cases
  const testCases = [
    {
      name: 'No parameters (all organizations)',
      query: {}
    },
    {
      name: 'Current organization only',
      query: { current: 'true' }
    },
    {
      name: 'Filter by subdomain',
      query: { subdomain: firstOrg.organization.subdomain }
    },
    {
      name: 'Filter by status',
      query: { status: firstOrg.organization.status }
    },
    {
      name: 'Filter by role',
      query: { role: firstOrg.role }
    },
    {
      name: 'Current with details',
      query: { current: 'true', include_details: 'true' }
    },
    {
      name: 'All filters combined (problematic case)',
      query: {
        current: 'true',
        subdomain: firstOrg.organization.subdomain,
        status: firstOrg.organization.status,
        role: firstOrg.role,
        include_details: 'true'
      }
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log(`   Query: ${JSON.stringify(testCase.query)}`);
    
    const mockReq = {
      user: { uid: user.firebase_uid, _id: user._id },
      query: testCase.query,
      ip: '127.0.0.1',
      get: () => 'test-user-agent'
    };
    
    let responseData = null;
    let statusCode = null;
    
    const mockRes = {
      json: (data) => {
        responseData = data;
        return mockRes;
      },
      status: (code) => {
        statusCode = code;
        return mockRes;
      }
    };
    
    try {
      await controller.getMyOrganizations(mockReq, mockRes);
      
      if (statusCode === 200 || (!statusCode && responseData?.success)) {
        console.log(`   ✅ Success: ${responseData?.data?.total || 'single org'} organization(s)`);
        if (responseData?.data?.organizations) {
          console.log(`      Organizations: ${responseData.data.organizations.length}`);
        } else if (responseData?.data?._id) {
          console.log(`      Single org: ${responseData.data.name}`);
        }
      } else {
        console.log(`   ❌ Failed: Status ${statusCode}`);
        console.log(`      Message: ${responseData?.message}`);
        if (responseData?.debug) {
          console.log(`      Debug info:`);
          console.log(`        Total user orgs: ${responseData.debug.total_user_organizations}`);
          console.log(`        Filters applied: ${JSON.stringify(responseData.debug.filters_applied)}`);
          if (responseData.debug.organizations_before_filtering) {
            console.log(`        Orgs before filtering:`);
            responseData.debug.organizations_before_filtering.forEach((org, i) => {
              console.log(`          ${i + 1}. ${org.name} (${org.subdomain}, ${org.status}, role: ${org.user_role})`);
            });
          }
        }
      }
    } catch (error) {
      console.log(`   ❌ Exception: ${error.message}`);
    }
  }
};

// Test role value matching
const testRoleMatching = async () => {
  console.log('\n🔍 Testing role value matching...');
  
  // Find organizations and check what role values are actually stored
  const orgs = await Organization.find({}).limit(5);
  
  console.log('📋 Organization member roles in database:');
  for (const org of orgs) {
    console.log(`   ${org.name}:`);
    if (org.members && org.members.length > 0) {
      org.members.forEach((member, i) => {
        console.log(`     ${i + 1}. User: ${member.user}, Role: "${member.role}", Status: ${member.status}`);
      });
    } else {
      console.log('     No members');
    }
  }
  
  // Check what getOrganizationsWithRoles returns
  console.log('\n📋 What getOrganizationsWithRoles returns:');
  const users = await User.find({}).limit(3);
  for (const user of users) {
    try {
      const userOrgs = await user.getOrganizationsWithRoles();
      if (userOrgs.length > 0) {
        console.log(`   ${user.email}:`);
        userOrgs.forEach((item, i) => {
          console.log(`     ${i + 1}. Org: ${item.organization.name}, Role: "${item.role}"`);
        });
      }
    } catch (error) {
      console.log(`   ${user.email}: Error - ${error.message}`);
    }
  }
};

// Main execution function
const main = async () => {
  console.log('🚀 Starting My Organizations Endpoint Test...');
  
  try {
    await connectDB();
    
    const testData = await testSpecificUser();
    if (testData) {
      await testControllerWithParams(testData.user, testData.organizations);
    }
    
    await testRoleMatching();
    
    console.log('\n🎉 Test completed!');
    console.log('\n💡 Key Insights:');
    console.log('   1. Check if role values match exactly (super_user vs org_super_user)');
    console.log('   2. Verify subdomain, status, and role values are correct');
    console.log('   3. Test individual filters before combining them');
    console.log('   4. Use the debug info to understand filtering results');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n👋 Database connection closed');
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, testSpecificUser, testControllerWithParams };
