#!/usr/bin/env node
// scripts/run-migration.js
const mongoose = require('mongoose');
const HierarchicalPrivilegesMigration = require('../migrations/001-hierarchical-privileges');

/**
 * Migration Runner Script
 * 
 * Usage:
 * node src/scripts/run-migration.js --migration=hierarchical-privileges [options]
 * 
 * Options:
 * --dry-run                    Run migration in dry-run mode (no changes)
 * --god-super-user=email       Email of user to assign God Super User privileges
 * --rollback                   Rollback the migration
 * --help                       Show help
 */

async function runMigration() {
  const args = process.argv.slice(2);
  const options = {};
  let migrationName = '';
  let action = 'up';

  // Parse command line arguments
  for (const arg of args) {
    if (arg.startsWith('--migration=')) {
      migrationName = arg.split('=')[1];
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--god-super-user=')) {
      options.godSuperUserEmail = arg.split('=')[1];
    } else if (arg === '--rollback') {
      action = 'down';
    } else if (arg === '--help') {
      showHelp();
      process.exit(0);
    }
  }

  if (!migrationName) {
    console.error('Error: Migration name is required');
    showHelp();
    process.exit(1);
  }

  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database');
    console.log('Connected to MongoDB');

    // Run the migration
    let migration;
    let result;

    switch (migrationName) {
      case 'hierarchical-privileges':
        migration = new HierarchicalPrivilegesMigration();
        break;
      default:
        console.error(`Error: Unknown migration '${migrationName}'`);
        process.exit(1);
    }

    console.log(`\n=== Running Migration: ${migrationName} ===`);
    console.log(`Action: ${action}`);
    if (options.dryRun) {
      console.log('Mode: DRY RUN (no changes will be made)');
    }
    if (options.godSuperUserEmail) {
      console.log(`God Super User: ${options.godSuperUserEmail}`);
    }
    console.log('');

    if (action === 'up') {
      result = await migration.up(options);
    } else {
      result = await migration.down();
    }

    // Display results
    console.log('\n=== Migration Results ===');
    if (result.success) {
      console.log('✅ Migration completed successfully');
      
      if (result.results) {
        console.log('\nSummary:');
        console.log(`- Users updated: ${result.results.users_updated}`);
        console.log(`- Organizations processed: ${result.results.organizations_processed}`);
        console.log(`- Roles created: ${result.results.roles_created}`);
        console.log(`- God Super User assigned: ${result.results.god_super_user_assigned ? 'Yes' : 'No'}`);
        
        if (result.results.errors.length > 0) {
          console.log('\n⚠️  Errors encountered:');
          result.results.errors.forEach(error => {
            console.log(`  - ${error}`);
          });
        }
      }
    } else {
      console.log('❌ Migration failed');
      console.log(`Error: ${result.error}`);
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Migration failed with exception:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('\nMongoDB connection closed');
  }
}

function showHelp() {
  console.log(`
Migration Runner

Usage:
  node src/scripts/run-migration.js --migration=<name> [options]

Available Migrations:
  hierarchical-privileges    Set up hierarchical privilege system

Options:
  --dry-run                  Run migration in dry-run mode (no changes)
  --god-super-user=email     Email of user to assign God Super User privileges
  --rollback                 Rollback the migration
  --help                     Show this help

Examples:
  # Run hierarchical privileges migration in dry-run mode
  node src/scripts/run-migration.js --migration=hierarchical-privileges --dry-run

  # Run migration and assign God Super User
  node src/scripts/run-migration.js --migration=hierarchical-privileges --god-super-user=<EMAIL>

  # Rollback migration
  node src/scripts/run-migration.js --migration=hierarchical-privileges --rollback

Environment Variables:
  MONGODB_URI               MongoDB connection string (default: mongodb://localhost:27017/your-database)
`);
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runMigration().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { runMigration };
