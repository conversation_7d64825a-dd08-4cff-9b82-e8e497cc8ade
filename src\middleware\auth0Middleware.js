// src/middleware/auth0Middleware.js
const { auth, requiresAuth } = require('express-openid-connect');
const auth0Config = require('../config/auth0Config');
const User = require('../models/User');
const logger = require('../services/logger');

/**
 * Auth0 Middleware with Subdomain Support
 * Integrates Auth0 authentication with existing user system and subdomain functionality
 */
class Auth0Middleware {
  constructor() {
    this.isEnabled = process.env.AUTH0_CLIENT_ID && process.env.AUTH0_SECRET;
  }

  /**
   * Initialize Auth0 middleware with subdomain support
   */
  initialize() {
    if (!this.isEnabled) {
      logger.warn('Auth0 not configured - skipping Auth0 middleware', {
        component: 'auth0-middleware',
        operation: 'initialize'
      });
      return (req, res, next) => next();
    }

    if (!auth0Config.validateConfig()) {
      logger.error('Auth0 configuration validation failed', {
        component: 'auth0-middleware',
        operation: 'initialize'
      });
      return (req, res, next) => next();
    }

    // Return middleware that uses dynamic config based on subdomain
    return (req, res, next) => {
      try {
        // Get Auth0 config for current subdomain
        const config = req.auth0Config || auth0Config.getDefaultConfig();
        
        // Apply Auth0 middleware with dynamic config
        const auth0Middleware = auth(config);
        auth0Middleware(req, res, next);
      } catch (error) {
        logger.error('Auth0 middleware error', {
          component: 'auth0-middleware',
          operation: 'middleware_execution',
          error: error.message,
          metadata: {
            subdomain: req.subdomain?.subdomain,
            path: req.path
          }
        });
        next();
      }
    };
  }

  /**
   * Middleware to sync Auth0 user with local user database
   */
  syncUserMiddleware() {
    return async (req, res, next) => {
      try {
        // Only process if user is authenticated via Auth0
        if (!req.oidc || !req.oidc.isAuthenticated()) {
          return next();
        }

        const auth0User = req.oidc.user;
        
        // Find or create user in local database
        let user = await User.findOne({ 
          $or: [
            { auth0_sub: auth0User.sub },
            { email: auth0User.email }
          ]
        });

        if (!user) {
          // Create new user from Auth0 profile
          user = new User({
            auth0_sub: auth0User.sub,
            email: auth0User.email,
            name: auth0User.name || auth0User.nickname,
            profile_picture: auth0User.picture,
            email_verified: auth0User.email_verified,
            status: 'active',
            created_at: new Date(),
            auth_provider: 'auth0'
          });

          // Add organization context if available
          if (req.organization) {
            user.organizations = [{
              org: req.organization._id,
              role: 'member',
              joined_at: new Date()
            }];
          }

          await user.save();

          logger.info('New user created from Auth0', {
            component: 'auth0-middleware',
            operation: 'user_creation',
            metadata: {
              user_id: user._id,
              auth0_sub: auth0User.sub,
              email: auth0User.email,
              organization_id: req.organization?._id
            }
          });
        } else {
          // Update existing user with Auth0 data
          let updated = false;
          
          if (!user.auth0_sub) {
            user.auth0_sub = auth0User.sub;
            updated = true;
          }
          
          if (user.profile_picture !== auth0User.picture) {
            user.profile_picture = auth0User.picture;
            updated = true;
          }

          if (updated) {
            await user.save();
          }
        }

        // Attach user to request (compatible with existing auth system)
        req.user = user;
        req.auth0User = auth0User;

        // Add organization context to user if not already present
        if (req.organization && user.organizations) {
          const hasOrg = user.organizations.some(org => 
            org.org.toString() === req.organization._id.toString()
          );
          
          if (!hasOrg) {
            user.organizations.push({
              org: req.organization._id,
              role: 'member',
              joined_at: new Date()
            });
            await user.save();
          }
        }

        next();
      } catch (error) {
        logger.error('Auth0 user sync error', {
          component: 'auth0-middleware',
          operation: 'user_sync',
          error: error.message,
          metadata: {
            auth0_sub: req.oidc?.user?.sub,
            email: req.oidc?.user?.email
          }
        });
        next();
      }
    };
  }

  /**
   * Middleware to require Auth0 authentication
   */
  requireAuth() {
    if (!this.isEnabled) {
      return (req, res, next) => next();
    }
    return requiresAuth();
  }

  /**
   * Middleware to optionally use Auth0 or existing auth system
   */
  hybridAuthMiddleware() {
    return async (req, res, next) => {
      try {
        // Check if Auth0 is authenticated
        if (req.oidc && req.oidc.isAuthenticated()) {
          // User is authenticated via Auth0, sync user was handled by syncUserMiddleware
          return next();
        }

        // Fall back to existing authentication system
        const existingAuth = require('./auth');
        return existingAuth.authenticate(req, res, next);
      } catch (error) {
        logger.error('Hybrid auth middleware error', {
          component: 'auth0-middleware',
          operation: 'hybrid_auth',
          error: error.message
        });
        next();
      }
    };
  }

  /**
   * Get Auth0 login URL for current subdomain
   */
  getLoginUrl(req, returnTo = '/') {
    if (req.getAuth0LoginUrl) {
      return req.getAuth0LoginUrl(returnTo);
    }
    
    const subdomain = req.subdomain?.subdomain;
    const rootDomain = process.env.ROOT_DOMAIN || 'digimeet.live';
    const baseURL = subdomain && subdomain !== 'app' 
      ? `https://${subdomain}.${rootDomain}`
      : `https://${rootDomain}`;
    
    return `${baseURL}/auth/login?returnTo=${encodeURIComponent(returnTo)}`;
  }

  /**
   * Get Auth0 logout URL for current subdomain
   */
  getLogoutUrl(req, returnTo = '/') {
    if (req.getAuth0LogoutUrl) {
      return req.getAuth0LogoutUrl(returnTo);
    }
    
    const subdomain = req.subdomain?.subdomain;
    const rootDomain = process.env.ROOT_DOMAIN || 'digimeet.live';
    const baseURL = subdomain && subdomain !== 'app' 
      ? `https://${subdomain}.${rootDomain}`
      : `https://${rootDomain}`;
    
    return `${baseURL}/auth/logout?returnTo=${encodeURIComponent(returnTo)}`;
  }
}

// Export singleton instance
module.exports = new Auth0Middleware();
