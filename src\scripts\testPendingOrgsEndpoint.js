#!/usr/bin/env node

/**
 * Test Script: Verify Pending Organizations Endpoint
 *
 * This script tests the new GET /api/v1/organizations/pending endpoint
 * to ensure it works correctly for god_super_user privileges.
 *
 * Usage: node src/scripts/testPendingOrgsEndpoint.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Organization = require('../models/Organization');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Create test data
const createTestData = async () => {
  console.log('\n🔧 Creating test data...');

  try {
    // Create a god_super_user for testing
    const godUser = await User.findOne({
      systemPrivileges: { $elemMatch: { privilege: 'god_super_user' } }
    });

    if (!godUser) {
      console.log('❌ No god_super_user found. Please create one first.');
      return null;
    }

    console.log(`✅ Found god_super_user: ${godUser.email}`);

    // Create some pending organizations for testing
    const pendingOrgs = [];

    for (let i = 1; i <= 3; i++) {
      const existingOrg = await Organization.findOne({
        subdomain: `test-pending-${i}`
      });

      if (!existingOrg) {
        const org = new Organization({
          name: `Test Pending Organization ${i}`,
          subdomain: `test-pending-${i}`,
          branding: {
            logoUrl: `https://example.com/logo${i}.png`,
            primaryColor: `#${Math.floor(Math.random()*16777215).toString(16)}`
          },
          createdBy: godUser._id,
          createdByRole: 'super_user',
          status: 'pending',
          members: [{
            user: godUser._id,
            role: 'super_user',
            joinedAt: new Date(),
            addedBy: godUser._id,
            status: 'active'
          }]
        });

        await org.save();
        pendingOrgs.push(org);
        console.log(`   ✅ Created pending org: ${org.name}`);
      } else {
        pendingOrgs.push(existingOrg);
        console.log(`   ℹ️  Pending org already exists: ${existingOrg.name}`);
      }
    }

    return { godUser, pendingOrgs };
  } catch (error) {
    console.error('❌ Failed to create test data:', error);
    return null;
  }
};

// Test the controller method directly
const testControllerMethod = async (godUser) => {
  console.log('\n🧪 Testing controller method directly...');

  try {
    const OrganizationController = require('../api/v1/controllers/organizationController');
    const controller = new OrganizationController();

    // Mock request and response objects
    const mockReq = {
      user: { uid: godUser.firebase_uid },
      query: { page: 1, limit: 5 },
      ip: '127.0.0.1',
      get: () => 'test-user-agent'
    };

    const mockRes = {
      json: (data) => {
        console.log('📋 Controller Response:');
        console.log(JSON.stringify(data, null, 2));
        return mockRes;
      },
      status: (code) => {
        console.log(`📊 Status Code: ${code}`);
        return mockRes;
      }
    };

    await controller.getPendingOrganizations(mockReq, mockRes);

  } catch (error) {
    console.error('❌ Controller test failed:', error);
  }
};

// Test privilege checking
const testPrivilegeChecking = async () => {
  console.log('\n🔒 Testing privilege checking...');

  try {
    // Find a regular user (non-god_super_user)
    const regularUser = await User.findOne({
      systemPrivileges: { $not: { $elemMatch: { privilege: 'god_super_user' } } }
    });

    if (!regularUser) {
      console.log('⚠️  No regular user found for privilege testing');
      return;
    }

    console.log(`🔍 Testing with regular user: ${regularUser.email}`);

    const OrganizationController = require('../api/v1/controllers/organizationController');
    const controller = new OrganizationController();

    // Mock request with regular user
    const mockReq = {
      user: { uid: regularUser.firebase_uid },
      query: { page: 1, limit: 5 },
      ip: '127.0.0.1',
      get: () => 'test-user-agent'
    };

    const mockRes = {
      json: (data) => {
        console.log('📋 Regular User Response:');
        console.log(JSON.stringify(data, null, 2));
        return mockRes;
      },
      status: (code) => {
        console.log(`📊 Status Code: ${code}`);
        return mockRes;
      }
    };

    await controller.getPendingOrganizations(mockReq, mockRes);

  } catch (error) {
    console.error('❌ Privilege test failed:', error);
  }
};

// Test pagination
const testPagination = async (godUser) => {
  console.log('\n📄 Testing pagination...');

  try {
    const OrganizationController = require('../api/v1/controllers/organizationController');
    const controller = new OrganizationController();

    // Test with different pagination parameters
    const testCases = [
      { page: 1, limit: 2 },
      { page: 2, limit: 2 },
      { page: 1, limit: 10 }
    ];

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing pagination: page=${testCase.page}, limit=${testCase.limit}`);

      const mockReq = {
        user: { uid: godUser.firebase_uid },
        query: testCase,
        ip: '127.0.0.1',
        get: () => 'test-user-agent'
      };

      const mockRes = {
        json: (data) => {
          if (data.success && data.data) {
            console.log(`   📊 Found ${data.data.organizations.length} organizations`);
            console.log(`   📄 Pagination: ${JSON.stringify(data.data.pagination)}`);
          } else {
            console.log(`   ❌ Error: ${data.message}`);
          }
          return mockRes;
        },
        status: (code) => {
          console.log(`   📊 Status: ${code}`);
          return mockRes;
        }
      };

      await controller.getPendingOrganizations(mockReq, mockRes);
    }

  } catch (error) {
    console.error('❌ Pagination test failed:', error);
  }
};

// Cleanup test data
const cleanupTestData = async () => {
  console.log('\n🧹 Cleaning up test data...');

  try {
    const result = await Organization.deleteMany({
      subdomain: { $regex: /^test-pending-/ }
    });

    console.log(`✅ Deleted ${result.deletedCount} test organizations`);
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
};

// Generate summary report
const generateSummary = async () => {
  console.log('\n📊 Summary Report:');

  try {
    const totalOrgs = await Organization.countDocuments();
    const pendingOrgs = await Organization.countDocuments({ status: 'pending' });
    const activeOrgs = await Organization.countDocuments({ status: 'active' });
    const godUsers = await User.countDocuments({
      systemPrivileges: { $elemMatch: { privilege: 'god_super_user' } }
    });

    console.log(`   📋 Total Organizations: ${totalOrgs}`);
    console.log(`   ⏳ Pending Organizations: ${pendingOrgs}`);
    console.log(`   ✅ Active Organizations: ${activeOrgs}`);
    console.log(`   👑 God Super Users: ${godUsers}`);

    if (pendingOrgs > 0) {
      console.log('\n✅ The endpoint should return data for god_super_user');
    } else {
      console.log('\n⚠️  No pending organizations found - endpoint will return empty results');
    }

  } catch (error) {
    console.error('❌ Summary generation failed:', error);
  }
};

// Main execution function
const main = async () => {
  console.log('🚀 Starting Pending Organizations Endpoint Test...');

  try {
    await connectDB();

    const testData = await createTestData();
    if (!testData) {
      console.log('❌ Failed to create test data. Exiting.');
      return;
    }

    const { godUser } = testData;

    await testControllerMethod(godUser);
    await testPrivilegeChecking();
    await testPagination(godUser);
    await generateSummary();

    // Ask user if they want to cleanup
    console.log('\n❓ Do you want to cleanup test data? (The script will keep it for manual testing)');
    console.log('   To cleanup manually, run: db.organizations.deleteMany({subdomain: /^test-pending-/})');

    console.log('\n🎉 Test completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Start your server: npm start');
    console.log('   2. Test the endpoint: GET /api/v1/organizations/pending');
    console.log('   3. Check Swagger docs: /api-docs');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n👋 Database connection closed');
  }
};

// Test the organization endpoints that are failing
const testOrganizationEndpoints = async () => {
  console.log('\n🧪 Testing organization endpoints...');

  try {
    // Find a user with organizations
    const userWithOrgs = await User.findOne({
      $or: [
        { 'roles.0': { $exists: true } },
        { organizations: { $exists: true, $ne: [] } }
      ]
    });

    if (!userWithOrgs) {
      console.log('❌ No user with organizations found');
      return;
    }

    console.log(`🔍 Testing with user: ${userWithOrgs.email}`);

    const OrganizationController = require('../api/v1/controllers/organizationController');
    const controller = new OrganizationController();

    // Test getMyOrganizations
    console.log('\n📋 Testing getMyOrganizations...');
    const mockReq1 = {
      user: { uid: userWithOrgs.firebase_uid, _id: userWithOrgs._id },
      ip: '127.0.0.1',
      get: () => 'test-user-agent'
    };

    const mockRes1 = {
      json: (data) => {
        console.log('✅ getMyOrganizations Response:');
        console.log(JSON.stringify(data, null, 2));
        return mockRes1;
      },
      status: (code) => {
        console.log(`📊 Status Code: ${code}`);
        return mockRes1;
      }
    };

    await controller.getMyOrganizations(mockReq1, mockRes1);

    // Test getCurrentOrganization
    console.log('\n🏢 Testing getCurrentOrganization...');
    const mockReq2 = {
      user: { uid: userWithOrgs.firebase_uid, _id: userWithOrgs._id },
      query: {},
      headers: {},
      body: {},
      ip: '127.0.0.1',
      get: () => 'test-user-agent'
    };

    const mockRes2 = {
      json: (data) => {
        console.log('✅ getCurrentOrganization Response:');
        console.log(JSON.stringify(data, null, 2));
        return mockRes2;
      },
      status: (code) => {
        console.log(`📊 Status Code: ${code}`);
        return mockRes2;
      }
    };

    await controller.getCurrentOrganization(mockReq2, mockRes2);

  } catch (error) {
    console.error('❌ Organization endpoints test failed:', error);
  }
};

// Run the script
if (require.main === module) {
  main().then(() => {
    return testOrganizationEndpoints();
  });
}

module.exports = { main, createTestData, testControllerMethod, testOrganizationEndpoints };
