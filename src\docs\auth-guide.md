# Authentication Guide for Auth Arsenal API

This guide explains the various authentication methods available in the Auth Arsenal API and how to use them.

## Authentication Methods

The API supports multiple authentication methods:

1. **Firebase Authentication**
   - Email/Password
   - Phone OTP
   - OAuth (Google, LinkedIn)

2. **Custom JWT Authentication**
   - For direct API integration

## Authentication Flow

### 1. Email/Password Authentication

#### Registration
```http
POST /api/v1/users/register
Content-Type: application/json

{
  "name": "John <PERSON>",
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "password": "SecurePassword123"
}
```

### 2. Phone OTP Authentication

#### Request OTP
```http
POST /api/v1/oauth/phone/send-code
Content-Type: application/json

{
  "phoneNumber": "+**********",
  "recaptchaToken": "your-recaptcha-token"
}
```

#### Verify OTP
```http
POST /api/v1/oauth/phone/verify-code
Content-Type: application/json

{
  "sessionInfo": "session-info-from-previous-response",
  "code": "123456"
}
```

### 3. OAuth Authentication

#### Initiate OAuth Flow
```http
GET /api/v1/oauth/google/login
```
or
```http
GET /api/v1/oauth/linkedin/login
```

The user will be redirected to the provider's consent page, and then back to the callback URL.

### 4. Email OTP Authentication

#### Request OTP
```http
POST /api/v1/oauth/email/send-otp
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### Verify OTP
```http
POST /api/v1/oauth/email/verify-otp
Content-Type: application/json

{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

## Using Authentication Tokens

After successful authentication, you will receive:

1. **Firebase ID Token** (`idToken`)
2. **Refresh Token** (`refreshToken`)

### Making Authenticated Requests

Include the Firebase ID token in the `Authorization` header:

```http
GET /api/v1/users/me
Authorization: Bearer your-firebase-id-token
```

### Refreshing Tokens

When the ID token expires, use the refresh token to get a new one:

```http
POST /api/v1/middleware/refreshToken-firebase
Content-Type: application/json

{
  "refreshToken": "your-refresh-token"
}
```

For JWT tokens:

```http
POST /api/v1/middleware/refreshToken-jwt
Content-Type: application/json

{
  "refreshToken": "your-jwt-refresh-token"
}
```

## Error Handling

Common authentication errors:

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 400 | `identifier_and_password_required` | Missing login credentials |
| 401 | `invalid_token` | Token is invalid or expired |
| 403 | `user_not_registered` | User exists in Firebase but not in MongoDB |
| 404 | `User not found` | User does not exist |

## Security Best Practices

1. Always use HTTPS for API requests
2. Store tokens securely (e.g., in HttpOnly cookies)
3. Implement token refresh before expiration
4. Validate user input to prevent injection attacks
5. Use strong passwords and encourage users to do the same

## Additional Resources

- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)
- [JWT.io](https://jwt.io/) - For debugging JWT tokens
- [OAuth 2.0 Documentation](https://oauth.net/2/)
