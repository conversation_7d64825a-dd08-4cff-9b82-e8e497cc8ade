// src/config/rateLimitConfig.js

/**
 * Rate Limiting Configuration
 * Centralized configuration for all rate limiting rules
 */
class RateLimitConfig {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  /**
   * Get rate limit configuration based on environment
   */
  getConfig() {
    const baseConfig = {
      // Redis configuration
      redis: {
        enabled: !!(process.env.REDIS_URL || process.env.REDIS_HOST),
        url: process.env.REDIS_URL,
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0,
        keyPrefix: process.env.REDIS_RATE_LIMIT_PREFIX || 'rl:'
      },

      // Global settings
      global: {
        enabled: process.env.RATE_LIMITING_ENABLED !== 'false',
        skipHealthChecks: true,
        skipGodUsers: process.env.SKIP_RATE_LIMIT_FOR_GOD_USERS === 'true',
        whitelistedIPs: process.env.RATE_LIMIT_WHITELIST_IPS?.split(',') || [],
        standardHeaders: true,
        legacyHeaders: false
      },

      // Rate limit rules
      limits: this.getLimits()
    };

    return baseConfig;
  }

  /**
   * Get rate limits based on environment
   */
  getLimits() {
    if (this.isDevelopment) {
      return this.getDevelopmentLimits();
    } else if (this.isProduction) {
      return this.getProductionLimits();
    } else {
      return this.getDefaultLimits();
    }
  }

  /**
   * Development environment limits (more lenient)
   */
  getDevelopmentLimits() {
    return {
      // General API access
      general: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxAuthenticated: 20000,   // 2000 requests per 15 min for auth users
        maxUnauthenticated: 5000   // 500 requests per 15 min for unauth users
      },

      // Authentication endpoints
      auth: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 20000                  // 20 login attempts per 15 minutes
      },

      // Registration
      registration: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 10000                 // 10 registration attempts per hour
      },

      // Password reset
      passwordReset: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 1000                   // 10 password reset attempts per hour
      },

      // Admin operations
      admin: {
        windowMs: 5 * 60 * 1000,  // 5 minutes
        max: 20000                // 200 admin operations per 5 minutes
      },

      // File uploads
      upload: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 10000                 // 100 uploads per hour
      },

      // Search operations
      search: {
        windowMs: 1 * 60 * 1000,  // 1 minute
        max: 6000                   // 60 searches per minute
      },

      // Organization creation
      organizationCreation: {
        windowMs: 24 * 60 * 60 * 1000, // 24 hours
        max: 1000                        // 10 organization creations per day
      },

      // API documentation access
      docs: {
        windowMs: 5 * 60 * 1000,  // 5 minutes
        max: 1000                 // 100 docs requests per 5 minutes
      }
    };
  }

  /**
   * Production environment limits (stricter)
   */
  getProductionLimits() {
    return {
      // General API access
      general: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxAuthenticated: 10000,   // 1000 requests per 15 min for auth users
        maxUnauthenticated: 1000   // 100 requests per 15 min for unauth users
      },

      // Authentication endpoints
      auth: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 1000                   // 10 login attempts per 15 minutes
      },

      // Registration
      registration: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 5000                    // 5 registration attempts per hour
      },

      // Password reset
      passwordReset: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 3000                    // 3 password reset attempts per hour
      },

      // Admin operations
      admin: {
        windowMs: 5 * 60 * 1000,  // 5 minutes
        max: 10000                 // 100 admin operations per 5 minutes
      },

      // File uploads
      upload: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 5000                   // 50 uploads per hour
      },

      // Search operations
      search: {
        windowMs: 1 * 60 * 1000,  // 1 minute
        max: 3000                   // 30 searches per minute
      },

      // Organization creation
      organizationCreation: {
        windowMs: 24 * 60 * 60 * 1000, // 24 hours
        max: 3000                        // 3 organization creations per day
      },

      // API documentation access
      docs: {
        windowMs: 5 * 60 * 1000,  // 5 minutes
        max: 50000                   // 50 docs requests per 5 minutes
      }
    };
  }

  /**
   * Default limits (fallback)
   */
  getDefaultLimits() {
    return this.getProductionLimits();
  }

  /**
   * Get custom limits for specific endpoints
   */
  getCustomLimits() {
    return {
      // Specific endpoint overrides
      endpoints: {
        '/api/v1/auth/login': 'auth',
        '/api/v1/auth/register': 'registration',
        '/api/v1/auth/forgot-password': 'passwordReset',
        '/api/v1/auth/reset-password': 'passwordReset',
        '/api/v1/organizations/create': 'organizationCreation',
        '/api/v1/upload/*': 'upload',
        '/api/v1/search/*': 'search',
        '/api/v1/admin/*': 'admin',
        '/api-docs*': 'docs',
        '/swagger*': 'docs'
      },

      // Path patterns that should use specific limits
      patterns: [
        { pattern: /^\/api\/v1\/auth\//, limit: 'auth' },
        { pattern: /^\/api\/v1\/admin\//, limit: 'admin' },
        { pattern: /^\/api\/v1\/upload\//, limit: 'upload' },
        { pattern: /^\/api\/v1\/search\//, limit: 'search' },
        { pattern: /^\/api-docs/, limit: 'docs' },
        { pattern: /^\/swagger/, limit: 'docs' }
      ]
    };
  }

  /**
   * Get rate limit configuration for a specific endpoint
   */
  getLimitForEndpoint(path) {
    const customLimits = this.getCustomLimits();
    const limits = this.getLimits();

    // Check exact endpoint matches
    if (customLimits.endpoints[path]) {
      const limitType = customLimits.endpoints[path];
      return limits[limitType];
    }

    // Check pattern matches
    for (const { pattern, limit } of customLimits.patterns) {
      if (pattern.test(path)) {
        return limits[limit];
      }
    }

    // Return general limit as default
    return limits.general;
  }

  /**
   * Get environment-specific message templates
   */
  getMessageTemplates() {
    return {
      general: {
        message: 'Too many requests',
        code: 'RATE_LIMIT_EXCEEDED',
        details: 'You have exceeded the rate limit. Please try again later.'
      },
      auth: {
        message: 'Too many authentication attempts',
        code: 'AUTH_RATE_LIMIT_EXCEEDED',
        details: 'Too many login attempts. Please try again later for security reasons.'
      },
      registration: {
        message: 'Too many registration attempts',
        code: 'REGISTRATION_RATE_LIMIT_EXCEEDED',
        details: 'Too many registration attempts. Please try again later.'
      },
      passwordReset: {
        message: 'Too many password reset attempts',
        code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
        details: 'Too many password reset attempts. Please try again later.'
      },
      admin: {
        message: 'Too many administrative requests',
        code: 'ADMIN_RATE_LIMIT_EXCEEDED',
        details: 'Too many administrative operations. Please slow down.'
      },
      upload: {
        message: 'Too many upload attempts',
        code: 'UPLOAD_RATE_LIMIT_EXCEEDED',
        details: 'Too many file uploads. Please try again later.'
      },
      search: {
        message: 'Too many search requests',
        code: 'SEARCH_RATE_LIMIT_EXCEEDED',
        details: 'Too many search requests. Please slow down your searches.'
      },
      organizationCreation: {
        message: 'Too many organization creation attempts',
        code: 'ORG_CREATION_RATE_LIMIT_EXCEEDED',
        details: 'You have reached the daily limit for creating organizations.'
      },
      docs: {
        message: 'Too many documentation requests',
        code: 'DOCS_RATE_LIMIT_EXCEEDED',
        details: 'Too many requests to documentation. Please slow down.'
      }
    };
  }

  /**
   * Validate configuration
   */
  validateConfig() {
    const config = this.getConfig();
    const errors = [];

    // Validate Redis configuration
    if (config.redis.enabled && !config.redis.url && !config.redis.host) {
      errors.push('Redis is enabled but no URL or host provided');
    }

    // Validate limits
    const limits = config.limits;
    for (const [key, limit] of Object.entries(limits)) {
      if (!limit.windowMs || !limit.max) {
        errors.push(`Invalid limit configuration for ${key}: missing windowMs or max`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
module.exports = new RateLimitConfig();
