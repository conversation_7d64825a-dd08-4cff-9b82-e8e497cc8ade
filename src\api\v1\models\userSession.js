const mongoose = require('mongoose');

const userSessionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  token: {
    type: String,
    required: true,
    index: true
  },
  deviceInfo: {
    userAgent: String,
    deviceType: String,
    platform: String,
    browser: String,
    ip: String
  },
  status: {
    type: String,
    enum: ['active', 'expired', 'revoked'],
    default: 'active',
    index: true
  },
  lastActive: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    required: true,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Compound indexes for common queries
userSessionSchema.index({ userId: 1, status: 1 });
userSessionSchema.index({ userId: 1, expiresAt: 1 });

// Add instance method to check if session is valid
userSessionSchema.methods.isValid = function() {
  return this.status === 'active' && new Date() < this.expiresAt;
};

module.exports = mongoose.model('UserSession', userSessionSchema); 