const nodemailer = require('nodemailer');
const Config = require('../models/Config'); 
let cachedTransporter = null;
async function getTransporter() {
  if (cachedTransporter) return cachedTransporter;

  const cfg = await Config.findOne(); 
  if (!cfg || !cfg.smtp) {
    throw new Error('SMTP settings are not configured');
  }

  cachedTransporter = nodemailer.createTransport({
    service: 'Gmail',  
    auth: {
      user: cfg.smtp.user, 
      pass: cfg.smtp.pass, 
    },
  });

  return cachedTransporter;
}

/**
 * Generates the HTML email body for OTP
 * @param {string} userName 
 * @param {string} otp 
 * @returns {string} 
 */
function generateOtpEmailBody(userName, otp) {
  return `
    <html>
      <body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
        <div style="background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); max-width: 600px; margin: auto;">
          <h2 style="color: #2C3E50;">Hello ${userName},</h2>
          <p style="font-size: 16px; color: #333333; line-height: 1.5;">Your one-time password (OTP) for logging in is:</p>
          <h3 style="color: #27AE60; font-size: 28px; font-weight: bold; text-align: center;">${otp}</h3>
          <p style="font-size: 16px; color: #333333; line-height: 1.5;">To proceed with your login, simply enter the OTP on the login page. Please note that the code will expire in 15 minutes.</p>
          <p style="font-size: 16px; color: #333333; line-height: 1.5;">If this wasn’t you, please feel free to ignore this message.</p>
          <p style="font-size: 16px; color: #333333; line-height: 1.5;">Best regards,<br><strong style="color: #27AE60;">From Diggi's</strong></p>
        </div>
      </body>
    </html>
  `;
}

/**
 * Generates the HTML email body for Signup
 * @param {string} userName 
 * @returns {string} 
 */
function welcomeEmailBody(userName) {
  return `
   <html>
  <body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
    <div style="background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); max-width: 600px; margin: auto;">
      <h2 style="color: #2C3E50;">🎉 Welcome, ${userName}!</h2>
      <p style="font-size: 16px; color: #333333; line-height: 1.5;">We’re thrilled to have you join our community at Diggi's’s platform. Your journey starts now, and we’re here to help you every step of the way!</p>
      <p style="font-size: 16px; color: #333333; line-height: 1.5;">Your registration is now complete. Please take a moment to explore our platform and get started with some exciting features.</p>
      
      <p style="font-size: 16px; color: #333333; line-height: 1.5;">Here’s what you can do next:</p>
      <ul style="font-size: 16px; color: #333333; line-height: 1.5;">
        <li>Check your profile and update your preferences.</li>
        <li>Explore our features and discover the best tools for you.</li>
        <li>Connect with our support team if you have any questions.</li>
      </ul>
      <p style="font-size: 16px; color: #333333; line-height: 1.5;">Best regards,<br><strong style="color: #27AE60;">From Diggi's</strong></p>
    </div>
  </body>
</html>  `;
}

/**
 * Send OTP email
 * @param {string} to 
 * @param {string} otp 
 * @param {string} subject 
 * @returns {Promise<boolean>}
 */
async function sendOtpEmail(to, otp, subject) {
  try {
    const transporter = await getTransporter();
    const userName = 'User';  
    const emailBody = generateOtpEmailBody(userName, otp); 

    const mailOptions = {
      from: process.env.SMTP_FROM,  
      to: to,
      subject: subject,
      html: emailBody,
    };

   
    const ccEmail = process.env.SMTP_CC || null;
    if (ccEmail) {
      mailOptions.cc = ccEmail; 
    }

    const info = await transporter.sendMail(mailOptions);
    console.log(`OTP sent successfully to ${to}: ${info.response}`);
    return true;
  } catch (error) {
    console.error("Error sending OTP email:", error);
    return false;
  }
}

/**
 * Send Signup email
 * @param {string} to - Recipient's email address
 * @param {string} userName - The name of the user to be displayed in the email
 * @returns {Promise<boolean>} - True if email sent successfully, otherwise false
 */
async function welcomeEmail(to, userName) {
  try {
    const transporter = await getTransporter();
    const emailBody = welcomeEmailBody(userName);  // Generate Signup email body

    const mailOptions = {
      from: process.env.SMTP_FROM,  
      to: to,
      subject: 'Welcome to Diggis Platform',
      html: emailBody,
    };

    const ccEmail = process.env.SMTP_CC || null;
    if (ccEmail) {
      mailOptions.cc = ccEmail;  
    }

    const info = await transporter.sendMail(mailOptions);
    console.log(`Signup email sent successfully to ${to}: ${info.response}`);
    return true;
  } catch (error) {
    console.error("Error sending Signup email:", error);
    return false;
  }
}


async function sendResetPasswordEmail(to, userName, link) {
  try {
    const transporter = await getTransporter();
    const html = `
      <html>
        <body style="font-family: Arial,sans-serif; background-color: #f4f4f4; padding: 20px;">
          <div style="background: #fff; padding: 25px; border-radius: 8px; max-width: 600px; margin: auto;">
            <h2 style="color: #2C3E50;">Hi ${userName || "User"},</h2>
            <p style="font-size: 16px;">You requested a password reset. Please click the link below to set a new password:</p>
            <a href="${link}" style="display: inline-block; margin: 18px 0; padding: 12px 22px; color: #fff; background: #27AE60; border-radius: 5px; text-decoration: none;">Reset Password</a>
            <p style="color: #888;">If you didn’t request this, you can safely ignore this email.<br>This link is valid for 1 hour.</p>
            <p style="font-size: 14px; color: #2C3E50;">Best regards,<br><b>Diggi's</b></p>
          </div>
        </body>
      </html>
    `;
    const mailOptions = {
      from: process.env.SMTP_FROM,
      to,
      subject: "Password Reset Request",
      html,
    };
    const info = await transporter.sendMail(mailOptions);
    console.log(`Reset email sent to ${to}: ${info.response}`);
    return true;
  } catch (err) {
    console.error("Error sending reset password email:", err);
    return false;
  }
}

module.exports = { getTransporter, sendOtpEmail, welcomeEmail,sendResetPasswordEmail };
