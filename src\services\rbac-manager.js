// services/rbac-manager.js
const RBACRole = require('../models/RBACRole');
const Organization = require('../models/Organization');
const User = require('../models/User');
const logger = require('./logger');
const { clearCaches } = require('./rbac-engine');

/**
 * Centralized RBAC Management Service
 * 
 * This service provides high-level operations for managing roles, permissions,
 * and access control using the centralized RBAC database.
 */
class RBACManager {
  constructor() {
    this.serviceName = 'rbac-manager';
  }

  /**
   * Initialize system roles for an organization
   * @param {String|ObjectId} organizationId - Organization ID
   * @param {String|ObjectId} createdBy - User ID who is creating the roles
   * @returns {Object} Result with created roles
   */
  async initializeSystemRoles(organizationId, createdBy = null) {
    const correlationId = logger.generateCorrelationId();
    
    logger.info('Initializing system roles for organization', {
      component: this.serviceName,
      operation: 'initialize_system_roles',
      correlation_id: correlationId,
      metadata: {
        organization_id: organizationId,
        created_by: createdBy
      }
    });

    try {
      // Verify organization exists
      const organization = await Organization.findById(organizationId);
      if (!organization) {
        throw new Error(`Organization with ID ${organizationId} not found`);
      }

      const systemRoleTemplates = RBACRole.getSystemRoles();
      const createdRoles = [];
      const skippedRoles = [];

      for (const roleTemplate of systemRoleTemplates) {
        // Skip system-scoped roles (they are global)
        if (roleTemplate.scope === 'system') continue;

        // Check if role already exists
        const existingRole = await RBACRole.findOne({
          name: roleTemplate.name,
          scope: 'organization',
          organization: organizationId
        });

        if (!existingRole) {
          const roleData = {
            ...roleTemplate,
            organization: organizationId,
            audit: {
              createdBy: createdBy,
              createdAt: new Date(),
              usageCount: 0,
              changeHistory: [{
                action: 'created',
                timestamp: new Date(),
                performedBy: createdBy,
                reason: 'System role initialization'
              }]
            }
          };

          const newRole = await RBACRole.create(roleData);
          createdRoles.push({
            id: newRole._id,
            name: newRole.name,
            displayName: newRole.displayName,
            level: newRole.hierarchy.level
          });

          logger.info('System role created', {
            component: this.serviceName,
            operation: 'initialize_system_roles',
            correlation_id: correlationId,
            metadata: {
              role_id: newRole._id,
              role_name: newRole.name,
              organization_id: organizationId
            }
          });
        } else {
          skippedRoles.push({
            id: existingRole._id,
            name: existingRole.name,
            reason: 'already_exists'
          });
        }
      }

      // Clear caches to ensure fresh data
      clearCaches();

      return {
        success: true,
        data: {
          organization_id: organizationId,
          organization_name: organization.name,
          roles_created: createdRoles,
          roles_skipped: skippedRoles,
          total_created: createdRoles.length,
          total_skipped: skippedRoles.length
        }
      };

    } catch (error) {
      logger.error('Failed to initialize system roles', error, {
        component: this.serviceName,
        operation: 'initialize_system_roles',
        correlation_id: correlationId,
        metadata: {
          organization_id: organizationId,
          created_by: createdBy
        }
      });

      return {
        success: false,
        error: {
          code: 'SYSTEM_ROLE_INITIALIZATION_FAILED',
          message: 'Failed to initialize system roles',
          details: error.message
        }
      };
    }
  }

  /**
   * Create a custom role
   * @param {Object} roleData - Role data
   * @param {String|ObjectId} createdBy - User ID who is creating the role
   * @returns {Object} Result with created role
   */
  async createCustomRole(roleData, createdBy) {
    const correlationId = logger.generateCorrelationId();
    
    logger.info('Creating custom role', {
      component: this.serviceName,
      operation: 'create_custom_role',
      correlation_id: correlationId,
      metadata: {
        role_name: roleData.name,
        organization_id: roleData.organization,
        created_by: createdBy
      }
    });

    try {
      // Validate required fields
      if (!roleData.name || !roleData.displayName || !roleData.description) {
        throw new Error('Missing required fields: name, displayName, description');
      }

      // Check if role already exists
      const existingRole = await RBACRole.findOne({
        name: roleData.name,
        scope: roleData.scope || 'organization',
        organization: roleData.organization
      });

      if (existingRole) {
        return {
          success: false,
          error: {
            code: 'ROLE_ALREADY_EXISTS',
            message: 'Role with this name already exists',
            details: `Role '${roleData.name}' already exists in this scope`
          }
        };
      }

      // Create role with audit information
      const newRoleData = {
        ...roleData,
        scope: roleData.scope || 'organization',
        hierarchy: roleData.hierarchy || { level: 10, type: 'custom' },
        metadata: {
          ...roleData.metadata,
          isActive: true,
          isSystem: false,
          isBuiltIn: false,
          category: roleData.metadata?.category || 'custom'
        },
        audit: {
          createdBy: createdBy,
          createdAt: new Date(),
          usageCount: 0,
          changeHistory: [{
            action: 'created',
            timestamp: new Date(),
            performedBy: createdBy,
            reason: 'Custom role creation',
            changes: {
              permissions: roleData.permissions || []
            }
          }]
        }
      };

      const newRole = await RBACRole.create(newRoleData);

      // Clear caches
      clearCaches();

      logger.info('Custom role created successfully', {
        component: this.serviceName,
        operation: 'create_custom_role',
        correlation_id: correlationId,
        metadata: {
          role_id: newRole._id,
          role_name: newRole.name,
          organization_id: newRole.organization
        }
      });

      return {
        success: true,
        data: {
          role_id: newRole._id,
          name: newRole.name,
          displayName: newRole.displayName,
          description: newRole.description,
          scope: newRole.scope,
          hierarchy: newRole.hierarchy,
          permissions: newRole.permissions,
          created_at: newRole.audit.createdAt
        }
      };

    } catch (error) {
      logger.error('Failed to create custom role', error, {
        component: this.serviceName,
        operation: 'create_custom_role',
        correlation_id: correlationId,
        metadata: {
          role_name: roleData.name,
          created_by: createdBy
        }
      });

      return {
        success: false,
        error: {
          code: 'CUSTOM_ROLE_CREATION_FAILED',
          message: 'Failed to create custom role',
          details: error.message
        }
      };
    }
  }

  /**
   * Update role permissions
   * @param {String|ObjectId} roleId - Role ID
   * @param {Array} permissions - New permissions array
   * @param {String|ObjectId} modifiedBy - User ID who is modifying the role
   * @returns {Object} Result with updated role
   */
  async updateRolePermissions(roleId, permissions, modifiedBy) {
    const correlationId = logger.generateCorrelationId();
    
    logger.info('Updating role permissions', {
      component: this.serviceName,
      operation: 'update_role_permissions',
      correlation_id: correlationId,
      metadata: {
        role_id: roleId,
        permissions_count: permissions.length,
        modified_by: modifiedBy
      }
    });

    try {
      const role = await RBACRole.findById(roleId);
      if (!role) {
        return {
          success: false,
          error: {
            code: 'ROLE_NOT_FOUND',
            message: 'Role not found',
            details: `Role with ID ${roleId} does not exist`
          }
        };
      }

      // Check if role is built-in (cannot be modified)
      if (role.metadata.isBuiltIn) {
        return {
          success: false,
          error: {
            code: 'BUILT_IN_ROLE_MODIFICATION_DENIED',
            message: 'Built-in roles cannot be modified',
            details: 'System built-in roles are read-only'
          }
        };
      }

      const oldPermissions = [...role.permissions];
      
      // Update permissions
      role.permissions = permissions;
      role.audit.lastModified = new Date();
      role.audit.modifiedBy = modifiedBy;
      
      // Add change history
      role.audit.changeHistory.push({
        action: 'updated',
        timestamp: new Date(),
        performedBy: modifiedBy,
        reason: 'Permission update',
        changes: {
          old_permissions: oldPermissions,
          new_permissions: permissions
        }
      });

      await role.save();

      // Clear caches
      clearCaches();

      logger.info('Role permissions updated successfully', {
        component: this.serviceName,
        operation: 'update_role_permissions',
        correlation_id: correlationId,
        metadata: {
          role_id: roleId,
          role_name: role.name,
          old_permissions_count: oldPermissions.length,
          new_permissions_count: permissions.length
        }
      });

      return {
        success: true,
        data: {
          role_id: role._id,
          name: role.name,
          permissions: role.permissions,
          last_modified: role.audit.lastModified,
          modified_by: modifiedBy
        }
      };

    } catch (error) {
      logger.error('Failed to update role permissions', error, {
        component: this.serviceName,
        operation: 'update_role_permissions',
        correlation_id: correlationId,
        metadata: {
          role_id: roleId,
          modified_by: modifiedBy
        }
      });

      return {
        success: false,
        error: {
          code: 'ROLE_PERMISSION_UPDATE_FAILED',
          message: 'Failed to update role permissions',
          details: error.message
        }
      };
    }
  }

  /**
   * Get roles for an organization with hierarchy
   * @param {String|ObjectId} organizationId - Organization ID
   * @param {Object} options - Query options
   * @returns {Object} Result with roles
   */
  async getOrganizationRoles(organizationId, options = {}) {
    const correlationId = logger.generateCorrelationId();
    
    try {
      const {
        includeInactive = false,
        sortBy = 'hierarchy.level',
        sortOrder = 1,
        page = 1,
        limit = 50
      } = options;

      const query = {
        $or: [
          { scope: 'system' },
          { scope: 'global' },
          { scope: 'organization', organization: organizationId }
        ]
      };

      if (!includeInactive) {
        query['metadata.isActive'] = true;
      }

      const skip = (page - 1) * limit;
      
      const [roles, total] = await Promise.all([
        RBACRole.find(query)
          .sort({ [sortBy]: sortOrder })
          .skip(skip)
          .limit(limit)
          .populate('organization', 'name subdomain'),
        RBACRole.countDocuments(query)
      ]);

      return {
        success: true,
        data: {
          roles: roles.map(role => ({
            id: role._id,
            name: role.name,
            displayName: role.displayName,
            description: role.description,
            scope: role.scope,
            hierarchy: role.hierarchy,
            permissions: role.permissions,
            metadata: role.metadata,
            organization: role.organization,
            usage_count: role.audit.usageCount,
            last_used: role.audit.lastUsed
          })),
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      };

    } catch (error) {
      logger.error('Failed to get organization roles', error, {
        component: this.serviceName,
        operation: 'get_organization_roles',
        correlation_id: correlationId,
        metadata: {
          organization_id: organizationId
        }
      });

      return {
        success: false,
        error: {
          code: 'GET_ORGANIZATION_ROLES_FAILED',
          message: 'Failed to retrieve organization roles',
          details: error.message
        }
      };
    }
  }

  /**
   * Delete a custom role
   * @param {String|ObjectId} roleId - Role ID
   * @param {String|ObjectId} deletedBy - User ID who is deleting the role
   * @returns {Object} Result
   */
  async deleteRole(roleId, deletedBy) {
    const correlationId = logger.generateCorrelationId();
    
    logger.info('Deleting role', {
      component: this.serviceName,
      operation: 'delete_role',
      correlation_id: correlationId,
      metadata: {
        role_id: roleId,
        deleted_by: deletedBy
      }
    });

    try {
      const role = await RBACRole.findById(roleId);
      if (!role) {
        return {
          success: false,
          error: {
            code: 'ROLE_NOT_FOUND',
            message: 'Role not found',
            details: `Role with ID ${roleId} does not exist`
          }
        };
      }

      // Check if role is built-in or system role (cannot be deleted)
      if (role.metadata.isBuiltIn || role.metadata.isSystem) {
        return {
          success: false,
          error: {
            code: 'SYSTEM_ROLE_DELETION_DENIED',
            message: 'System and built-in roles cannot be deleted',
            details: 'Only custom roles can be deleted'
          }
        };
      }

      // Check if role is in use by any users
      const usersWithRole = await User.countDocuments({
        'roles.role': roleId
      });

      if (usersWithRole > 0) {
        return {
          success: false,
          error: {
            code: 'ROLE_IN_USE',
            message: 'Role is currently assigned to users',
            details: `Role is assigned to ${usersWithRole} user(s). Remove role assignments before deletion.`
          }
        };
      }

      await RBACRole.findByIdAndDelete(roleId);

      // Clear caches
      clearCaches();

      logger.info('Role deleted successfully', {
        component: this.serviceName,
        operation: 'delete_role',
        correlation_id: correlationId,
        metadata: {
          role_id: roleId,
          role_name: role.name,
          deleted_by: deletedBy
        }
      });

      return {
        success: true,
        data: {
          role_id: roleId,
          role_name: role.name,
          deleted_at: new Date()
        }
      };

    } catch (error) {
      logger.error('Failed to delete role', error, {
        component: this.serviceName,
        operation: 'delete_role',
        correlation_id: correlationId,
        metadata: {
          role_id: roleId,
          deleted_by: deletedBy
        }
      });

      return {
        success: false,
        error: {
          code: 'ROLE_DELETION_FAILED',
          message: 'Failed to delete role',
          details: error.message
        }
      };
    }
  }
}

module.exports = new RBACManager();
