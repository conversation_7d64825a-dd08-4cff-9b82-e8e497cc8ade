# Auth0 + Subdomain Integration Guide

## Overview

This guide explains how to set up Auth0 authentication with your existing subdomain functionality for digimeet.live. The integration allows each organization to have its own Auth0 authentication context while maintaining a unified user database.

## 🎯 **Features**

✅ **Subdomain-specific Auth0 configuration**
✅ **Automatic user sync between Auth0 and local database**
✅ **Organization context in Auth0 tokens**
✅ **Hybrid authentication (Auth0 + existing Firebase/JWT)**
✅ **Multi-tenant session management**
✅ **Seamless integration with existing RBAC system**

## 🔧 **Setup Steps**

### 1. Auth0 Configuration

#### Create Auth0 Application
1. Go to [Auth0 Dashboard](https://manage.auth0.com/)
2. Create a new **Regular Web Application**
3. Configure the following settings:

**Application Settings:**
```
Name: DigiMeet Multi-Tenant App
Application Type: Regular Web Application
```

**Application URIs:**
```
Allowed Callback URLs:
https://digimeet.live/callback
https://*.digimeet.live/callback

Allowed Logout URLs:
https://digimeet.live/
https://*.digimeet.live/

Allowed Web Origins:
https://digimeet.live
https://*.digimeet.live

Allowed Origins (CORS):
https://digimeet.live
https://*.digimeet.live
```

#### Configure Auth0 Rules/Actions
Create an Auth0 Action to add organization context:

```javascript
exports.onExecutePostLogin = async (event, api) => {
  const { request } = event;
  
  // Extract subdomain from the request
  const host = request.hostname;
  const subdomain = host.split('.')[0];
  
  // Add custom claims
  if (subdomain && subdomain !== 'digimeet') {
    api.idToken.setCustomClaim('https://digimeet.live/subdomain', subdomain);
    api.accessToken.setCustomClaim('https://digimeet.live/subdomain', subdomain);
  }
  
  // Add organization context
  api.idToken.setCustomClaim('https://digimeet.live/organization', {
    subdomain: subdomain,
    domain: host
  });
};
```

### 2. Environment Variables

Update your `.env` file with Auth0 credentials:

```env
# Auth0 Configuration
AUTH0_SECRET=your_32_character_secret_key_here
AUTH0_CLIENT_ID=your_auth0_client_id
AUTH0_ISSUER_BASE_URL=https://your-tenant.auth0.com
AUTH0_BASE_URL=https://digimeet.live
AUTH0_AUDIENCE=https://digimeet.live/api
```

**Generate AUTH0_SECRET:**
```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### 3. Vercel Environment Variables

Add the same environment variables to your Vercel project:

1. Go to Vercel Dashboard → Your Project → Settings → Environment Variables
2. Add all Auth0 environment variables
3. Make sure they match your `.env` file exactly

### 4. DNS Configuration (if not done)

Ensure wildcard DNS is configured:
```
A Record: digimeet.live → Your server IP
CNAME Record: *.digimeet.live → digimeet.live
```

## 🧪 **Testing the Integration**

### Test Endpoints

1. **Auth0 Status:**
   ```bash
   curl https://digimeet.live/api/v1/auth0/status
   curl https://test.digimeet.live/api/v1/auth0/status
   ```

2. **Get Login URL:**
   ```bash
   curl https://test.digimeet.live/api/v1/auth0/login-url
   ```

3. **Protected Route:**
   ```bash
   curl https://test.digimeet.live/api/v1/auth0/protected
   ```

### Frontend Integration

**Get Auth0 login URL for current subdomain:**
```javascript
fetch('/api/v1/auth0/login-url')
  .then(res => res.json())
  .then(data => {
    window.location.href = data.loginUrl;
  });
```

**Check authentication status:**
```javascript
fetch('/api/v1/auth0/profile')
  .then(res => res.json())
  .then(data => {
    if (data.isAuthenticated) {
      console.log('User:', data.auth0User);
      console.log('Organization:', data.organization);
    }
  });
```

## 🔄 **How It Works**

### 1. Subdomain Detection
- Subdomain middleware extracts organization context
- Auth0 config middleware generates subdomain-specific configuration
- Each subdomain gets its own Auth0 session

### 2. User Sync
- When user logs in via Auth0, middleware syncs with local database
- Creates new user if doesn't exist
- Updates existing user with Auth0 profile data
- Maintains organization membership

### 3. Hybrid Authentication
- Supports both Auth0 and existing Firebase/JWT authentication
- Seamless fallback between authentication methods
- Maintains compatibility with existing API endpoints

### 4. Session Management
- Subdomain-specific session cookies
- Secure cross-subdomain session handling
- Automatic session renewal

## 🛡️ **Security Features**

- **Subdomain Isolation:** Each organization has isolated Auth0 sessions
- **CORS Protection:** Wildcard CORS with subdomain validation
- **Token Validation:** Auth0 tokens validated per subdomain
- **Organization Context:** Users automatically associated with organizations

## 📋 **API Endpoints**

| Endpoint | Description | Auth Required |
|----------|-------------|---------------|
| `GET /api/v1/auth0/status` | Get Auth0 status | No |
| `GET /api/v1/auth0/profile` | Get user profile | Yes |
| `GET /api/v1/auth0/login-url` | Get login URL | No |
| `GET /api/v1/auth0/logout-url` | Get logout URL | No |
| `GET /api/v1/auth0/protected` | Protected route example | Yes |

## 🔧 **Configuration Options**

### Per-Organization Customization

You can customize Auth0 behavior per organization by modifying the `auth0Config.js`:

```javascript
// Custom branding per organization
if (organization.branding) {
  config.authorizationParams.branding = organization.branding;
}

// Custom redirect URLs
config.routes.postLogoutRedirect = `https://${subdomain}.digimeet.live/dashboard`;
```

### Advanced Features

1. **Custom Claims:** Add organization-specific data to tokens
2. **Role Mapping:** Map Auth0 roles to local RBAC system
3. **SSO Integration:** Connect with enterprise identity providers
4. **Multi-Factor Authentication:** Enable MFA per organization

## 🚀 **Deployment**

1. **Commit Changes:**
   ```bash
   git add .
   git commit -m "feat: Add Auth0 integration with subdomain support"
   git push
   ```

2. **Configure Vercel Environment Variables**
3. **Test on Production Subdomains**
4. **Update Auth0 Application Settings**

## 🔍 **Troubleshooting**

### Common Issues

1. **Callback URL Mismatch:**
   - Ensure wildcard URLs are configured in Auth0
   - Check Vercel domain configuration

2. **Session Issues:**
   - Verify AUTH0_SECRET is 32+ characters
   - Check cookie domain settings

3. **CORS Errors:**
   - Ensure wildcard CORS is enabled
   - Verify subdomain patterns match

### Debug Commands

```bash
# Check Auth0 configuration
curl https://digimeet.live/debug/env | grep AUTH0

# Test subdomain extraction
curl https://test.digimeet.live/debug/subdomain

# Check Auth0 status
curl https://test.digimeet.live/api/v1/auth0/status
```

## 📚 **Next Steps**

1. **Frontend Integration:** Update your frontend to use Auth0 login URLs
2. **Role Mapping:** Configure Auth0 roles to map to your RBAC system
3. **SSO Setup:** Connect enterprise identity providers
4. **Monitoring:** Set up Auth0 logs and monitoring
5. **Testing:** Create comprehensive test suite for multi-tenant auth

## 🎉 **Benefits**

- **Scalable:** Each organization gets isolated authentication
- **Secure:** Industry-standard Auth0 security
- **Flexible:** Supports multiple authentication methods
- **Maintainable:** Clean separation of concerns
- **User-Friendly:** Seamless subdomain-based authentication
