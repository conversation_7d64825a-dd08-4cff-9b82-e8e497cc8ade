# 🔐 Password Protection for Documentation Access

## Overview

The Auth Arsenal API now includes password protection for accessing the main documentation pages. This adds an extra layer of security to prevent unauthorized access to your API documentation.

## Protected Endpoints

The following endpoints are now password-protected:

- **`/`** - Main welcome page
- **`/api-docs`** - Custom API documentation

## Unprotected Endpoints

These endpoints remain publicly accessible:

- **`/api/*`** - All API endpoints (protected by their own authentication)
- **`/health`** - Health check endpoint
- **`/login`** - Login page
- **`/logout`** - Logout functionality
- **`/swagger.json`** - OpenAPI specification (for API clients)

## How It Works

### 1. **Session-Based Authentication**
- Uses `express-session` for secure session management
- Sessions expire after 24 hours
- Secure cookies in production environment

### 2. **Simple Login Flow**
1. User visits protected page (e.g., `https://auth-03-0.vercel.app/`)
2. If not authenticated, redirected to `/login`
3. User enters the access password
4. On successful authentication, redirected to original page
5. Session maintained for 24 hours

### 3. **Environment-Specific Passwords**

#### Local Development
- **Password**: `admin123`
- **Session Secret**: `your-super-secret-session-key-change-this-in-production`

#### Production
- **Password**: `SecurePassword2024!` (from your .env selection)
- **Session Secret**: `production-super-secret-session-key-very-secure-2024`

## Environment Variables

### Required Variables

```bash
# Documentation Access Protection
DOCS_ACCESS_PASSWORD=your-secure-password-here
SESSION_SECRET=your-super-secret-session-key-here
```

### Vercel Environment Variables

The following environment variables have been configured in Vercel:

```bash
DOCS_ACCESS_PASSWORD=SecurePassword2024!
SESSION_SECRET=production-super-secret-session-key-very-secure-2024
```

## Usage Instructions

### For End Users

1. **Access the Documentation**:
   - Visit `https://auth-03-0.vercel.app/`
   - You'll be redirected to the login page

2. **Login**:
   - Enter the access password: `SecurePassword2024!`
   - Click "Access Documentation"

3. **Navigate Freely**:
   - Once logged in, you can access all protected pages
   - Session lasts for 24 hours

4. **Logout**:
   - Click the "🚪 Logout" button in the top-right corner
   - Or visit `/logout` directly

### For Developers

#### Local Development

```bash
# Switch to local environment
npm run env:local

# Start development server
npm run dev

# Access with password: admin123
```

#### Production Testing

```bash
# Switch to production environment
npm run env:production

# Start production server
npm run start

# Access with password: SecurePassword2024!
```

## Security Features

### 1. **Secure Session Management**
- HTTP-only cookies in production
- Secure flag enabled for HTTPS
- Session expiration after 24 hours
- CSRF protection through session-based authentication

### 2. **Environment Separation**
- Different passwords for development and production
- Secure session secrets
- Environment-specific configuration

### 3. **Graceful Redirects**
- Automatic redirect to login page
- Return to original page after authentication
- Clean logout functionality

## Customization

### Change Password

#### For Local Development
Update `.env.local`:
```bash
DOCS_ACCESS_PASSWORD=your-new-password
```

#### For Production
Update Vercel environment variable:
```bash
vercel env rm DOCS_ACCESS_PASSWORD production
echo "your-new-secure-password" | vercel env add DOCS_ACCESS_PASSWORD production
vercel --prod  # Redeploy
```

### Change Session Secret

```bash
# Generate a secure session secret
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# Update environment variable
vercel env rm SESSION_SECRET production
echo "your-new-session-secret" | vercel env add SESSION_SECRET production
```

### Customize Login Page

The login page is defined in `src/index.js` in the `/login` route. You can customize:

- **Styling**: Update the CSS in the `loginHtml` template
- **Branding**: Change the title, logo, and colors
- **Validation**: Add additional security measures

## API Integration

### Bypass for API Clients

API endpoints (`/api/*`) are not affected by this protection and continue to use their own authentication mechanisms:

- **JWT Authentication**: For API access
- **Firebase Authentication**: For user management
- **RBAC**: For permission-based access control

### Health Checks

The `/health` endpoint remains unprotected for monitoring and load balancer health checks.

## Troubleshooting

### Common Issues

1. **"Invalid password" Error**
   - Verify the correct password for your environment
   - Check environment variables are set correctly

2. **Session Not Persisting**
   - Ensure cookies are enabled in browser
   - Check if HTTPS is properly configured in production

3. **Redirect Loop**
   - Clear browser cookies and try again
   - Verify session secret is properly set

### Debug Mode

To debug session issues, you can temporarily add logging:

```javascript
// In src/index.js, add to the simpleAuth middleware
console.log('Session:', req.session);
console.log('Authenticated:', req.session?.authenticated);
```

## Security Considerations

1. **Use Strong Passwords**: Ensure production passwords are complex and unique
2. **Regular Rotation**: Change passwords periodically
3. **Monitor Access**: Check logs for unauthorized access attempts
4. **HTTPS Only**: Always use HTTPS in production for secure cookie transmission
5. **Environment Separation**: Keep development and production credentials separate

## Files Modified

- `src/index.js` - Added session middleware and authentication logic
- `src/docs/welcome.html` - Added logout button
- `.env`, `.env.local`, `.env.production` - Added password and session configuration
- `package.json` - Already included `express-session` dependency

## Next Steps

1. **Test the Implementation**: Verify login/logout functionality works correctly
2. **Monitor Usage**: Check logs for any authentication issues
3. **Update Documentation**: Inform team members of the new access requirements
4. **Consider Enhancements**: Add features like password reset, user management, etc.

---

**🔒 Your documentation is now secure!** Users must enter the correct password to access the main documentation pages while keeping API endpoints available for legitimate API clients.
