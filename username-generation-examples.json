{"description": "Username Generation Examples and Test Cases", "implementation": "Auto-generated unique usernames based on user's full name", "format": "{processed-name}-{unique-suffix}", "features": ["URL-safe format (lowercase letters, numbers, hyphens, underscores only)", "Maximum 50 characters", "Must start with a letter", "Guaranteed uniqueness across all users", "<PERSON>les special characters, accents, and edge cases", "Random alphanumeric suffix for uniqueness"], "examples": [{"input": "<PERSON>", "processed": "john-doe", "possible_outputs": ["john-doe-a7x9", "john-doe-k2m8", "john-doe-p5q3"], "description": "Standard name with space"}, {"input": "<PERSON>", "processed": "maria-garcia-lopez", "possible_outputs": ["maria-garcia-lopez-x9k2", "maria-garcia-lopez-m4n7", "maria-garcia-lopez-q8r5"], "description": "Name with accents and hyphens"}, {"input": "<PERSON>", "processed": "alex-smith", "possible_outputs": ["alex-smith-001", "alex-smith-b3x7", "alex-smith-n9m4"], "description": "Simple name, may use sequential or random suffix"}, {"input": "<PERSON><PERSON><PERSON>", "processed": "jean-pierre-o-connor", "possible_outputs": ["jean-pierre-<PERSON>-connor-z2k8", "jean-pierre-o-connor-a5m9", "jean-pierre-<PERSON>-connor-p7q3"], "description": "Name with apostrophe and hyphen"}, {"input": "<PERSON>小明", "processed": "user", "possible_outputs": ["user-x8k2m9", "user-a5n7p3", "user-q9r4t6"], "description": "Non-Latin characters fallback to 'user'"}, {"input": "", "processed": "user", "possible_outputs": ["user-m3k8x2", "user-p7q9n4", "user-z5r8a6"], "description": "Empty name fallback"}, {"input": "Dr. <PERSON>.", "processed": "dr-robert-johnson-jr", "possible_outputs": ["dr-robert-johnson-jr-k8m3", "dr-robert-johnson-jr-x2n7", "dr-robert-johnson-jr-q5p9"], "description": "Name with titles and suffixes"}, {"input": "<PERSON><PERSON><PERSON>", "processed": "anna-maria-muller-schmidt", "possible_outputs": ["anna-maria-muller-schmidt-a7k2", "anna-maria-muller-schmidt-m9x5", "anna-maria-muller-schmidt-p3q8"], "description": "German name with umlauts"}], "edge_cases": [{"case": "Very long name", "input": "<PERSON>-<PERSON><PERSON><PERSON>", "processed": "christopher-alexander-mont<PERSON><PERSON>y", "note": "Truncated to 35 characters to leave room for suffix"}, {"case": "Numbers in name", "input": "<PERSON> 2nd", "processed": "john-<PERSON>e-2nd", "note": "Numbers are preserved"}, {"case": "Special characters", "input": "<PERSON>@Doe#123!", "processed": "john-doe-123", "note": "Special characters replaced with hyphens"}, {"case": "Starting with number", "input": "123 John", "processed": "john", "note": "Leading numbers removed to ensure username starts with letter"}, {"case": "Only special characters", "input": "@#$%^&*()", "processed": "user", "note": "Falls back to 'user' when no valid characters"}], "uniqueness_strategy": {"primary": "Random alphanumeric suffix (4 characters)", "fallback": "Sequential numbers with zero-padding (001, 002, etc.)", "attempts": "Up to 10 random attempts before falling back to sequential", "collision_handling": "Automatic retry with new suffix"}, "api_integration": {"registration_endpoint": "POST /api/v1/users/register", "auto_generation": "Username is automatically generated during registration", "field_name": "user_name", "validation": "Unique constraint enforced at database level", "response_includes": "Generated username returned in registration response"}, "database_schema": {"field": "user_name", "type": "String", "required": true, "unique": true, "trim": true, "lowercase": true, "maxlength": 50, "pattern": "^[a-z][a-z0-9_-]*$", "index": "Unique index for performance"}, "test_scenarios": [{"scenario": "New user registration", "request": {"name": "<PERSON>", "email": "<EMAIL>", "password": "SecurePass123"}, "expected_response": {"message": "user_registered", "user": {"name": "<PERSON>", "user_name": "john-doe-a7x9", "email": "<EMAIL>"}}}, {"scenario": "Duplicate name handling", "description": "Multiple users with same name get different usernames", "users": [{"name": "<PERSON>", "username": "john-smith-k2m8"}, {"name": "<PERSON>", "username": "john-smith-x9p5"}, {"name": "<PERSON>", "username": "john-smith-a3q7"}]}]}