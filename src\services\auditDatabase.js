// src/services/auditDatabase.js
const mongoose = require('mongoose');
const logger = require('./logger');

/**
 * Separate MongoDB connection for audit logging
 * Provides isolation and performance benefits for audit data
 */
class AuditDatabase {
  constructor() {
    this.connection = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000; // 5 seconds
  }

  /**
   * Initialize audit database connection
   */
  async connect() {
    try {
      const auditDbUri = process.env.AUDIT_MONGO_URI ||
        process.env.MONGO_URI?.replace(/\/[^\/]*$/, '/audit_logs_db') ||
        'mongodb://localhost:27017/audit_logs_db';

      logger.info('Connecting to audit database', {
        component: 'audit-database',
        operation: 'connect',
        metadata: { uri: auditDbUri.replace(/\/\/.*@/, '//***:***@') }
      });

      // Create separate connection for audit logs
      this.connection = mongoose.createConnection(auditDbUri, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferCommands: false
      });

      // Connection event handlers
      this.connection.on('connected', () => {
        this.isConnected = true;
        this.connectionAttempts = 0;
        logger.info('Audit database connected successfully', {
          component: 'audit-database',
          operation: 'connected'
        });
      });

      this.connection.on('error', (error) => {
        this.isConnected = false;
        logger.error('Audit database connection error', {
          component: 'audit-database',
          operation: 'connection_error',
          error: error.message
        });
      });

      this.connection.on('disconnected', () => {
        this.isConnected = false;
        logger.warn('Audit database disconnected', {
          component: 'audit-database',
          operation: 'disconnected'
        });

        // Attempt to reconnect
        this.reconnect();
      });

      // Wait for initial connection
      await new Promise((resolve, reject) => {
        this.connection.once('open', resolve);
        this.connection.once('error', reject);

        // Timeout after 10 seconds
        setTimeout(() => reject(new Error('Connection timeout')), 10000);
      });

      return this.connection;
    } catch (error) {
      logger.error('Failed to connect to audit database', {
        component: 'audit-database',
        operation: 'connect_failed',
        error: error.message,
        metadata: { attempt: this.connectionAttempts + 1 }
      });

      this.connectionAttempts++;

      if (this.connectionAttempts < this.maxRetries) {
        logger.info(`Retrying audit database connection in ${this.retryDelay}ms`, {
          component: 'audit-database',
          operation: 'retry_connection',
          metadata: { attempt: this.connectionAttempts, maxRetries: this.maxRetries }
        });

        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        return this.connect();
      } else {
        logger.error('Max connection attempts reached for audit database', {
          component: 'audit-database',
          operation: 'max_retries_reached',
          metadata: { attempts: this.connectionAttempts }
        });
        throw error;
      }
    }
  }

  /**
   * Reconnect to audit database
   */
  async reconnect() {
    if (this.connectionAttempts >= this.maxRetries) {
      logger.error('Max reconnection attempts reached for audit database', {
        component: 'audit-database',
        operation: 'max_reconnect_attempts'
      });
      return;
    }

    try {
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      await this.connect();
    } catch (error) {
      logger.error('Audit database reconnection failed', {
        component: 'audit-database',
        operation: 'reconnect_failed',
        error: error.message
      });
    }
  }

  /**
   * Get audit database connection
   */
  getConnection() {
    if (!this.connection || !this.isConnected) {
      logger.warn('Audit database connection not available', {
        component: 'audit-database',
        operation: 'get_connection_unavailable'
      });
      return null;
    }
    return this.connection;
  }

  /**
   * Check if audit database is connected
   */
  isAuditDbConnected() {
    return this.isConnected && this.connection && this.connection.readyState === 1;
  }

  /**
   * Close audit database connection
   */
  async close() {
    if (this.connection) {
      try {
        await this.connection.close();
        this.isConnected = false;
        logger.info('Audit database connection closed', {
          component: 'audit-database',
          operation: 'connection_closed'
        });
      } catch (error) {
        logger.error('Error closing audit database connection', {
          component: 'audit-database',
          operation: 'close_error',
          error: error.message
        });
      }
    }
  }

  /**
   * Get database statistics
   */
  async getStats() {
    if (!this.isAuditDbConnected()) {
      return { connected: false, stats: null };
    }

    try {
      const db = this.connection.db;
      const stats = await db.stats();
      const collections = await db.listCollections().toArray();

      return {
        connected: true,
        stats: {
          database: stats.db,
          collections: collections.length,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexes: stats.indexes,
          objects: stats.objects
        }
      };
    } catch (error) {
      logger.error('Failed to get audit database stats', {
        component: 'audit-database',
        operation: 'get_stats_error',
        error: error.message
      });
      return { connected: true, stats: null, error: error.message };
    }
  }

  /**
   * Create database indexes for performance
   */
  async createIndexes() {
    if (!this.isAuditDbConnected()) {
      logger.warn('Cannot create indexes - audit database not connected', {
        component: 'audit-database',
        operation: 'create_indexes_skipped'
      });
      return;
    }

    try {
      const AuditLog = this.connection.model('AuditLog', require('../models/AuditLog').schema);

      // Ensure indexes are created
      await AuditLog.createIndexes();

      logger.info('Audit database indexes created successfully', {
        component: 'audit-database',
        operation: 'indexes_created'
      });
    } catch (error) {
      logger.error('Failed to create audit database indexes', {
        component: 'audit-database',
        operation: 'create_indexes_error',
        error: error.message
      });
    }
  }
}

// Singleton instance
const auditDatabase = new AuditDatabase();

module.exports = auditDatabase;
