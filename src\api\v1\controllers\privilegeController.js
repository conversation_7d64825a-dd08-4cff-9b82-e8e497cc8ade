//src/api/v1/controllers/privilegeController.js
const User = require('../../../models/User');
const Role = require('../../../models/Role');
const Organization = require('../../../models/Organization');
const privilegeManager = require('../../../services/privilege-manager');
const logger = require('../../../services/logger');
const mongoose = require('mongoose');

/**
 * Privilege Management Controller
 * Handles hierarchical privilege system including God Super User and Organization Super User
 */
class PrivilegeController {

  /**
   * Helper functions for flexible identifier support
   */
  isObjectId(id) {
    return mongoose.Types.ObjectId.isValid(id);
  }

  isEmail(identifier) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(identifier);
  }

  /**
   * Resolve user by ID or email
   * @param {string} identifier - User ID or email
   * @returns {Object|null} User object or null
   */
  async resolveUser(identifier) {
    if (this.isObjectId(identifier)) {
      return await User.findById(identifier);
    } else if (this.isEmail(identifier)) {
      return await User.findOne({ email: identifier.toLowerCase() });
    }
    return null;
  }

  /**
   * Resolve organization by ID or subdomain
   * @param {string} identifier - Organization ID or subdomain
   * @returns {Object|null} Organization object or null
   */
  async resolveOrganization(identifier) {
    if (this.isObjectId(identifier)) {
      return await Organization.findById(identifier);
    } else {
      return await Organization.findOne({ subdomain: identifier.toLowerCase() });
    }
  }

  /**
   * Assign God Super User privileges with bootstrap support
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async assignGodSuperUser(req, res) {
    const { userIdOrEmail, securitySettings = {} } = req.body;

    if (!userIdOrEmail) {
      return res.status(400).json({
        success: false,
        message: 'User identifier is required',
        error: {
          code: 'MISSING_USER_IDENTIFIER',
          details: 'userIdOrEmail field is required'
        }
      });
    }

    try {
      const targetUser = await this.resolveUser(userIdOrEmail);
      if (!targetUser) {
        return res.status(404).json({
          success: false,
          message: 'Target user not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: `No user found with identifier: ${userIdOrEmail}`
          }
        });
      }

      const result = await privilegeManager.assignGodSuperUser(
        targetUser._id,
        req.user._id,
        securitySettings
      );

      if (!result.success) {
        return res.status(result.statusCode || 400).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }

      logger.info('God Super User privileges assigned', {
        component: 'privilege-controller',
        operation: 'assign_god_super_user',
        metadata: {
          target_user_id: targetUser._id,
          granted_by: req.user._id,
          bootstrap_mode: result.bootstrapMode
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          user: {
            _id: targetUser._id,
            email: targetUser.email,
            name: targetUser.name
          },
          privilegeLevel: 'god_super_user',
          bootstrapMode: result.bootstrapMode,
          previousGodUser: result.previousGodUser,
          assignedAt: new Date().toISOString()
        }
      });
    } catch (err) {
      logger.error('Failed to assign God Super User privileges', err, {
        component: 'privilege-controller',
        operation: 'assign_god_super_user',
        metadata: { userIdOrEmail, granted_by: req.user._id }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to assign God Super User privileges',
        error: {
          code: 'GOD_SUPER_USER_ASSIGNMENT_ERROR',
          details: 'An error occurred while assigning privileges'
        }
      });
    }
  }

  /**
   * Assign Organization Super User privileges
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async assignOrganizationSuperUser(req, res) {
    const { orgIdOrSubdomain } = req.params;
    const { userIdOrEmail } = req.body;

    if (!userIdOrEmail) {
      return res.status(400).json({
        success: false,
        message: 'User identifier is required',
        error: {
          code: 'MISSING_USER_IDENTIFIER',
          details: 'userIdOrEmail field is required'
        }
      });
    }

    try {
      const organization = await this.resolveOrganization(orgIdOrSubdomain);
      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: `No organization found with identifier: ${orgIdOrSubdomain}`
          }
        });
      }

      const targetUser = await this.resolveUser(userIdOrEmail);
      if (!targetUser) {
        return res.status(404).json({
          success: false,
          message: 'Target user not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: `No user found with identifier: ${userIdOrEmail}`
          }
        });
      }

      const result = await privilegeManager.assignOrganizationSuperUser(
        targetUser._id,
        organization._id,
        req.user._id
      );

      if (!result.success) {
        return res.status(result.statusCode || 400).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }

      logger.info('Organization Super User privileges assigned', {
        component: 'privilege-controller',
        operation: 'assign_organization_super_user',
        metadata: {
          target_user_id: targetUser._id,
          organization_id: organization._id,
          granted_by: req.user._id
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          user: {
            _id: targetUser._id,
            email: targetUser.email,
            name: targetUser.name
          },
          organization: {
            _id: organization._id,
            name: organization.name,
            subdomain: organization.subdomain
          },
          privilegeLevel: 'organization_super_user',
          assignedAt: new Date().toISOString()
        }
      });
    } catch (err) {
      logger.error('Failed to assign Organization Super User privileges', err, {
        component: 'privilege-controller',
        operation: 'assign_organization_super_user',
        metadata: { orgIdOrSubdomain, userIdOrEmail, granted_by: req.user._id }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to assign Organization Super User privileges',
        error: {
          code: 'ORG_SUPER_USER_ASSIGNMENT_ERROR',
          details: 'An error occurred while assigning privileges'
        }
      });
    }
  }

  /**
   * Get current privilege status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPrivilegeStatus(req, res) {
    try {
      const user = await User.findById(req.user._id)
        .populate({
          path: 'roles.role',
          select: 'name description hierarchy'
        })
        .populate({
          path: 'roles.org',
          select: 'name subdomain'
        });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const privilegeStatus = {
        user: {
          _id: user._id,
          email: user.email,
          name: user.name
        },
        systemPrivileges: {
          isGodSuperUser: user.isGodSuperUser(),
          hasSystemPrivileges: user.hasSystemPrivileges(),
          highestPrivilegeLevel: user.getHighestPrivilegeLevel(),
          systemPrivilegesList: user.systemPrivileges || []
        },
        organizationRoles: user.roles?.map(role => ({
          organization: role.org,
          role: role.role,
          assignedAt: role.assignedAt,
          assignedBy: role.assignedBy
        })) || [],
        privilegeSummary: {
          totalOrganizations: user.roles?.length || 0,
          canManageSystem: user.isGodSuperUser(),
          canManageOrganizations: user.roles?.some(r => 
            r.role?.name === 'org_super_user' || r.role?.hierarchy?.level <= 2
          ) || false
        }
      };

      res.json({
        success: true,
        data: privilegeStatus,
        metadata: {
          retrieved_at: new Date().toISOString()
        }
      });
    } catch (err) {
      logger.error('Failed to get privilege status', err, {
        component: 'privilege-controller',
        operation: 'get_privilege_status',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve privilege status',
        error: {
          code: 'PRIVILEGE_STATUS_ERROR',
          details: 'An error occurred while retrieving privilege information'
        }
      });
    }
  }

  /**
   * Revoke God Super User privileges
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async revokeGodSuperUser(req, res) {
    const { userIdOrEmail } = req.body;

    if (!userIdOrEmail) {
      return res.status(400).json({
        success: false,
        message: 'User identifier is required'
      });
    }

    try {
      const targetUser = await this.resolveUser(userIdOrEmail);
      if (!targetUser) {
        return res.status(404).json({
          success: false,
          message: 'Target user not found'
        });
      }

      if (!targetUser.isGodSuperUser()) {
        return res.status(400).json({
          success: false,
          message: 'User is not a God Super User'
        });
      }

      const result = await privilegeManager.revokeGodSuperUser(
        targetUser._id,
        req.user._id
      );

      if (!result.success) {
        return res.status(result.statusCode || 400).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }

      logger.info('God Super User privileges revoked', {
        component: 'privilege-controller',
        operation: 'revoke_god_super_user',
        metadata: {
          target_user_id: targetUser._id,
          revoked_by: req.user._id
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          user: {
            _id: targetUser._id,
            email: targetUser.email,
            name: targetUser.name
          },
          revokedAt: new Date().toISOString()
        }
      });
    } catch (err) {
      logger.error('Failed to revoke God Super User privileges', err, {
        component: 'privilege-controller',
        operation: 'revoke_god_super_user',
        metadata: { userIdOrEmail, revoked_by: req.user._id }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to revoke God Super User privileges',
        error: {
          code: 'GOD_SUPER_USER_REVOCATION_ERROR',
          details: 'An error occurred while revoking privileges'
        }
      });
    }
  }
}

module.exports = new PrivilegeController();
