# Organization Endpoints Fix

## 🔍 **Problem Analysis**

The user reported that organization endpoints are returning "Organization not found" even though the user profile (`GET /api/v1/users/me`) shows the user has organizations:

```json
{
  "organizations": [
    {
      "_id": "6838bfd6278ccc88e4179049",
      "name": "Acme Corporation",
      "subdomain": "acme-corp",
      "status": "active"
    }
  ]
}
```

But these endpoints were failing:
- `GET /api/v1/organizations/current` → 404 "Organization not found"
- `GET /api/v1/organizations/my-organizations` → 404 "Organization not found"

## 🔧 **Root Causes Identified**

### **1. `getCurrentOrganization` Issue**
- **Problem**: Method expected subdomain context from `req.subdomain` or headers
- **Reality**: When testing from localhost without subdomain middleware, these are not available
- **Impact**: Always returned 400 "No subdomain context available"

### **2. `getMyOrganizations` Issue**
- **Problem**: Potential user lookup failures or issues with `getOrganizationsWithRoles()` method
- **Reality**: Method might fail silently or have insufficient error handling
- **Impact**: Returned generic "Organization not found" instead of specific error

### **3. Data Consistency Issue**
- **Problem**: User-organization bidirectional relationship might be broken
- **Reality**: User might be in `organization.members` but not in `user.roles` (or vice versa)
- **Impact**: Different endpoints return different results

## ✅ **Fixes Implemented**

### **1. Enhanced `getMyOrganizations` Method**

**File**: `src/api/v1/controllers/organizationController.js`

**Changes**:
- **Multiple user lookup strategies**: Try both `firebase_uid` and `_id`
- **Comprehensive logging**: Added debug logging at each step
- **Better error handling**: Specific error messages with debug information
- **Fallback mechanisms**: Multiple ways to find the user

**Before**:
```javascript
const user = await User.findOne({ firebase_uid: req.user.uid });
if (!user) {
  return res.status(404).json({ message: 'User not found' });
}
```

**After**:
```javascript
let user = await User.findOne({ firebase_uid: req.user.uid });
if (!user && req.user._id) {
  user = await User.findById(req.user._id);
}
if (!user) {
  // Detailed logging and debug info
  return res.status(404).json({ 
    message: 'User not found',
    debug: { firebase_uid: req.user.uid, user_id: req.user._id }
  });
}
```

### **2. Enhanced `getCurrentOrganization` Method**

**File**: `src/api/v1/controllers/organizationController.js`

**Changes**:
- **Multiple subdomain sources**: Check `req.subdomain`, headers, query params, body
- **Fallback to user's first organization**: If no subdomain, use user's first org
- **Better error messages**: Specific debug information
- **Flexible context handling**: Works with or without subdomain middleware

**Before**:
```javascript
const subdomain = req.subdomain || req.headers['x-subdomain'];
if (!subdomain) {
  return res.status(400).json({
    message: 'No subdomain context available'
  });
}
```

**After**:
```javascript
let subdomain = req.subdomain || 
               req.headers['x-subdomain'] || 
               req.query.subdomain ||
               req.body.subdomain;

// If no subdomain, try user's first organization
if (!subdomain) {
  let user = await User.findOne({ firebase_uid: req.user.uid });
  if (user) {
    const userOrganizations = await user.getOrganizationsWithRoles();
    if (userOrganizations.length > 0) {
      subdomain = userOrganizations[0].organization.subdomain;
    }
  }
}
```

## 🧪 **Testing Tools Created**

### **1. Debug Script** (`src/scripts/debugOrganizationIssues.js`)
- **Purpose**: Diagnose specific user-organization relationship issues
- **Features**:
  - Debug specific user data and organization data
  - Test bidirectional relationships
  - Check database consistency
  - Test controller methods directly

### **2. Enhanced Test Script** (`src/scripts/testPendingOrgsEndpoint.js`)
- **Added**: Organization endpoints testing functionality
- **Features**:
  - Test `getMyOrganizations` method
  - Test `getCurrentOrganization` method
  - Mock request/response objects for testing

## 🔄 **Usage Examples**

### **Testing getCurrentOrganization with Subdomain**
```bash
# With subdomain parameter
curl -H "Authorization: Bearer <token>" \
     "http://localhost:3000/api/v1/organizations/current?subdomain=acme-corp"

# With header
curl -H "Authorization: Bearer <token>" \
     -H "x-subdomain: acme-corp" \
     "http://localhost:3000/api/v1/organizations/current"
```

### **Testing getMyOrganizations**
```bash
curl -H "Authorization: Bearer <token>" \
     "http://localhost:3000/api/v1/organizations/my-organizations"
```

## 🔍 **Debugging Steps**

### **1. Run Debug Script**
```bash
node src/scripts/debugOrganizationIssues.js
```

This will:
- Check the specific user's data
- Verify organization relationships
- Test controller methods directly
- Identify any data inconsistencies

### **2. Check for Data Inconsistencies**
If the debug script finds inconsistencies, run the sync fix:
```bash
node src/scripts/fixUserOrganizationSync.js
```

### **3. Test Endpoints**
After fixes, test the endpoints:
```bash
# Test with debug info
curl -H "Authorization: Bearer <token>" \
     "http://localhost:3000/api/v1/organizations/my-organizations"

# Test current org with fallback
curl -H "Authorization: Bearer <token>" \
     "http://localhost:3000/api/v1/organizations/current"
```

## 📊 **Expected Results**

### **After Fix - getMyOrganizations**
```json
{
  "message": "User organizations retrieved successfully",
  "organizations": [
    {
      "organization": {
        "_id": "6838bfd6278ccc88e4179049",
        "name": "Acme Corporation",
        "subdomain": "acme-corp",
        "status": "active",
        "branding": { ... }
      },
      "role": "super_user",
      "joinedAt": "2025-05-29T20:13:10.956Z",
      "status": "active"
    }
  ],
  "total": 1
}
```

### **After Fix - getCurrentOrganization**
```json
{
  "_id": "6838bfd6278ccc88e4179049",
  "name": "Acme Corporation",
  "subdomain": "acme-corp",
  "branding": { ... },
  "memberCount": 1,
  "createdAt": "2025-05-29T20:13:10.956Z",
  "metadata": {
    "retrieved_at": "2025-05-29T21:30:00.000Z",
    "subdomain_context": "acme-corp",
    "duration_ms": 45,
    "lookup_method": "subdomain"
  }
}
```

## 🚨 **Common Issues & Solutions**

### **Issue**: "User not found" in getMyOrganizations
**Solution**: Check if user exists with both `firebase_uid` and `_id` lookup methods

### **Issue**: "No subdomain context available" in getCurrentOrganization
**Solution**: 
1. Pass subdomain as query parameter: `?subdomain=acme-corp`
2. Use header: `x-subdomain: acme-corp`
3. Let it fallback to user's first organization

### **Issue**: Data inconsistency between user and organization
**Solution**: Run the sync fix script: `node src/scripts/fixUserOrganizationSync.js`

## 🎯 **Next Steps**

1. **Test the fixes**: Use the debug script to verify the fixes work
2. **Run sync if needed**: If inconsistencies found, run the sync script
3. **Update frontend**: Ensure frontend handles the new response formats
4. **Monitor logs**: Check application logs for any remaining issues
5. **Document subdomain usage**: Update API docs with subdomain parameter options

## 📝 **Summary**

The fixes address the core issues by:
- ✅ **Making endpoints more resilient** with multiple lookup strategies
- ✅ **Adding comprehensive error handling** with debug information
- ✅ **Providing fallback mechanisms** for subdomain context
- ✅ **Creating debugging tools** to identify and fix data issues
- ✅ **Maintaining backward compatibility** while adding new functionality

The endpoints should now work correctly both with and without subdomain context, and provide clear error messages when issues occur.
