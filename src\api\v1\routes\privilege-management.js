// api/v1/routes/privilege-management.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../../middleware/auth');
const { rbac } = require('../../../middleware/rbac');
const privilegeController = require('../controllers/privilegeController');
const roleController = require('../controllers/roleController');

/**
 * @swagger
 * tags:
 *   - name: PrivilegeManagement
 *     description: Hierarchical privilege and role management system
 */

/**
 * @swagger
 * /api/v1/privileges/god-super-user:
 *   post:
 *     summary: Assign God Super User privileges with bootstrap support
 *     description: |
 *       Assign system-wide God Super User privileges to a user with support for first-time setup (bootstrap).
 *
 *       **Bootstrap Mode (First-Time Setup):**
 *       - When NO God Super User exists in the system, ANY authenticated user can assign God Super User privileges
 *       - This enables initial system setup without requiring existing privileges
 *
 *       **Protected Mode (After Bootstrap):**
 *       - When a God Super User already exists, only the current God Super User can assign privileges to another user
 *       - This transfers privileges from the current God Super User to the new user
 *       - The previous God Super User is automatically demoted to audit viewer role
 *
 *       Only one God Super User can exist at any time (atomic operation).
 *     tags: [PrivilegeManagement]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIdentifier
 *             properties:
 *               userIdentifier:
 *                 type: string
 *                 description: User MongoDB ObjectId or email address to grant God Super User privileges
 *                 example: "663041cf7a14c7c000a3f999"
 *     responses:
 *       200:
 *         description: God Super User privileges assigned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     user_id:
 *                       type: string
 *                       description: ID of the user who received God Super User privileges
 *                     user_email:
 *                       type: string
 *                       description: Email of the user who received privileges
 *                     privilege_level:
 *                       type: string
 *                       example: god_super_user
 *                     assignment_type:
 *                       type: string
 *                       enum: [bootstrap, transfer]
 *                       description: Whether this was a bootstrap assignment or privilege transfer
 *                     previous_god_user:
 *                       type: object
 *                       nullable: true
 *                       description: Information about the previous God Super User (null for bootstrap)
 *                       properties:
 *                         id:
 *                           type: string
 *                         email:
 *                           type: string
 *                         demoted_to:
 *                           type: string
 *                           example: audit_viewer
 *                 metadata:
 *                   type: object
 *                   properties:
 *                     assignment_context:
 *                       type: string
 *                       enum: [bootstrap, protected_transfer]
 *                     bootstrap_assignment:
 *                       type: boolean
 *                       description: True if this was a first-time bootstrap assignment
 *       400:
 *         description: Invalid request parameters
 *       403:
 *         description: Insufficient privileges (only in protected mode when God Super User already exists)
 *       404:
 *         description: Target user not found
 *       500:
 *         description: Internal server error
 */

router.post('/god-super-user', authenticate, privilegeController.assignGodSuperUser);

/**
 * @swagger
 * /api/v1/privileges/org-super-user:
 *   post:
 *     summary: Assign Organization Super User role
 *     description: Assign Organization Super User role to a user for a specific organization using email/ID and org subdomain/ID
 *     tags: [PrivilegeManagement]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIdentifier
 *               - orgIdentifier
 *             properties:
 *               userIdentifier:
 *                 type: string
 *                 description: User email or ID
 *                 example: <EMAIL>
 *               orgIdentifier:
 *                 type: string
 *                 description: Organization subdomain or ID
 *                 example: acme-corp
 *     responses:
 *       200:
 *         description: Organization Super User role assigned successfully
 *       400:
 *         description: Invalid request parameters
 *       403:
 *         description: Insufficient privileges
 *       404:
 *         description: User or organization not found
 *       500:
 *         description: Internal server error
 */
router.post('/org-super-user/:orgIdOrSubdomain', authenticate, rbac('role_object:assign', { allowSystemWide: true, requireOrgContext: false }), privilegeController.assignOrganizationSuperUser);

/**
 * @swagger
 * /api/v1/privileges/roles/initialize/{orgIdOrSubdomain}:
 *   post:
 *     summary: Initialize system roles for organization with flexible identifier support
 *     description: |
 *       Create all predefined system roles for an organization.
 *       Supports both MongoDB ObjectId and subdomain for organization identification.
 *
 *       **Supported identifier formats:**
 *       - MongoDB ObjectId: `663041cf7a14c7c000a3f999`
 *       - Subdomain: `acme-corp`
 *     tags: [PrivilegeManagement]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: orgIdOrSubdomain
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization MongoDB ObjectId or subdomain
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           subdomain:
 *             value: "acme-corp"
 *             summary: Organization subdomain
 *     responses:
 *       200:
 *         description: System roles initialized successfully
 *       400:
 *         description: Invalid organization identifier
 *       403:
 *         description: Insufficient privileges
 *       404:
 *         description: Organization not found
 *       500:
 *         description: Internal server error
 */
router.post('/roles/initialize/:orgIdOrSubdomain', authenticate, rbac('role_object:create', { allowSystemWide: true, requireOrgContext: false }), roleController.initializeRoles);

/**
 * @swagger
 * /api/v1/privileges/user/{userIdOrEmail}/roles:
 *   get:
 *     summary: Get user's roles and privileges with flexible identifier support
 *     description: |
 *       Retrieve all roles and privilege levels for a specific user.
 *       Supports both MongoDB ObjectId and email address for user identification.
 *
 *       **Supported identifier formats:**
 *       - MongoDB ObjectId: `663041cf7a14c7c000a3f999`
 *       - Email address: `<EMAIL>`
 *     tags: [PrivilegeManagement]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: userIdOrEmail
 *         required: true
 *         schema:
 *           type: string
 *         description: User MongoDB ObjectId or email address
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           email:
 *             value: "<EMAIL>"
 *             summary: Email address
 *     responses:
 *       200:
 *         description: User roles and privileges retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     user_id:
 *                       type: string
 *                     user_email:
 *                       type: string
 *                     user_name:
 *                       type: string
 *                     system_privileges:
 *                       type: array
 *                       items:
 *                         type: object
 *                     organization_roles:
 *                       type: array
 *                       items:
 *                         type: object
 *                     privilege_summary:
 *                       type: object
 *       400:
 *         description: Invalid user identifier format
 *       403:
 *         description: Insufficient privileges
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/user/:userIdOrEmail/roles', authenticate, rbac('user_object:read', { allowSystemWide: true, requireOrgContext: false }), privilegeController.getPrivilegeStatus);

module.exports = router;
