// src/routes/docs.js
const express = require('express');
const path = require('path');
const { swaggerSpec, swaggerUi } = require('../docs/swagger');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   - name: Health
 *     description: Service health and liveness endpoints
 *   - name: Documentation
 *     description: Swagger UI and raw spec
 */

/**
 * @swagger
 * /health:
 *   get:
 *     tags: [Health]
 *     summary: API Health Check
 *     description: |
 *       Returns the current health status of the API service.
 *       Use this endpoint to verify that the API is running and accessible.
 *       This endpoint does not require authentication and includes subdomain context.
 *     security: []
 *     responses:
 *       200:
 *         description: Service is healthy and operational
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   description: Health status message
 *                   example: your server is up and running
 *                 subdomain:
 *                   type: object
 *                   description: Subdomain extraction information
 *                 organization:
 *                   type: object
 *                   description: Organization context if resolved
 *       500:
 *         description: Service is experiencing issues
 */
router.get('/health', (req, res) => {
  try {
    const response = {
      status: 'your server is up and running',
      timestamp: new Date().toISOString(),
      version: '2.0.0-subdomain-enabled',
      subdomain: req.subdomain || null,
      organization: req.organization ? {
        id: req.organization._id,
        name: req.organization.name,
        subdomain: req.organization.subdomain
      } : null,
      organizationContext: req.organizationContext || null,
      host: req.get('host'),
      environment: process.env.NODE_ENV || 'development'
    };

    res.json(response);
  } catch (error) {
    console.error('Health endpoint error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /:
 *   get:
 *     tags: [Documentation]
 *     summary: API Welcome Page (Password Protected)
 *     description: |
 *       Welcome page with links to API documentation and guides.
 *       This is the root endpoint that serves as an entry point to the API.
 *       Requires password authentication to access.
 *     security: []
 *     responses:
 *       200:
 *         description: Welcome page HTML
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *       302:
 *         description: Redirect to login page if not authenticated
 */
router.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../docs', 'welcome.html'));
});

/**
 * @swagger
 * /api-docs-ui:
 *   get:
 *     tags: [Documentation]
 *     summary: Swagger UI Documentation
 *     description: |
 *       Interactive Swagger UI documentation for the API.
 *       This endpoint serves the standard Swagger UI interface.
 *     security: []
 *     responses:
 *       200:
 *         description: Swagger UI HTML page
 */
router.use('/api-docs-ui', swaggerUi.serve, swaggerUi.setup(swaggerSpec, { explorer: true }));

/**
 * @swagger
 * /swagger.json:
 *   get:
 *     tags: [Documentation]
 *     summary: OpenAPI Specification
 *     description: |
 *       Raw OpenAPI/Swagger JSON specification for the API.
 *       This endpoint returns the complete API specification in JSON format.
 *     security: []
 *     responses:
 *       200:
 *         description: OpenAPI specification in JSON format
 */
router.get('/swagger.json', (req, res) => res.json(swaggerSpec));

/**
 * @swagger
 * /api-docs:
 *   get:
 *     tags: [Documentation]
 *     summary: Custom API Documentation (Password Protected)
 *     description: |
 *       Custom-styled API documentation interface.
 *       This endpoint serves a custom HTML documentation page with enhanced styling and usability.
 *       Requires password authentication to access.
 *     security: []
 *     responses:
 *       200:
 *         description: Custom API documentation HTML page
 */
router.get('/api-docs', (req, res) => res.sendFile(path.join(__dirname, '../docs', 'swagger.html')));

/**
 * @swagger
 * /auth-guide:
 *   get:
 *     tags: [Documentation]
 *     summary: Authentication Guide
 *     description: |
 *       Comprehensive guide to authentication methods and flows.
 *       This document explains how to use the various authentication methods provided by the API.
 *     security: []
 *     responses:
 *       200:
 *         description: Authentication guide in Markdown format
 */
router.get('/auth-guide', (req, res) => {
  res.setHeader('Content-Type', 'text/markdown');
  res.sendFile(path.join(__dirname, '../docs', 'auth-guide.md'));
});

/**
 * @swagger
 * /docs:
 *   get:
 *     tags: [Documentation]
 *     summary: Documentation Guide
 *     description: |
 *       Guide to using the API documentation.
 *       This document explains how to effectively use the API documentation and understand the API structure.
 *     security: []
 *     responses:
 *       200:
 *         description: Documentation guide in Markdown format
 */
router.get('/docs', (req, res) => {
  res.setHeader('Content-Type', 'text/markdown');
  res.sendFile(path.join(__dirname, '../docs', 'README.md'));
});

module.exports = router;
