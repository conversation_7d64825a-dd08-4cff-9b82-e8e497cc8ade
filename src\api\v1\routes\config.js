// src/api/v1/routes/config.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../../middleware/auth');
const { rbac } = require('../../../middleware/rbac');
const configController = require('../controllers/configController');

/**
 * @swagger
 * tags:
 *   name: Config
 *   description: Runtime configuration (admin only)
 */

/**
 * @swagger
 * /api/v1/config/email:
 *   get:
 *     tags: [Config]
 *     summary: Get current SMTP settings
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     responses:
 *       200:
 *         description: Current SMTP config
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 smtp:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: string
 *                     fromAddress:
 *                       type: string
 *                     cc_users:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: List of CC email addresses (optional)
 */
router.get(
  '/email',
  authenticate,
  rbac('config:read'),
  configController.getEmailConfig
);

/**
 * @swagger
 * /api/v1/config/email:
 *   post:
 *     tags: [Config]
 *     summary: Update SMTP settings
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               smtp:
 *                 type: object
 *                 required:
 *                   - user
 *                   - pass
 *                   - fromAddress
 *                 properties:
 *                   user:
 *                     type: string
 *                   pass:
 *                     type: string
 *                   fromAddress:
 *                     type: string
 *                   cc_users:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of CC email addresses (optional)
 *             example:
 *               smtp:
 *                 user: "<EMAIL>"
 *                 pass: "app-password-or-oauth2-token"
 *                 fromAddress: "Your App <<EMAIL>>"
 *                 cc_users:
 *                   - "<EMAIL>"
 *                   - "<EMAIL>"
 *     responses:
 *       200:
 *         description: SMTP settings updated
 */
router.post(
  '/email',
  authenticate,
  rbac('config:update'),
  configController.updateEmailConfig
);

module.exports = router;
