# API Rate Limiting Implementation

## 🎯 **Overview**

This document describes the comprehensive rate limiting system implemented to protect the API from abuse and ensure fair usage across all users.

## 🏗️ **Architecture**

### **Components**
1. **Rate Limit Middleware** (`src/middleware/rateLimitMiddleware.js`)
2. **Rate Limit Configuration** (`src/config/rateLimitConfig.js`)
3. **Redis Store** (optional, for distributed rate limiting)
4. **Dynamic Endpoint Detection**

### **Features**
- ✅ **Multiple Rate Limiting Strategies** - Different limits for different endpoint types
- ✅ **User-based vs IP-based** - Authenticated users get higher limits
- ✅ **Redis Support** - Distributed rate limiting across multiple servers
- ✅ **Environment-specific Limits** - Different limits for dev/prod
- ✅ **God User Bypass** - Optional bypass for system administrators
- ✅ **Comprehensive Logging** - Detailed rate limit violation tracking
- ✅ **Graceful Degradation** - Falls back to memory store if Redis unavailable

## 📊 **Rate Limits**

### **Production Environment**

| Endpoint Type | Window | Authenticated Users | Unauthenticated Users |
|---------------|--------|--------------------|--------------------|
| **General API** | 15 min | 1,000 requests | 100 requests |
| **Authentication** | 15 min | 10 requests | 10 requests |
| **Registration** | 1 hour | 5 requests | 5 requests |
| **Password Reset** | 1 hour | 3 requests | 3 requests |
| **Admin Operations** | 5 min | 100 requests | N/A |
| **File Uploads** | 1 hour | 50 requests | N/A |
| **Search Operations** | 1 min | 30 requests | 10 requests |
| **Organization Creation** | 24 hours | 3 requests | N/A |
| **API Documentation** | 5 min | 50 requests | 50 requests |

### **Development Environment**

| Endpoint Type | Window | Authenticated Users | Unauthenticated Users |
|---------------|--------|--------------------|--------------------|
| **General API** | 15 min | 2,000 requests | 500 requests |
| **Authentication** | 15 min | 20 requests | 20 requests |
| **Registration** | 1 hour | 10 requests | 10 requests |
| **Password Reset** | 1 hour | 10 requests | 10 requests |
| **Admin Operations** | 5 min | 200 requests | N/A |
| **File Uploads** | 1 hour | 100 requests | N/A |
| **Search Operations** | 1 min | 60 requests | 20 requests |
| **Organization Creation** | 24 hours | 10 requests | N/A |
| **API Documentation** | 5 min | 100 requests | 100 requests |

## 🔧 **Configuration**

### **Environment Variables**

```bash
# Enable/disable rate limiting
RATE_LIMITING_ENABLED=true

# Skip rate limits for god super users
SKIP_RATE_LIMIT_FOR_GOD_USERS=false

# Whitelist specific IPs (comma-separated)
RATE_LIMIT_WHITELIST_IPS=127.0.0.1,::1

# Redis configuration for distributed rate limiting
REDIS_URL=redis://localhost:6379
# OR
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-password
REDIS_DB=0
REDIS_RATE_LIMIT_PREFIX=rl:
```

### **Endpoint-Specific Configuration**

The system automatically detects endpoint types based on URL patterns:

```javascript
// Authentication endpoints
/api/v1/auth/* → 'auth' limits

// Admin endpoints  
/api/v1/admin/* → 'admin' limits

// Upload endpoints
/api/v1/upload/* → 'upload' limits

// Search endpoints
/api/v1/search/* → 'search' limits

// Organization creation
/api/v1/organizations/create → 'organizationCreation' limits

// Documentation
/api-docs*, /swagger* → 'docs' limits
```

## 🚀 **Implementation**

### **1. Global Rate Limiting**

Applied to all requests automatically:

```javascript
// In src/index.js
app.use(rateLimitMiddleware.dynamicLimit());
```

### **2. Endpoint-Specific Rate Limiting**

Applied to specific routes:

```javascript
// Authentication endpoints
router.post('/login',
  rateLimitMiddleware.getMiddleware('auth'),
  authController.login
);

// Organization creation
router.post('/create',
  rateLimitMiddleware.getMiddleware('organizationCreation'),
  organizationController.createOrganization
);
```

### **3. Rate Limit Key Generation**

The system generates unique keys based on:

1. **User ID** (highest priority) - for authenticated users
2. **Firebase UID** (fallback) - for Firebase-authenticated users  
3. **IP Address** (fallback) - for unauthenticated users

```javascript
generateKey(req) {
  if (req.user?._id) return `user:${req.user._id}`;
  if (req.user?.uid) return `firebase:${req.user.uid}`;
  return `ip:${req.ip}`;
}
```

## 📝 **Response Format**

When rate limit is exceeded, the API returns:

```json
{
  "success": false,
  "message": "Too many requests",
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "details": "You have exceeded the rate limit. Please try again later.",
    "retry_after": "60"
  },
  "metadata": {
    "limit_type": "auth",
    "reset_time": "2025-05-29T22:00:00.000Z"
  }
}
```

### **HTTP Headers**

Standard rate limiting headers are included:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: **********
Retry-After: 60
```

## 🔍 **Monitoring & Logging**

### **Rate Limit Violations**

All rate limit violations are logged with detailed information:

```javascript
{
  component: 'rate-limit-middleware',
  operation: 'rate_limit_exceeded',
  metadata: {
    key: 'user:12345',
    ip: '*************',
    user_id: '12345',
    path: '/api/v1/auth/login',
    method: 'POST',
    limit_type: 'auth',
    user_agent: 'Mozilla/5.0...'
  }
}
```

### **Performance Monitoring**

Rate limit checks include performance tracking:

```javascript
{
  component: 'rate-limit-middleware',
  operation: 'rate_limit_reached',
  metadata: {
    limit: 100,
    window: 900000,
    path: '/api/v1/users',
    duration_ms: 5
  }
}
```

## 🛠️ **Administration**

### **Bypassing Rate Limits**

#### **God Super Users**
```bash
# Enable god user bypass
SKIP_RATE_LIMIT_FOR_GOD_USERS=true
```

#### **IP Whitelisting**
```bash
# Whitelist specific IPs
RATE_LIMIT_WHITELIST_IPS=*************,*********
```

#### **Health Checks**
Health check endpoints (`/health`, `/ping`) are automatically excluded.

### **Redis Management**

#### **Monitor Redis Usage**
```bash
# Connect to Redis
redis-cli

# Check rate limit keys
KEYS rl:*

# Check specific user's rate limit
GET rl:user:12345

# Clear all rate limit data
FLUSHDB
```

#### **Redis Failover**
If Redis becomes unavailable, the system automatically falls back to in-memory rate limiting.

## 🧪 **Testing**

### **Test Rate Limits**

```bash
# Test authentication rate limit
for i in {1..15}; do
  curl -X POST http://localhost:3000/api/v1/auth/login \
    -H "Content-Type: application/json" \
    -d '{"identifier":"<EMAIL>","password":"wrong"}'
done

# Test general API rate limit
for i in {1..105}; do
  curl http://localhost:3000/api/v1/users/me
done
```

### **Verify Headers**

```bash
curl -I http://localhost:3000/api/v1/users/me
# Should include:
# X-RateLimit-Limit: 1000
# X-RateLimit-Remaining: 999
# X-RateLimit-Reset: **********
```

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Rate Limits Too Strict**
```javascript
// Adjust limits in src/config/rateLimitConfig.js
getProductionLimits() {
  return {
    general: {
      windowMs: 15 * 60 * 1000,
      maxAuthenticated: 2000, // Increase from 1000
      maxUnauthenticated: 200  // Increase from 100
    }
  };
}
```

#### **Redis Connection Issues**
```bash
# Check Redis connectivity
redis-cli ping

# Check Redis logs
docker logs redis-container

# Verify environment variables
echo $REDIS_URL
```

#### **Memory Usage (Without Redis)**
In-memory rate limiting can consume significant memory with many users. Consider:
- Enabling Redis for production
- Reducing rate limit windows
- Implementing cleanup for expired entries

### **Performance Optimization**

#### **Redis Optimization**
```bash
# Redis configuration for rate limiting
maxmemory 256mb
maxmemory-policy allkeys-lru
save ""  # Disable persistence for rate limiting data
```

#### **Application Optimization**
- Use Redis for production environments
- Implement rate limit key cleanup
- Monitor memory usage in development

## 📈 **Metrics & Analytics**

### **Key Metrics to Monitor**

1. **Rate Limit Hit Rate** - Percentage of requests hitting limits
2. **Top Rate Limited Users** - Users frequently hitting limits
3. **Endpoint-Specific Violations** - Which endpoints are most limited
4. **Geographic Distribution** - Rate limits by IP location
5. **Time-based Patterns** - Peak usage times

### **Alerting**

Set up alerts for:
- High rate limit violation rates (>5%)
- Redis connection failures
- Memory usage spikes (in-memory mode)
- Unusual traffic patterns

## 🔮 **Future Enhancements**

1. **Dynamic Rate Limiting** - Adjust limits based on server load
2. **User-Specific Limits** - Custom limits for premium users
3. **Geographic Rate Limiting** - Different limits by region
4. **Machine Learning** - Detect and prevent abuse patterns
5. **Rate Limit Analytics Dashboard** - Real-time monitoring UI
6. **Burst Allowances** - Allow temporary bursts above normal limits

This rate limiting system provides robust protection while maintaining excellent user experience and comprehensive monitoring capabilities.
