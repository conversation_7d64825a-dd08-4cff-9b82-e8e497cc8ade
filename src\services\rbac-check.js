// services/rbac-check.js
const { getPermissions } = require('./rbac-cache');

function hasPermission(user, orgId, resource, action) {
  // Find the user's role for this org
  const userRoleEntry = (user.roles || []).find(r => r.org.toString() === orgId.toString());
  if (!userRoleEntry) return false;

  const permissions = getPermissions(orgId, userRoleEntry.role);
  for (const perm of permissions) {
    if (perm.resource === resource && (perm.actions.includes(action) || perm.actions.includes('*')))
      return true;
    if (perm.resource === resource && perm.actions === '*')
      return true;
    if (perm.resource === '*' && perm.actions.includes('*'))
      return true;
  }
  return false;
}

module.exports = { hasPermission };
