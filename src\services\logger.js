// src/services/logger.js
const crypto = require('crypto');

/**
 * Simple structured logger service for RBAC middleware
 * Provides consistent logging with correlation IDs and structured metadata
 */
class Logger {
  constructor() {
    this.timers = new Map();
    this.context = new Map();
  }

  /**
   * Generate a unique correlation ID for request tracking
   */
  generateCorrelationId() {
    return crypto.randomBytes(8).toString('hex');
  }

  /**
   * Start a timer for performance tracking
   */
  startTimer(operation) {
    const timerId = `${operation}_${Date.now()}_${Math.random()}`;
    this.timers.set(timerId, Date.now());
    return timerId;
  }

  /**
   * End a timer and return duration
   */
  endTimer(timerId) {
    const startTime = this.timers.get(timerId);
    if (!startTime) return { duration_ms: 0 };

    const duration_ms = Date.now() - startTime;
    this.timers.delete(timerId);
    return { duration_ms };
  }

  /**
   * Set context for correlation tracking
   */
  setContext(correlationId, user, requestContext) {
    this.context.set(correlationId, { user, requestContext });
  }

  /**
   * Format log message with metadata
   */
  _formatLog(level, message, metadata = {}) {
    return {
      timestamp: new Date().toISOString(),
      level: level.toUpperCase(),
      message,
      ...metadata
    };
  }

  /**
   * Log error messages
   */
  error(message, error, metadata = {}) {
    const logData = this._formatLog('error', message, {
      ...metadata,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    });
    console.error(JSON.stringify(logData, null, 2));
  }

  /**
   * Log warning messages
   */
  warn(message, metadata = {}) {
    const logData = this._formatLog('warn', message, metadata);
    console.warn(JSON.stringify(logData, null, 2));
  }

  /**
   * Log info messages
   */
  info(message, metadata = {}) {
    const logData = this._formatLog('info', message, metadata);
    console.info(JSON.stringify(logData, null, 2));
  }

  /**
   * Log debug messages
   */
  debug(message, metadata = {}) {
    if (process.env.NODE_ENV === 'development' || process.env.LOG_LEVEL === 'debug') {
      const logData = this._formatLog('debug', message, metadata);
      console.debug(JSON.stringify(logData, null, 2));
    }
  }

  /**
   * Log authentication events
   */
  logAuthentication(event, data, metadata = {}) {
    this.info(`Authentication: ${event}`, {
      event_type: 'authentication',
      ...data,
      ...metadata
    });
  }

  /**
   * Log security violations
   */
  logSecurityViolation(message, metadata = {}) {
    this.warn(`Security Violation: ${message}`, {
      event_type: 'security_violation',
      ...metadata
    });
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetric(metric, value, metadata = {}) {
    this.info(`Performance: ${metric}`, {
      event_type: 'performance',
      metric,
      value,
      ...metadata
    });
  }

  /**
   * Log RBAC events specifically
   */
  logRBACEvent(event, data, metadata = {}) {
    this.info(`RBAC: ${event}`, {
      event_type: 'rbac',
      ...data,
      ...metadata
    });
  }

  /**
   * Log audit events with enhanced metadata
   */
  logAuditEvent(operation, data, metadata = {}) {
    const auditData = {
      event_type: 'audit',
      operation,
      timestamp: new Date().toISOString(),
      ...data,
      ...metadata
    };

    this.info(`Audit: ${operation}`, auditData);

    // Also send to audit logger if available
    try {
      const auditLogger = require('./auditLogger');
      auditLogger.logOperation({
        operationType: this.mapOperationToAuditType(operation),
        resourceType: data.resource_type || 'unknown',
        user: data.user,
        endpoint: data.endpoint,
        requestData: data.request_data,
        responseData: data.response_data,
        responseStatus: data.response_status,
        executionTime: data.execution_time_ms,
        organizationContext: data.organization_context,
        roleContext: data.role_context,
        identifierTypes: data.identifier_types,
        isAdminAction: data.is_admin_action,
        isPrivilegeEscalation: data.is_privilege_escalation,
        customMetadata: metadata
      });
    } catch (error) {
      // Silently fail if audit logger is not available
      this.debug('Audit logger not available', { error: error.message });
    }
  }

  /**
   * Map operation names to audit types
   */
  mapOperationToAuditType(operation) {
    const operationMap = {
      'create': 'CREATE',
      'read': 'READ',
      'update': 'UPDATE',
      'delete': 'DELETE',
      'login': 'AUTH',
      'logout': 'AUTH',
      'assign': 'ASSIGN',
      'revoke': 'REVOKE',
      'assign_role': 'ASSIGN',
      'remove_role': 'REVOKE'
    };

    return operationMap[operation.toLowerCase()] || 'READ';
  }

  /**
   * Enhanced logging for API operations with audit integration
   */
  logAPIOperation(req, res, operationType, resourceType, additionalData = {}) {
    const user = req.user || {};
    const executionTime = req.startTime ? Date.now() - req.startTime : undefined;

    const logData = {
      operation_type: operationType,
      resource_type: resourceType,
      endpoint: req.originalUrl || req.url,
      method: req.method,
      user: {
        id: user._id || user.id,
        email: user.email
      },
      request_data: req.body,
      response_status: res.statusCode,
      execution_time_ms: executionTime,
      ip_address: req.ip || req.connection?.remoteAddress,
      user_agent: req.get('User-Agent'),
      ...additionalData
    };

    this.logAuditEvent(`${operationType}_${resourceType}`, logData);
  }

  /**
   * Log privilege escalation events
   */
  logPrivilegeEscalation(user, action, target, metadata = {}) {
    this.logAuditEvent('privilege_escalation', {
      user: {
        id: user._id || user.id,
        email: user.email
      },
      action,
      target,
      is_privilege_escalation: true,
      ...metadata
    });
  }

  /**
   * Log bulk operations
   */
  logBulkOperation(user, operation, targets, metadata = {}) {
    this.logAuditEvent('bulk_operation', {
      user: {
        id: user._id || user.id,
        email: user.email
      },
      operation,
      targets_count: Array.isArray(targets) ? targets.length : 1,
      targets: Array.isArray(targets) ? targets.slice(0, 10) : [targets], // Limit to first 10
      is_bulk_operation: true,
      ...metadata
    });
  }
}

// Export singleton instance
module.exports = new Logger();
