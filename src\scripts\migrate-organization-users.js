#!/usr/bin/env node

/**
 * Migration Script: Update User Types for Organization Super Users
 * 
 * This script finds all users who are super_users of organizations
 * and updates their type field to 'organization' if it's not already set.
 * 
 * Usage:
 *   node src/scripts/migrate-organization-users.js
 *   
 * Options:
 *   --dry-run    Show what would be updated without making changes
 *   --verbose    Show detailed logging
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Role = require('../models/Role');
const logger = require('../services/logger');

class OrganizationUserMigration {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.verbose = options.verbose || false;
    this.stats = {
      totalUsers: 0,
      usersChecked: 0,
      usersUpdated: 0,
      usersSkipped: 0,
      errors: 0
    };
  }

  /**
   * Connect to MongoDB
   */
  async connect() {
    try {
      await mongoose.connect(process.env.MONGO_URI);
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error.message);
      process.exit(1);
    }
  }

  /**
   * Disconnect from MongoDB
   */
  async disconnect() {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }

  /**
   * Find super_user role ID
   */
  async getSuperUserRole() {
    const superUserRole = await Role.findOne({ name: 'super_user' });
    if (!superUserRole) {
      throw new Error('super_user role not found in database');
    }
    return superUserRole._id;
  }

  /**
   * Find all users who are super_users of organizations
   */
  async findOrganizationSuperUsers(superUserRoleId) {
    const users = await User.find({
      'roles.role': superUserRoleId
    }).populate('roles.org', 'name subdomain');

    // Filter to only include users who have super_user roles in organizations
    const orgSuperUsers = users.filter(user => {
      return user.roles.some(roleEntry => 
        roleEntry.role.toString() === superUserRoleId.toString() && 
        roleEntry.org // Has an organization
      );
    });

    return orgSuperUsers;
  }

  /**
   * Update a user's type to 'organization'
   */
  async updateUserType(user) {
    const previousType = user.type;
    
    if (user.type === 'organization') {
      this.stats.usersSkipped++;
      if (this.verbose) {
        console.log(`⏭️  Skipped ${user.email} - already type 'organization'`);
      }
      return false;
    }

    if (!this.dryRun) {
      user.type = 'organization';
      await user.save();

      // Log the change
      logger.info('User type migrated to organization', {
        component: 'migration-script',
        operation: 'migrate_user_type',
        metadata: {
          user_id: user._id,
          user_email: user.email,
          previous_type: previousType || 'null',
          new_type: 'organization',
          migration_reason: 'User is super_user of organization(s)'
        }
      });
    }

    this.stats.usersUpdated++;
    
    const organizations = user.roles
      .filter(role => role.org)
      .map(role => role.org.name || role.org.subdomain || role.org._id)
      .join(', ');

    console.log(`${this.dryRun ? '🔍' : '✅'} ${this.dryRun ? 'Would update' : 'Updated'} ${user.email}: ${previousType || 'null'} → organization (Orgs: ${organizations})`);
    
    return true;
  }

  /**
   * Run the migration
   */
  async run() {
    console.log('🚀 Starting Organization User Type Migration');
    console.log(`Mode: ${this.dryRun ? 'DRY RUN' : 'LIVE UPDATE'}`);
    console.log('');

    try {
      // Get super_user role
      const superUserRoleId = await this.getSuperUserRole();
      console.log(`✅ Found super_user role: ${superUserRoleId}`);

      // Find all organization super users
      const orgSuperUsers = await this.findOrganizationSuperUsers(superUserRoleId);
      this.stats.totalUsers = orgSuperUsers.length;

      console.log(`📊 Found ${orgSuperUsers.length} users who are super_users of organizations`);
      console.log('');

      if (orgSuperUsers.length === 0) {
        console.log('✅ No users need migration');
        return;
      }

      // Process each user
      for (const user of orgSuperUsers) {
        this.stats.usersChecked++;
        
        try {
          await this.updateUserType(user);
        } catch (error) {
          this.stats.errors++;
          console.error(`❌ Error updating ${user.email}:`, error.message);
        }
      }

      // Print summary
      console.log('');
      console.log('📊 Migration Summary:');
      console.log(`   Total users found: ${this.stats.totalUsers}`);
      console.log(`   Users checked: ${this.stats.usersChecked}`);
      console.log(`   Users ${this.dryRun ? 'would be updated' : 'updated'}: ${this.stats.usersUpdated}`);
      console.log(`   Users skipped: ${this.stats.usersSkipped}`);
      console.log(`   Errors: ${this.stats.errors}`);
      
      if (this.dryRun) {
        console.log('');
        console.log('🔍 This was a dry run. To apply changes, run without --dry-run flag');
      }

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');
const verbose = args.includes('--verbose');

// Run migration
async function main() {
  const migration = new OrganizationUserMigration({ dryRun, verbose });
  
  await migration.connect();
  await migration.run();
  await migration.disconnect();
  
  console.log('✅ Migration completed');
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = OrganizationUserMigration;
