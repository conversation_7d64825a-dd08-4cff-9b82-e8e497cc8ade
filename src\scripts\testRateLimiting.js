#!/usr/bin/env node

/**
 * Rate Limiting Test Script
 * 
 * This script tests the rate limiting functionality by making multiple requests
 * to different endpoints and verifying that rate limits are enforced correctly.
 * 
 * Usage: node src/scripts/testRateLimiting.js
 */

const axios = require('axios');

class RateLimitTester {
  constructor(baseURL = 'http://localhost:3000') {
    this.baseURL = baseURL;
    this.results = [];
  }

  /**
   * Make a request and capture rate limit headers
   */
  async makeRequest(endpoint, options = {}) {
    try {
      const response = await axios({
        url: `${this.baseURL}${endpoint}`,
        method: options.method || 'GET',
        headers: options.headers || {},
        data: options.data,
        validateStatus: () => true // Don't throw on 4xx/5xx
      });

      return {
        status: response.status,
        headers: {
          'x-ratelimit-limit': response.headers['x-ratelimit-limit'],
          'x-ratelimit-remaining': response.headers['x-ratelimit-remaining'],
          'x-ratelimit-reset': response.headers['x-ratelimit-reset'],
          'retry-after': response.headers['retry-after']
        },
        data: response.data
      };
    } catch (error) {
      return {
        status: 0,
        error: error.message,
        headers: {}
      };
    }
  }

  /**
   * Test general API rate limiting
   */
  async testGeneralRateLimit() {
    console.log('\n🧪 Testing General API Rate Limiting...');
    
    const endpoint = '/api/v1/users/me';
    const maxRequests = 10; // Test with small number
    
    for (let i = 1; i <= maxRequests; i++) {
      const result = await this.makeRequest(endpoint);
      
      console.log(`Request ${i}: Status ${result.status}, Remaining: ${result.headers['x-ratelimit-remaining']}`);
      
      if (result.status === 429) {
        console.log(`✅ Rate limit triggered at request ${i}`);
        console.log(`   Retry-After: ${result.headers['retry-after']} seconds`);
        console.log(`   Error: ${result.data?.error?.code}`);
        break;
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Test authentication rate limiting
   */
  async testAuthRateLimit() {
    console.log('\n🔐 Testing Authentication Rate Limiting...');
    
    const endpoint = '/api/v1/auth/login';
    const maxRequests = 15; // Should hit auth limit (10 in prod, 20 in dev)
    
    for (let i = 1; i <= maxRequests; i++) {
      const result = await this.makeRequest(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        data: {
          identifier: '<EMAIL>',
          password: 'wrongpassword'
        }
      });
      
      console.log(`Login attempt ${i}: Status ${result.status}, Remaining: ${result.headers['x-ratelimit-remaining']}`);
      
      if (result.status === 429) {
        console.log(`✅ Auth rate limit triggered at attempt ${i}`);
        console.log(`   Retry-After: ${result.headers['retry-after']} seconds`);
        console.log(`   Error: ${result.data?.error?.code}`);
        break;
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  /**
   * Test organization creation rate limiting
   */
  async testOrgCreationRateLimit() {
    console.log('\n🏢 Testing Organization Creation Rate Limiting...');
    
    const endpoint = '/api/v1/organizations/create';
    const maxRequests = 5; // Should hit org creation limit (3 in prod, 10 in dev)
    
    for (let i = 1; i <= maxRequests; i++) {
      const result = await this.makeRequest(endpoint, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer fake-token' // Will fail auth but should hit rate limit first
        },
        data: {
          name: `Test Organization ${i}`,
          subdomain: `test-org-${i}`
        }
      });
      
      console.log(`Org creation ${i}: Status ${result.status}, Remaining: ${result.headers['x-ratelimit-remaining']}`);
      
      if (result.status === 429) {
        console.log(`✅ Organization creation rate limit triggered at attempt ${i}`);
        console.log(`   Retry-After: ${result.headers['retry-after']} seconds`);
        console.log(`   Error: ${result.data?.error?.code}`);
        break;
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  }

  /**
   * Test password reset rate limiting
   */
  async testPasswordResetRateLimit() {
    console.log('\n🔑 Testing Password Reset Rate Limiting...');
    
    const endpoint = '/api/v1/auth/request-reset';
    const maxRequests = 6; // Should hit password reset limit (3 in prod, 10 in dev)
    
    for (let i = 1; i <= maxRequests; i++) {
      const result = await this.makeRequest(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        data: {
          email: '<EMAIL>'
        }
      });
      
      console.log(`Password reset ${i}: Status ${result.status}, Remaining: ${result.headers['x-ratelimit-remaining']}`);
      
      if (result.status === 429) {
        console.log(`✅ Password reset rate limit triggered at attempt ${i}`);
        console.log(`   Retry-After: ${result.headers['retry-after']} seconds`);
        console.log(`   Error: ${result.data?.error?.code}`);
        break;
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  /**
   * Test rate limit headers
   */
  async testRateLimitHeaders() {
    console.log('\n📊 Testing Rate Limit Headers...');
    
    const result = await this.makeRequest('/api/v1/users/me');
    
    console.log('Rate Limit Headers:');
    console.log(`   X-RateLimit-Limit: ${result.headers['x-ratelimit-limit']}`);
    console.log(`   X-RateLimit-Remaining: ${result.headers['x-ratelimit-remaining']}`);
    console.log(`   X-RateLimit-Reset: ${result.headers['x-ratelimit-reset']}`);
    
    if (result.headers['x-ratelimit-reset']) {
      const resetTime = new Date(parseInt(result.headers['x-ratelimit-reset']) * 1000);
      console.log(`   Reset Time: ${resetTime.toISOString()}`);
    }
  }

  /**
   * Test different IP addresses (simulated)
   */
  async testDifferentIPs() {
    console.log('\n🌐 Testing Different IP Addresses...');
    
    const ips = ['*************', '*********', '***********'];
    
    for (const ip of ips) {
      const result = await this.makeRequest('/api/v1/users/me', {
        headers: {
          'X-Forwarded-For': ip,
          'X-Real-IP': ip
        }
      });
      
      console.log(`IP ${ip}: Status ${result.status}, Remaining: ${result.headers['x-ratelimit-remaining']}`);
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting Rate Limiting Tests...');
    console.log(`📍 Testing against: ${this.baseURL}`);
    
    try {
      // Test server connectivity
      const healthCheck = await this.makeRequest('/health');
      if (healthCheck.status === 0) {
        console.log('❌ Server is not running. Please start the server first.');
        return;
      }
      
      await this.testRateLimitHeaders();
      await this.testGeneralRateLimit();
      await this.testAuthRateLimit();
      await this.testPasswordResetRateLimit();
      await this.testOrgCreationRateLimit();
      await this.testDifferentIPs();
      
      console.log('\n🎉 Rate limiting tests completed!');
      console.log('\n💡 Notes:');
      console.log('   - Rate limits vary between development and production environments');
      console.log('   - Authenticated users typically get higher limits');
      console.log('   - Redis-backed rate limiting provides better accuracy');
      console.log('   - Check server logs for detailed rate limiting information');
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new RateLimitTester();
  tester.runAllTests();
}

module.exports = RateLimitTester;
