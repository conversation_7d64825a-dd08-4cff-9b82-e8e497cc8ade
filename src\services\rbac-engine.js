// services/rbac-engine.js
const User = require('../models/User');
const Role = require('../models/Role');
const RBACRole = require('../models/RBACRole');
const NodeCache = require('node-cache');
const logger = require('./logger');

/**
 * Enhanced RBAC Engine with Centralized Roles Database
 *
 * This engine serves as the authoritative source for all permission checking,
 * role validation, and privilege verification using the dedicated RBAC roles database.
 */

// Cache for frequently accessed roles and permissions (TTL: 5 minutes)
const roleCache = new NodeCache({
  stdTTL: 300,
  checkperiod: 60,
  useClones: false
});

// Cache for permission check results (TTL: 1 minute for faster responses)
const permissionCache = new NodeCache({
  stdTTL: 60,
  checkperiod: 30,
  useClones: false
});

const RESOURCES = {
  USER_OBJECT: 'user_object',
  AUTH_PREV: 'auth_prev',
  EVENT: 'event',
  TICKET: 'ticket',
  ORG_OBJECT: 'org_object',
  ROLE_OBJECT: 'role_object',
  AUDIT_LOG: 'audit_log',
  SETTINGS: 'settings',
  // Legacy compatibility
  USER: 'user',
  ORG: 'org',
  ROLE: 'role'
};

const ACTIONS = {
  CREATE:     'create',
  READ:       'read',
  UPDATE:     'update',
  DELETE:     'delete',
  BAN:        'ban',
  ACTIVATE:   'activate',
  DEACTIVATE: 'deactivate',
  SHARE:      'share',
  RESET:      'reset',
  ASSIGN:     'assign',
  APPROVE:    'approve',
  EXPORT:     'export',
  IMPORT:     'import',
  CONFIGURE:  'configure',
  MONITOR:    'monitor',
  AUDIT:      'audit',
  BACKUP:     'backup',
  RESTORE:    'restore',
  PUBLISH:    'publish',
  MODERATE:   'moderate',
  ALL:        '*'
};

/**
 * Enhanced permission checking using centralized RBAC roles database
 * @param {Object} user - User object with roles and system privileges
 * @param {String} orgId - Organization ID (optional for system-wide operations)
 * @param {String} permission - Permission string (e.g., 'user_object:create')
 * @returns {Boolean} - True if user has permission
 */
async function hasPermission(user, orgId, permission) {
  const startTime = Date.now();
  const correlationId = logger.generateCorrelationId();

  try {
    // Input validation
    if (!user || !permission) {
      logger.warn('RBAC Engine: Invalid input parameters', {
        component: 'rbac-engine',
        operation: 'has_permission',
        correlation_id: correlationId,
        metadata: {
          has_user: !!user,
          has_permission: !!permission,
          org_id: orgId
        }
      });
      return false;
    }

    // Parse permission string
    const [resource, action] = permission.split(':');
    if (!resource || !action) {
      logger.warn('RBAC Engine: Invalid permission format', {
        component: 'rbac-engine',
        operation: 'has_permission',
        correlation_id: correlationId,
        metadata: {
          permission,
          parsed_resource: resource,
          parsed_action: action
        }
      });
      return false;
    }

    // Create cache key for permission check
    const cacheKey = `perm:${user._id}:${orgId || 'system'}:${permission}`;
    const cachedResult = permissionCache.get(cacheKey);

    if (cachedResult !== undefined) {
      logger.debug('RBAC Engine: Permission check cache hit', {
        component: 'rbac-engine',
        operation: 'has_permission_cached',
        correlation_id: correlationId,
        metadata: {
          user_id: user._id,
          permission,
          org_id: orgId,
          cached_result: cachedResult,
          duration_ms: Date.now() - startTime
        }
      });
      return cachedResult;
    }

    // Check system privileges first (God Super User, System Admin, etc.)
    if (user.systemPrivileges && user.systemPrivileges.length > 0) {
      const hasSystemPermission = await checkSystemPrivileges(user, resource, action, correlationId);
      if (hasSystemPermission) {
        permissionCache.set(cacheKey, true);
        return true;
      }
    }

    // Check organization-based roles using centralized RBAC database
    const hasRolePermission = await checkRolePermissions(user, orgId, resource, action, correlationId);

    // Cache the result
    permissionCache.set(cacheKey, hasRolePermission);

    logger.info('RBAC Engine: Permission check completed', {
      component: 'rbac-engine',
      operation: 'has_permission',
      correlation_id: correlationId,
      metadata: {
        user_id: user._id,
        permission,
        org_id: orgId,
        result: hasRolePermission,
        duration_ms: Date.now() - startTime
      }
    });

    return hasRolePermission;

  } catch (error) {
    logger.error('RBAC Engine: Permission check failed', error, {
      component: 'rbac-engine',
      operation: 'has_permission',
      correlation_id: correlationId,
      metadata: {
        user_id: user._id,
        permission,
        org_id: orgId,
        duration_ms: Date.now() - startTime
      }
    });
    return false;
  }
}

/**
 * Check system privileges (God Super User, System Admin, etc.)
 * @param {Object} user - User object
 * @param {String} resource - Resource name
 * @param {String} action - Action name
 * @param {String} correlationId - Correlation ID for logging
 * @returns {Boolean} - True if user has system privilege for this permission
 */
async function checkSystemPrivileges(user, resource, action, correlationId) {
  try {
    for (const privilege of user.systemPrivileges) {
      // God Super User has unrestricted access
      if (privilege.level === 'god_super_user') {
        logger.info('RBAC Engine: God Super User privilege granted', {
          component: 'rbac-engine',
          operation: 'check_system_privileges',
          correlation_id: correlationId,
          metadata: {
            user_id: user._id,
            privilege_level: 'god_super_user',
            resource,
            action
          }
        });
        return true;
      }

      // System Admin has broad access
      if (privilege.level === 'system_admin') {
        const systemAdminResources = ['user_object', 'org_object', 'role_object', 'audit_log', 'settings'];
        if (systemAdminResources.includes(resource)) {
          logger.info('RBAC Engine: System Admin privilege granted', {
            component: 'rbac-engine',
            operation: 'check_system_privileges',
            correlation_id: correlationId,
            metadata: {
              user_id: user._id,
              privilege_level: 'system_admin',
              resource,
              action
            }
          });
          return true;
        }
      }

      // Support Admin has limited access
      if (privilege.level === 'support_admin') {
        const supportResources = ['user_object', 'ticket', 'audit_log'];
        const supportActions = ['read', 'update'];
        if (supportResources.includes(resource) && supportActions.includes(action)) {
          return true;
        }
      }

      // Audit Viewer has read-only access to audit logs
      if (privilege.level === 'audit_viewer') {
        if (resource === 'audit_log' && action === 'read') {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    logger.error('RBAC Engine: System privilege check failed', error, {
      component: 'rbac-engine',
      operation: 'check_system_privileges',
      correlation_id: correlationId,
      metadata: {
        user_id: user._id,
        resource,
        action
      }
    });
    return false;
  }
}

/**
 * Check role-based permissions using centralized RBAC database
 * @param {Object} user - User object
 * @param {String} orgId - Organization ID
 * @param {String} resource - Resource name
 * @param {String} action - Action name
 * @param {String} correlationId - Correlation ID for logging
 * @returns {Boolean} - True if user has role-based permission
 */
async function checkRolePermissions(user, orgId, resource, action, correlationId) {
  try {
    if (!user.roles || !Array.isArray(user.roles)) {
      return false;
    }

    for (const userRole of user.roles) {
      // Skip if not for this organization (unless system-wide operation)
      if (orgId && userRole.org && userRole.org.toString() !== orgId.toString()) {
        continue;
      }

      // Get role from centralized RBAC database
      const rbacRole = await getRBACRole(userRole.role, orgId, correlationId);
      if (!rbacRole) continue;

      // Check if role has the required permission
      if (rbacRole.hasPermission(resource, action)) {
        // Update role usage statistics
        await rbacRole.updateUsage();

        logger.info('RBAC Engine: Role permission granted', {
          component: 'rbac-engine',
          operation: 'check_role_permissions',
          correlation_id: correlationId,
          metadata: {
            user_id: user._id,
            role_id: rbacRole._id,
            role_name: rbacRole.name,
            resource,
            action,
            org_id: orgId
          }
        });

        return true;
      }
    }

    return false;
  } catch (error) {
    logger.error('RBAC Engine: Role permission check failed', error, {
      component: 'rbac-engine',
      operation: 'check_role_permissions',
      correlation_id: correlationId,
      metadata: {
        user_id: user._id,
        resource,
        action,
        org_id: orgId
      }
    });
    return false;
  }
}

/**
 * Get RBAC role from centralized database with caching
 * @param {String|ObjectId} roleId - Role ID or role object
 * @param {String} orgId - Organization ID
 * @param {String} correlationId - Correlation ID for logging
 * @returns {Object|null} - RBAC role object or null
 */
async function getRBACRole(roleId, orgId, correlationId) {
  try {
    // Handle different role reference formats
    let actualRoleId = roleId;
    if (typeof roleId === 'object' && roleId._id) {
      actualRoleId = roleId._id;
    }

    // Create cache key
    const cacheKey = `role:${actualRoleId}:${orgId || 'system'}`;
    let rbacRole = roleCache.get(cacheKey);

    if (rbacRole) {
      logger.debug('RBAC Engine: Role cache hit', {
        component: 'rbac-engine',
        operation: 'get_rbac_role_cached',
        correlation_id: correlationId,
        metadata: {
          role_id: actualRoleId,
          org_id: orgId,
          cache_key: cacheKey
        }
      });
      return rbacRole;
    }

    // Try to find in centralized RBAC database first
    rbacRole = await RBACRole.findById(actualRoleId);

    if (!rbacRole) {
      // Fallback to legacy Role model for backward compatibility
      const legacyRole = await Role.findById(actualRoleId);
      if (legacyRole) {
        // Convert legacy role to RBAC format
        rbacRole = await convertLegacyRoleToRBAC(legacyRole, orgId, correlationId);
      }
    }

    if (rbacRole) {
      // Cache the role for future use
      roleCache.set(cacheKey, rbacRole);

      logger.debug('RBAC Engine: Role loaded and cached', {
        component: 'rbac-engine',
        operation: 'get_rbac_role',
        correlation_id: correlationId,
        metadata: {
          role_id: actualRoleId,
          role_name: rbacRole.name,
          org_id: orgId,
          is_legacy_conversion: !rbacRole.scope
        }
      });
    }

    return rbacRole;
  } catch (error) {
    logger.error('RBAC Engine: Failed to get RBAC role', error, {
      component: 'rbac-engine',
      operation: 'get_rbac_role',
      correlation_id: correlationId,
      metadata: {
        role_id: roleId,
        org_id: orgId
      }
    });
    return null;
  }
}

/**
 * Convert legacy Role to RBAC format for backward compatibility
 * @param {Object} legacyRole - Legacy role object
 * @param {String} orgId - Organization ID
 * @param {String} correlationId - Correlation ID for logging
 * @returns {Object} - RBAC-compatible role object
 */
async function convertLegacyRoleToRBAC(legacyRole, orgId, correlationId) {
  try {
    // Create a temporary RBAC-compatible object
    const rbacCompatibleRole = {
      _id: legacyRole._id,
      name: legacyRole.name,
      displayName: legacyRole.name,
      description: legacyRole.description || `Legacy role: ${legacyRole.name}`,
      scope: 'organization',
      organization: orgId,
      permissions: legacyRole.permissions || [],
      hierarchy: legacyRole.hierarchy || { level: 10, type: 'custom' },
      metadata: {
        isActive: true,
        isSystem: false,
        isBuiltIn: false,
        category: 'custom'
      },

      // Add RBAC methods
      hasPermission: function(resource, action) {
        return this.permissions.some(permission => {
          return permission.resource === resource &&
                 (permission.actions.includes(action) || permission.actions.includes('*'));
        });
      },

      updateUsage: async function() {
        // For legacy roles, we don't update usage stats
        return Promise.resolve();
      }
    };

    logger.info('RBAC Engine: Legacy role converted to RBAC format', {
      component: 'rbac-engine',
      operation: 'convert_legacy_role',
      correlation_id: correlationId,
      metadata: {
        legacy_role_id: legacyRole._id,
        legacy_role_name: legacyRole.name,
        org_id: orgId,
        permissions_count: rbacCompatibleRole.permissions.length
      }
    });

    return rbacCompatibleRole;
  } catch (error) {
    logger.error('RBAC Engine: Failed to convert legacy role', error, {
      component: 'rbac-engine',
      operation: 'convert_legacy_role',
      correlation_id: correlationId,
      metadata: {
        legacy_role_id: legacyRole._id,
        org_id: orgId
      }
    });
    return null;
  }
}

/**
 * Clear caches (useful for testing or when roles are updated)
 */
function clearCaches() {
  roleCache.flushAll();
  permissionCache.flushAll();
  logger.info('RBAC Engine: All caches cleared', {
    component: 'rbac-engine',
    operation: 'clear_caches'
  });
}

/**
 * Get cache statistics
 */
function getCacheStats() {
  return {
    roleCache: roleCache.getStats(),
    permissionCache: permissionCache.getStats()
  };
}

module.exports = {
  hasPermission,
  checkSystemPrivileges,
  checkRolePermissions,
  getRBACRole,
  convertLegacyRoleToRBAC,
  clearCaches,
  getCacheStats,
  RESOURCES,
  ACTIONS
};
