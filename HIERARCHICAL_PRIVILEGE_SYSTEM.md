# Hierarchical User Privilege System

## Overview

This document describes the comprehensive hierarchical user privilege system implemented in the application. The system provides multiple levels of access control, from system-wide God Super User privileges to organization-scoped roles with granular permissions.

## Privilege Hierarchy

### 1. God Super User (System-Wide)
- **Level**: Highest (Level 0)
- **Scope**: Entire system across all organizations
- **Access**: Unrestricted access to all resources and actions
- **Limitation**: Only one God Super User can exist at a time
- **Audit**: All actions are logged for compliance
- **Security**: Enhanced security features (MFA, IP restrictions, session timeout)

### 2. System Privileges
- **system_admin**: System administration access
- **support_admin**: Support and maintenance access  
- **audit_viewer**: Read-only audit access

### 3. Organization Super User
- **Level**: Organization-highest (Level 1)
- **Scope**: Complete access within specific organization
- **Access**: All permissions for all resources within assigned organization
- **Limitation**: Multiple users can have this role, each scoped to their organization
- **Cross-org**: Cannot access other organizations

### 4. Standard Hierarchical Roles

#### View Manager (Level 2)
- **Access**: Read-only access to all organization data
- **Use Case**: Auditors, compliance officers, executives
- **Permissions**: Read access to all resources

#### Admin (Level 3)
- **Access**: Create, read, update permissions (limited delete)
- **Use Case**: Department administrators, senior managers
- **Permissions**: Most administrative functions except critical deletions

#### Manager (Level 4)
- **Access**: Read and update permissions for specific resources
- **Use Case**: Team leads, project managers
- **Scope**: Team or department level access

#### Member (Level 5)
- **Access**: Basic user access with read and limited update
- **Use Case**: Regular employees, team members
- **Scope**: Self-service and assigned tasks

#### Guest (Level 6)
- **Access**: Minimal read-only access to public resources
- **Use Case**: External users, temporary access
- **Scope**: Public information only

## Implementation Details

### Database Schema

#### Enhanced User Model
```javascript
// System-wide privileges
systemPrivileges: [{
  level: String, // 'god_super_user', 'system_admin', etc.
  grantedAt: Date,
  grantedBy: ObjectId,
  security: {
    allowedIPs: [String],
    requireMFA: Boolean,
    sessionTimeout: Number
  },
  auditLog: [{
    action: String,
    timestamp: Date,
    ipAddress: String,
    userAgent: String,
    details: Mixed
  }]
}]

// Organization-scoped roles
roles: [{
  org: ObjectId,
  role: ObjectId,
  assignedAt: Date,
  assignedBy: ObjectId,
  permissions: {
    overrides: [{
      resource: String,
      actions: [String],
      granted: Boolean
    }],
    expiresAt: Date
  }
}]
```

#### Enhanced Role Model
```javascript
// Role hierarchy
hierarchy: {
  level: Number, // 1-10, lower = higher privilege
  type: String,  // 'system', 'organization', 'custom', 'temporary'
  parent: ObjectId
}

// Enhanced permissions with constraints
permissions: [{
  resource: String,
  actions: [String],
  constraints: {
    scope: String, // 'self', 'team', 'department', 'organization'
    timeRestrictions: {
      allowedHours: { start: Number, end: Number },
      allowedDays: [Number],
      timezone: String
    },
    resourceLimits: {
      maxItems: Number,
      maxFileSize: Number,
      allowedFileTypes: [String],
      rateLimits: {
        requestsPerHour: Number,
        requestsPerDay: Number
      }
    }
  }
}]
```

### RBAC Middleware Enhancement

The RBAC middleware now supports hierarchical privilege checking:

1. **God Super User Check**: Bypasses all permission checks with audit logging
2. **Organization Super User Check**: Bypasses checks within organization scope
3. **System Privilege Check**: Handles system-level privileges with audit logging
4. **Standard RBAC Check**: Traditional role-based permission checking
5. **Legacy Compatibility**: Maintains backward compatibility with existing roles

### API Endpoints

#### Privilege Management
- `POST /api/v1/privileges/god-super-user` - Assign God Super User privileges
- `POST /api/v1/privileges/org-super-user` - Assign Organization Super User role
- `POST /api/v1/privileges/roles/initialize/{orgId}` - Initialize system roles
- `GET /api/v1/privileges/user/{userId}/roles` - Get user's roles and privileges

#### Role Management (Enhanced)
- `POST /api/v1/roles` - Create custom roles
- `GET /api/v1/roles/org/{orgId}` - Get organization roles
- `PATCH /api/v1/roles/users/{userId}/org/{orgId}/assign-role` - Assign roles

## Usage Examples

### 1. Assign God Super User
```javascript
// Only current God Super User can assign
POST /api/v1/privileges/god-super-user
{
  "userId": "user_id_here",
  "securitySettings": {
    "allowedIPs": ["***********/24"],
    "requireMFA": true,
    "sessionTimeout": 30
  }
}
```

### 2. Assign Organization Super User via Subdomain
```javascript
POST /api/v1/privileges/org-super-user
{
  "userIdentifier": "<EMAIL>",
  "orgIdentifier": "acme-corp"
}
```

### 3. Initialize System Roles for Organization
```javascript
POST /api/v1/privileges/roles/initialize/org_id_here
// Creates all predefined system roles for the organization
```

### 4. Check User Privileges in Code
```javascript
// Check if user is God Super User
if (user.isGodSuperUser()) {
  // Grant unrestricted access
}

// Check if user is org super user for specific org
if (user.isOrgSuperUser(orgId)) {
  // Grant org-wide access
}

// Get user's role in organization
const roleInOrg = user.getRoleInOrganization(orgId);
```

## Migration

### Running the Migration
```bash
# Dry run to see what would be changed
node src/scripts/run-migration.js --migration=hierarchical-privileges --dry-run

# Run migration and assign God Super User
node src/scripts/run-migration.js --migration=hierarchical-privileges --god-super-user=<EMAIL>

# Rollback if needed (use with caution)
node src/scripts/run-migration.js --migration=hierarchical-privileges --rollback
```

### Migration Process
1. **User Schema Update**: Adds systemPrivileges, security, and profile fields
2. **Role Creation**: Creates system roles for all organizations
3. **Role Assignment Migration**: Updates existing role assignments to new format
4. **God Super User Assignment**: Optionally assigns God Super User privileges

## Security Features

### Audit Logging
- All God Super User actions are logged with full context
- System privilege usage is tracked
- Privilege escalations are recorded
- IP addresses and user agents are captured

### Security Safeguards
- **Single God Super User**: Only one can exist at a time
- **IP Restrictions**: God Super User can be restricted to specific IPs
- **MFA Requirements**: Enhanced accounts require multi-factor authentication
- **Session Timeouts**: Configurable session timeouts for high-privilege accounts
- **Audit Trails**: Comprehensive logging for compliance

### Error Handling
- **Structured Error Responses**: Consistent error format with codes
- **Detailed Logging**: Component-based logging with correlation IDs
- **Graceful Degradation**: System continues to function if privilege services fail
- **Validation**: Comprehensive input validation and sanitization

## Backward Compatibility

The system maintains full backward compatibility:
- **Existing Roles**: All current roles continue to work
- **Legacy Superadmin**: Old superadmin patterns are still supported
- **API Compatibility**: No breaking changes to existing endpoints
- **Migration Safety**: Migration can be run in dry-run mode first

## Performance Considerations

- **Caching**: Role permissions are cached for performance
- **Indexing**: Database indexes on privilege and role fields
- **Lazy Loading**: Privilege checks are optimized for common cases
- **Correlation IDs**: Request tracking for performance monitoring

## Monitoring and Observability

### Structured Logging
```json
{
  "timestamp": "2025-05-24T17:41:02.412Z",
  "level": "INFO",
  "message": "God Super User bypass granted",
  "component": "rbac-middleware",
  "operation": "god_super_user_check",
  "correlation_id": "3f1ecf690b2e0ee9",
  "metadata": {
    "user_id": "683204ae94c218731eb1ca70",
    "permission_bypassed": "user:delete",
    "privilege_level": "god_super_user",
    "duration_ms": 5
  }
}
```

### Metrics
- Permission check duration
- Privilege bypass frequency
- Failed access attempts
- Role assignment changes

## Best Practices

1. **Principle of Least Privilege**: Assign minimum necessary permissions
2. **Regular Audits**: Review privilege assignments regularly
3. **Temporary Roles**: Use time-limited roles when appropriate
4. **Documentation**: Document all privilege escalations
5. **Monitoring**: Monitor high-privilege account usage
6. **Backup**: Backup user/role data before major changes

## Troubleshooting

### Common Issues
1. **Permission Denied**: Check user's role assignments and hierarchy levels
2. **God Super User Assignment**: Ensure only current God Super User assigns new one
3. **Organization Access**: Verify user has role in correct organization
4. **Migration Issues**: Run in dry-run mode first to identify problems

### Debug Information
- Check `req.rbacContext` for permission check details
- Review audit logs for privilege usage
- Use correlation IDs to trace requests across services
- Monitor structured logs for error patterns

This hierarchical privilege system provides enterprise-grade access control while maintaining simplicity and backward compatibility.
