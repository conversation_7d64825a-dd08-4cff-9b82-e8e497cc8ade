// src/middleware/auditMiddleware.js
const auditLogger = require('../services/auditLogger');
const logger = require('../services/logger');

/**
 * Comprehensive Audit Logging Middleware
 * Captures all API requests and responses for audit trail
 */
class AuditMiddleware {
  constructor() {
    this.excludedPaths = [
      '/health',
      '/api-docs',
      '/swagger.json',
      '/favicon.ico',
      '/docs'
    ];
    
    this.excludedMethods = ['OPTIONS', 'HEAD'];
    
    // Paths that contain sensitive data requiring special handling
    this.sensitivePaths = [
      '/api/v1/auth/login',
      '/api/v1/auth/register',
      '/api/v1/auth/reset-password'
    ];
  }

  /**
   * Main audit middleware function
   */
  middleware() {
    return async (req, res, next) => {
      // Skip excluded paths and methods
      if (this.shouldSkipAudit(req)) {
        return next();
      }

      const startTime = Date.now();
      const requestId = this.generateRequestId();
      
      // Add request ID to request for tracking
      req.auditRequestId = requestId;
      
      // Capture original request data
      const originalRequestData = this.captureRequestData(req);
      
      // Capture original response methods
      const originalSend = res.send;
      const originalJson = res.json;
      const originalEnd = res.end;
      
      let responseData = null;
      let responseCaptured = false;

      // Override response methods to capture response data
      res.send = function(data) {
        if (!responseCaptured) {
          responseData = data;
          responseCaptured = true;
        }
        return originalSend.call(this, data);
      };

      res.json = function(data) {
        if (!responseCaptured) {
          responseData = data;
          responseCaptured = true;
        }
        return originalJson.call(this, data);
      };

      res.end = function(data) {
        if (!responseCaptured && data) {
          responseData = data;
          responseCaptured = true;
        }
        return originalEnd.call(this, data);
      };

      // Handle response completion
      res.on('finish', async () => {
        try {
          const endTime = Date.now();
          const executionTime = endTime - startTime;
          
          await this.logAuditEntry({
            req,
            res,
            requestId,
            originalRequestData,
            responseData,
            executionTime,
            startTime: new Date(startTime),
            endTime: new Date(endTime)
          });
        } catch (error) {
          logger.error('Failed to log audit entry in middleware', {
            component: 'audit-middleware',
            operation: 'log_audit_entry_error',
            error: error.message,
            metadata: {
              request_id: requestId,
              endpoint: req.originalUrl,
              method: req.method
            }
          });
        }
      });

      next();
    };
  }

  /**
   * Check if request should be skipped for auditing
   */
  shouldSkipAudit(req) {
    // Skip excluded methods
    if (this.excludedMethods.includes(req.method)) {
      return true;
    }

    // Skip excluded paths
    const path = req.originalUrl || req.url;
    return this.excludedPaths.some(excludedPath => 
      path.startsWith(excludedPath)
    );
  }

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Capture request data safely
   */
  captureRequestData(req) {
    try {
      return {
        method: req.method,
        url: req.originalUrl || req.url,
        headers: this.sanitizeHeaders(req.headers),
        query: req.query,
        params: req.params,
        body: this.isSensitivePath(req) ? '[REDACTED]' : req.body,
        ip: req.ip || req.connection?.remoteAddress,
        userAgent: req.get('User-Agent')
      };
    } catch (error) {
      logger.error('Failed to capture request data', {
        component: 'audit-middleware',
        operation: 'capture_request_data_error',
        error: error.message
      });
      return {};
    }
  }

  /**
   * Check if path contains sensitive data
   */
  isSensitivePath(req) {
    const path = req.originalUrl || req.url;
    return this.sensitivePaths.some(sensitivePath => 
      path.includes(sensitivePath)
    );
  }

  /**
   * Sanitize headers to remove sensitive information
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    
    // Remove or mask sensitive headers
    const sensitiveHeaders = [
      'authorization', 'cookie', 'x-api-key', 'x-auth-token'
    ];
    
    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }

  /**
   * Log comprehensive audit entry
   */
  async logAuditEntry(data) {
    const { req, res, requestId, originalRequestData, responseData, executionTime, startTime } = data;
    
    // Extract user information from request
    const user = req.user || {};
    
    // Determine resource information
    const resourceInfo = this.extractResourceInfo(req);
    
    // Build audit log data
    const auditData = {
      req,
      res,
      requestId,
      user: {
        id: user._id || user.id,
        email: user.email
      },
      endpoint: req.originalUrl || req.url,
      method: req.method,
      requestData: originalRequestData.body,
      responseData: this.sanitizeResponseData(responseData, res.statusCode),
      responseStatus: res.statusCode,
      executionTime,
      resourceType: resourceInfo.type,
      resourceId: resourceInfo.id,
      organizationContext: this.extractOrganizationContext(req),
      roleContext: this.extractRoleContext(req),
      identifierTypes: this.extractIdentifierTypes(req),
      isAdminAction: this.isAdminAction(req, user),
      isPrivilegeEscalation: this.isPrivilegeEscalation(req),
      isBulkOperation: this.isBulkOperation(req),
      containsPII: this.containsPII(originalRequestData.body),
      customMetadata: {
        request_size: JSON.stringify(originalRequestData.body || {}).length,
        response_size: JSON.stringify(responseData || {}).length,
        user_agent_parsed: this.parseUserAgent(req.get('User-Agent')),
        geo_location: this.extractGeoLocation(req),
        session_info: this.extractSessionInfo(req)
      }
    };

    // Log to audit system
    await auditLogger.logOperation(auditData);
  }

  /**
   * Extract resource information from request
   */
  extractResourceInfo(req) {
    const path = req.originalUrl || req.url;
    const params = req.params || {};
    
    let type = 'unknown';
    let id = null;
    
    if (path.includes('/users')) {
      type = 'user';
      id = params.userId || params.userIdOrEmail || params.id;
    } else if (path.includes('/organizations')) {
      type = 'organization';
      id = params.orgId || params.orgIdOrSubdomain || params.idOrSubdomain || params.id;
    } else if (path.includes('/roles')) {
      type = 'role';
      id = params.roleId || params.roleIdOrName || params.id;
    } else if (path.includes('/privileges')) {
      type = 'privilege';
      id = params.userId || params.userIdOrEmail;
    } else if (path.includes('/auth')) {
      type = 'auth';
      id = req.body?.identifier || req.body?.email;
    }
    
    return { type, id };
  }

  /**
   * Extract organization context from request
   */
  extractOrganizationContext(req) {
    const params = req.params || {};
    const query = req.query || {};
    const body = req.body || {};
    
    const orgId = params.orgId || params.orgIdOrSubdomain || query.orgId || body.organizationId;
    
    if (!orgId) return null;
    
    return {
      organization_id: orgId,
      organization_identifier: orgId,
      lookup_method: this.isObjectId(orgId) ? 'objectId' : 'subdomain'
    };
  }

  /**
   * Extract role context from request
   */
  extractRoleContext(req) {
    const params = req.params || {};
    const body = req.body || {};
    
    const roleId = params.roleId || params.roleIdOrName || body.roleId || body.roleName;
    
    if (!roleId) return null;
    
    return {
      role_id: roleId,
      role_identifier: roleId,
      lookup_method: this.isObjectId(roleId) ? 'objectId' : 'roleName'
    };
  }

  /**
   * Extract identifier types used in the request
   */
  extractIdentifierTypes(req) {
    const params = req.params || {};
    const identifierTypes = {};
    
    // User identifier type
    if (params.userIdOrEmail) {
      identifierTypes.user_lookup_method = this.isEmail(params.userIdOrEmail) ? 'email' : 'objectId';
    }
    
    // Organization identifier type
    if (params.orgIdOrSubdomain || params.idOrSubdomain) {
      const orgIdentifier = params.orgIdOrSubdomain || params.idOrSubdomain;
      identifierTypes.organization_lookup_method = this.isObjectId(orgIdentifier) ? 'objectId' : 'subdomain';
    }
    
    // Role identifier type
    if (params.roleIdOrName) {
      identifierTypes.role_lookup_method = this.isObjectId(params.roleIdOrName) ? 'objectId' : 'roleName';
    }
    
    return identifierTypes;
  }

  /**
   * Utility methods
   */
  isObjectId(str) {
    return /^[0-9a-fA-F]{24}$/.test(str);
  }

  isEmail(str) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str);
  }

  isAdminAction(req, user) {
    const path = req.originalUrl || req.url;
    return path.includes('/privileges') || 
           path.includes('/admin') ||
           (user.roles && user.roles.some(role => role.level <= 2));
  }

  isPrivilegeEscalation(req) {
    const path = req.originalUrl || req.url;
    return path.includes('/god-super-user') || 
           path.includes('/assign-role') ||
           path.includes('/privileges');
  }

  isBulkOperation(req) {
    const body = req.body || {};
    return Array.isArray(body) || 
           (body.users && Array.isArray(body.users)) ||
           (body.operations && Array.isArray(body.operations));
  }

  containsPII(data) {
    if (!data || typeof data !== 'object') return false;
    
    const piiFields = ['email', 'phone', 'ssn', 'address', 'name'];
    const dataStr = JSON.stringify(data).toLowerCase();
    
    return piiFields.some(field => dataStr.includes(field));
  }

  sanitizeResponseData(responseData, statusCode) {
    if (statusCode >= 400) {
      // For error responses, only log error structure, not sensitive details
      return { error: true, status: statusCode };
    }
    
    if (!responseData) return null;
    
    try {
      const parsed = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;
      
      // Remove sensitive fields from response
      const sanitized = { ...parsed };
      delete sanitized.access_token;
      delete sanitized.refresh_token;
      delete sanitized.password;
      
      return sanitized;
    } catch (error) {
      return { _parse_error: true, _original_type: typeof responseData };
    }
  }

  parseUserAgent(userAgent) {
    if (!userAgent) return null;
    
    // Simple user agent parsing
    const browser = userAgent.includes('Chrome') ? 'Chrome' :
                   userAgent.includes('Firefox') ? 'Firefox' :
                   userAgent.includes('Safari') ? 'Safari' : 'Unknown';
    
    const os = userAgent.includes('Windows') ? 'Windows' :
              userAgent.includes('Mac') ? 'macOS' :
              userAgent.includes('Linux') ? 'Linux' : 'Unknown';
    
    return { browser, os };
  }

  extractGeoLocation(req) {
    // Extract geo location from headers if available
    return {
      country: req.headers['cf-ipcountry'] || req.headers['x-country'],
      region: req.headers['cf-region'] || req.headers['x-region'],
      city: req.headers['cf-city'] || req.headers['x-city']
    };
  }

  extractSessionInfo(req) {
    return {
      session_id: req.sessionID,
      csrf_token: req.headers['x-csrf-token'],
      referer: req.headers.referer,
      origin: req.headers.origin
    };
  }
}

// Export singleton instance
module.exports = new AuditMiddleware();
