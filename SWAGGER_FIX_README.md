# 🔧 Swagger UI CORS Fix - User Registration API

## Problem Summary

The Swagger UI at `https://auth-03-0.vercel.app/api-docs` was showing a "Failed to fetch" error with the message "URL scheme must be 'http' or 'https' for CORS request" when trying to execute API calls, particularly the `/api/v1/users/register` endpoint.

**Root Cause**: The `SERVER_URL` environment variable was set to `http://localhost:3000` instead of the production URL `https://auth-03-0.vercel.app`, causing Swagger UI to attempt API calls to localhost instead of the deployed server.

## ✅ Solution Implemented

### 1. Updated Environment Configuration

- **Updated `.env`**: Changed `SERVER_URL` from `http://localhost:3000` to `https://auth-03-0.vercel.app`
- **Created `.env.local`**: For local development with `SERVER_URL=http://localhost:3000`
- **Created `.env.production`**: For production deployment with `SERVER_URL=https://auth-03-0.vercel.app`

### 2. Enhanced Package.json Scripts

Added environment switching scripts to `package.json`:

```json
{
  "scripts": {
    "env:local": "cp .env.local .env && echo \"Switched to LOCAL environment\"",
    "env:production": "cp .env.production .env && echo \"Switched to PRODUCTION environment\"",
    "start:local": "npm run env:local && npm start",
    "dev:local": "npm run env:local && npm run dev",
    "start:production": "npm run env:production && npm start",
    "dev:production": "npm run env:production && npm run dev"
  }
}
```

### 3. Vercel Environment Update Scripts

Created scripts to update Vercel environment variables:
- `update-vercel-env.sh` (Linux/Mac)
- `update-vercel-env.bat` (Windows)

## 🚀 Next Steps Required

### Step 1: Update Vercel Environment Variables

Run one of these scripts to update your Vercel environment variables:

**For Windows:**
```bash
./update-vercel-env.bat
```

**For Linux/Mac:**
```bash
chmod +x update-vercel-env.sh
./update-vercel-env.sh
```

**Or manually via Vercel CLI:**
```bash
vercel env add SERVER_URL production
# Enter: https://auth-03-0.vercel.app

vercel env add REDIRECT_URI production  
# Enter: https://auth-03-0.vercel.app/api/v1/oauth/google/callback

vercel env add EMAIL_LINK_CALLBACK_URL production
# Enter: https://auth-03-0.vercel.app/api/v1/auth/email/callback
```

### Step 2: Redeploy Application

After updating environment variables, redeploy your application:

```bash
git add .
git commit -m "Fix Swagger UI CORS issue - update environment configuration"
git push origin main
```

### Step 3: Verify the Fix

1. Wait for deployment to complete
2. Visit `https://auth-03-0.vercel.app/api-docs`
3. Try the `/api/v1/users/register` endpoint with this test data:

```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone_number": "+911234567890",
  "password": "SuperSecret123"
}
```

## 🔄 Environment Management

### For Local Development:
```bash
npm run start:local    # Switch to local env and start
npm run dev:local      # Switch to local env and start with nodemon
```

### For Production Testing:
```bash
npm run start:production    # Switch to production env and start
npm run dev:production      # Switch to production env and start with nodemon
```

### Manual Environment Switching:
```bash
npm run env:local       # Switch to local environment
npm run env:production  # Switch to production environment
```

## 📋 Environment Variables Updated

| Variable | Local Value | Production Value |
|----------|-------------|------------------|
| `SERVER_URL` | `http://localhost:3000` | `https://auth-03-0.vercel.app` |
| `REDIRECT_URI` | `http://localhost:3000/api/v1/oauth/google/callback` | `https://auth-03-0.vercel.app/api/v1/oauth/google/callback` |
| `EMAIL_LINK_CALLBACK_URL` | `http://localhost:3000/api/v1/auth/email/callback` | `https://auth-03-0.vercel.app/api/v1/auth/email/callback` |

## 🔍 How to Verify the Fix

1. **Check Swagger JSON**: Visit `https://auth-03-0.vercel.app/swagger.json` and verify that the `servers` section shows:
   ```json
   "servers": [{"url": "https://auth-03-0.vercel.app", "description": "API Server"}]
   ```

2. **Test API Endpoint**: Use the Swagger UI to test the user registration endpoint

3. **Check Browser Network Tab**: Verify that API calls are being made to `https://auth-03-0.vercel.app` instead of `localhost`

## 🛠️ Files Modified/Created

- ✏️ **Modified**: `.env` - Updated SERVER_URL to production URL
- ✏️ **Modified**: `package.json` - Added environment switching scripts
- ➕ **Created**: `.env.local` - Local development environment
- ➕ **Created**: `.env.production` - Production environment
- ➕ **Created**: `update-vercel-env.sh` - Vercel environment update script (Linux/Mac)
- ➕ **Created**: `update-vercel-env.bat` - Vercel environment update script (Windows)
- ➕ **Created**: `SWAGGER_FIX_README.md` - This documentation

## 🎯 Expected Result

After implementing these changes and redeploying:

1. ✅ Swagger UI will make API calls to the correct production URL
2. ✅ CORS errors will be resolved
3. ✅ User registration endpoint will work correctly from Swagger UI
4. ✅ All API endpoints will be testable from the documentation interface
5. ✅ Environment switching will be seamless for development vs production
