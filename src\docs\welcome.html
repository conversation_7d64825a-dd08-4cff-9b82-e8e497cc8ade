<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced RBAC API with Flexible Identifiers & Audit Logging</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --tertiary-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --quaternary-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      --dark-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      --light-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      --card-shadow: 0 20px 40px rgba(0,0,0,0.1);
      --card-shadow-hover: 0 30px 60px rgba(0,0,0,0.15);
      --text-primary: #2c3e50;
      --text-secondary: #34495e;
      --border-radius: 20px;
      --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      line-height: 1.7;
      color: var(--text-primary);
      background: var(--background-gradient);
      background-attachment: fixed;
      min-height: 100vh;
      overflow-x: hidden;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
      pointer-events: none;
      z-index: -1;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      position: relative;
      z-index: 1;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
    }

    @keyframes pulse {
      0%, 100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
    }

    .header {
      background: var(--dark-gradient);
      color: white;
      padding: 60px 40px;
      border-radius: var(--border-radius);
      margin-bottom: 40px;
      text-align: center;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
      animation: fadeInUp 0.8s ease-out;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
      transform: rotate(45deg);
      animation: float 6s ease-in-out infinite;
    }

    .header h1 {
      font-size: 3em;
      margin-bottom: 15px;
      font-weight: 800;
      position: relative;
      z-index: 2;
      text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .header .subtitle {
      font-size: 1.3em;
      opacity: 0.95;
      margin-bottom: 25px;
      position: relative;
      z-index: 2;
      font-weight: 400;
    }

    .header .version {
      background: rgba(255,255,255,0.25);
      backdrop-filter: blur(10px);
      padding: 8px 20px;
      border-radius: 25px;
      font-size: 0.95em;
      display: inline-block;
      position: relative;
      z-index: 2;
      font-weight: 600;
      border: 1px solid rgba(255,255,255,0.3);
      transition: var(--transition);
    }

    .header .version:hover {
      background: rgba(255,255,255,0.35);
      transform: translateY(-2px);
    }

    .logout-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(255,255,255,0.2);
      color: white;
      border: 1px solid rgba(255,255,255,0.3);
      padding: 8px 16px;
      border-radius: 20px;
      text-decoration: none;
      font-size: 0.9em;
      font-weight: 500;
      transition: var(--transition);
      backdrop-filter: blur(10px);
      z-index: 10;
    }

    .logout-btn:hover {
      background: rgba(255,255,255,0.3);
      transform: translateY(-2px);
      color: white;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
      gap: 30px;
      margin-bottom: 40px;
    }

    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: var(--border-radius);
      padding: 35px;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
      border: 1px solid rgba(255,255,255,0.3);
      position: relative;
      overflow: hidden;
      animation: fadeInUp 0.8s ease-out;
      animation-fill-mode: both;
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
      transition: var(--transition);
    }

    .card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: var(--card-shadow-hover);
    }

    .card:hover::before {
      left: 100%;
    }

    .card h2 {
      color: var(--text-primary);
      margin-bottom: 20px;
      font-size: 1.5em;
      font-weight: 700;
      display: flex;
      align-items: center;
      position: relative;
      z-index: 2;
    }

    .card h3 {
      color: var(--text-secondary);
      margin: 25px 0 15px 0;
      font-size: 1.2em;
      font-weight: 600;
      position: relative;
      z-index: 2;
    }

    .icon {
      width: 24px;
      height: 24px;
      margin-right: 10px;
      opacity: 0.8;
    }

    .feature-list {
      list-style: none;
      margin: 15px 0;
    }

    .feature-list li {
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      position: relative;
      padding-left: 25px;
    }

    .feature-list li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #27ae60;
      font-weight: bold;
    }

    .feature-list li:last-child {
      border-bottom: none;
    }

    .button {
      display: inline-block;
      padding: 14px 28px;
      text-decoration: none;
      border-radius: 12px;
      font-weight: 600;
      margin: 10px 10px 10px 0;
      transition: var(--transition);
      text-align: center;
      font-size: 0.95em;
      position: relative;
      overflow: hidden;
      border: none;
      cursor: pointer;
      z-index: 1;
    }

    .button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: var(--transition);
      z-index: -1;
    }

    .button:hover::before {
      left: 100%;
    }

    .button.primary {
      background: var(--primary-gradient);
      color: white;
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .button.primary:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
    }

    .button.secondary {
      background: var(--secondary-gradient);
      color: white;
      box-shadow: 0 8px 20px rgba(245, 87, 108, 0.3);
    }

    .button.secondary:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 25px rgba(245, 87, 108, 0.4);
    }

    .button.tertiary {
      background: var(--tertiary-gradient);
      color: white;
      box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
    }

    .button.tertiary:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 25px rgba(79, 172, 254, 0.4);
    }

    .button.quaternary {
      background: var(--quaternary-gradient);
      color: white;
      box-shadow: 0 8px 20px rgba(67, 233, 123, 0.3);
    }

    .button.quaternary:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 25px rgba(67, 233, 123, 0.4);
    }

    .hierarchy-table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      font-size: 0.9em;
    }

    .hierarchy-table th,
    .hierarchy-table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    .hierarchy-table th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
    }

    .hierarchy-table tr:hover {
      background: #f8f9fa;
    }

    .level-badge {
      background: #3498db;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8em;
      font-weight: bold;
    }

    .level-0 { background: #e74c3c; }
    .level-1 { background: #e67e22; }
    .level-2 { background: #f39c12; }
    .level-3 { background: #27ae60; }
    .level-4 { background: #3498db; }
    .level-5 { background: #9b59b6; }
    .level-6 { background: #95a5a6; }

    .code-example {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 15px 0;
    }

    .highlight {
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #e67e22;
      margin: 15px 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }

    .stat-card {
      background: var(--primary-gradient);
      color: white;
      padding: 25px;
      border-radius: 15px;
      text-align: center;
      position: relative;
      overflow: hidden;
      transition: var(--transition);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
      transform: rotate(45deg);
      transition: var(--transition);
    }

    .stat-card:hover {
      transform: translateY(-5px) scale(1.05);
      box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
    }

    .stat-card:hover::before {
      animation: float 2s ease-in-out;
    }

    .stat-number {
      font-size: 2.5em;
      font-weight: 800;
      display: block;
      position: relative;
      z-index: 2;
    }

    .stat-label {
      font-size: 0.95em;
      opacity: 0.95;
      position: relative;
      z-index: 2;
      font-weight: 500;
    }

    .footer {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: var(--border-radius);
      padding: 40px;
      text-align: center;
      margin-top: 50px;
      box-shadow: var(--card-shadow);
      border: 1px solid rgba(255,255,255,0.3);
      animation: fadeInUp 0.8s ease-out;
    }

    .footer h3 {
      color: var(--text-primary);
      margin-bottom: 20px;
      font-size: 1.8em;
      font-weight: 700;
    }

    .made-by-footer {
      background: var(--dark-gradient);
      color: white;
      padding: 25px;
      text-align: center;
      margin-top: 30px;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
      animation: fadeInUp 0.8s ease-out;
    }

    .made-by-footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
      transition: var(--transition);
    }

    .made-by-footer:hover::before {
      left: 100%;
    }

    .made-by-text {
      font-size: 1.1em;
      font-weight: 600;
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .made-by-text::before {
      content: '💻';
      font-size: 1.2em;
    }

    .made-by-text::after {
      content: '✨';
      font-size: 1.2em;
    }

    /* Staggered animation for cards */
    .card:nth-child(1) { animation-delay: 0.1s; }
    .card:nth-child(2) { animation-delay: 0.2s; }
    .card:nth-child(3) { animation-delay: 0.3s; }
    .card:nth-child(4) { animation-delay: 0.4s; }
    .card:nth-child(5) { animation-delay: 0.5s; }
    .card:nth-child(6) { animation-delay: 0.6s; }

    @media (max-width: 768px) {
      .container {
        padding: 15px;
      }

      .header {
        padding: 40px 25px;
      }

      .header h1 {
        font-size: 2.2em;
      }

      .header .subtitle {
        font-size: 1.1em;
      }

      .grid {
        grid-template-columns: 1fr;
        gap: 25px;
      }

      .card {
        padding: 25px;
      }

      .button {
        display: block;
        margin: 10px 0;
        padding: 12px 24px;
      }

      .hierarchy-table {
        font-size: 0.8em;
      }

      .hierarchy-table th,
      .hierarchy-table td {
        padding: 8px;
      }

      .stat-number {
        font-size: 2em;
      }

      .made-by-text {
        font-size: 1em;
      }
    }

    @media (max-width: 480px) {
      .header h1 {
        font-size: 1.8em;
      }

      .header .subtitle {
        font-size: 1em;
      }

      .card {
        padding: 20px;
      }

      .card h2 {
        font-size: 1.3em;
      }

      .card h3 {
        font-size: 1.1em;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <a href="/logout" class="logout-btn">🚪 Logout</a>
      <h1>🚀 Advanced RBAC API</h1>
      <div class="subtitle">Enterprise-Grade Role-Based Access Control with Flexible Identifiers & Comprehensive Audit Logging</div>
      <div class="version">v2.0.0 | Production Ready</div>
    </div>

    <!-- System Overview -->
    <div class="card">
      <h2>🎯 System Overview</h2>
      <p>A comprehensive Role-Based Access Control (RBAC) API system featuring:</p>
      <ul class="feature-list">
        <li><strong>Flexible Identifier Support</strong> - Use human-readable identifiers (emails, subdomains, role names) alongside MongoDB ObjectIds</li>
        <li><strong>Separate Audit Database</strong> - Dedicated MongoDB database for complete API operation tracking with compliance features</li>
        <li><strong>7-Level Hierarchical Privilege System</strong> - From God Super User (Level 0) to Guest (Level 6) with inheritance</li>
        <li><strong>Organization-Scoped Management</strong> - Multi-tenant architecture with organization isolation</li>
        <li><strong>Dynamic Role Creation</strong> - Custom roles with granular permissions and constraints</li>
        <li><strong>Enterprise Security</strong> - Data sanitization, PII protection, and comprehensive audit trails</li>
      </ul>

      <div class="stats-grid">
        <div class="stat-card">
          <span class="stat-number">7</span>
          <span class="stat-label">Privilege Levels</span>
        </div>
        <div class="stat-card">
          <span class="stat-number">20+</span>
          <span class="stat-label">API Endpoints</span>
        </div>
        <div class="stat-card">
          <span class="stat-number">100%</span>
          <span class="stat-label">Audit Coverage</span>
        </div>
        <div class="stat-card">
          <span class="stat-number">3</span>
          <span class="stat-label">Identifier Types</span>
        </div>
      </div>
    </div>

    <div class="grid">
      <!-- Documentation Links -->
      <div class="card">
        <h2>📚 API Documentation</h2>
        <p>Explore the comprehensive API documentation with interactive examples:</p>
        <a href="/api-docs-ui?access=granted" class="button primary">🔍 Interactive Swagger UI</a>
        <a href="/api-docs?access=granted" class="button secondary">📖 Custom Documentation</a>
        <a href="/swagger.json" class="button tertiary">📄 OpenAPI Specification</a>
        <a href="/health" class="button quaternary">💚 Health Check</a>
      </div>

      <!-- Guides -->
      <div class="card">
        <h2>📖 Comprehensive Guides</h2>
        <p>Learn how to effectively use the API system:</p>
        <a href="/auth-guide" class="button primary">🔐 Authentication Guide</a>
        <a href="/docs" class="button secondary">📋 Documentation Guide</a>

        <h3>Quick Start</h3>
        <div class="highlight">
          <strong>God Super User Credentials:</strong><br>
          Email: <code><EMAIL></code><br>
          Password: <code>@Ak720998</code>
        </div>
      </div>

      <!-- Flexible Identifiers -->
      <div class="card">
        <h2>🔄 Flexible Identifier Support</h2>
        <p>Use human-readable identifiers across all endpoints:</p>
        <ul class="feature-list">
          <li><strong>Users:</strong> MongoDB ObjectId OR email address</li>
          <li><strong>Organizations:</strong> MongoDB ObjectId OR subdomain</li>
          <li><strong>Roles:</strong> MongoDB ObjectId OR role name (with org context)</li>
        </ul>

        <h3>Example API Calls</h3>
        <div class="code-example">
# Using human-readable identifiers
GET /api/v1/organizations/acme-corp
GET /api/v1/roles/editor?orgId=acme-corp
PATCH /api/v1/roles/users/<EMAIL>/org/acme-corp/assign-role

# Using ObjectIds (traditional)
GET /api/v1/organizations/663041cf7a14c7c000a3f999
GET /api/v1/roles/663041cf7a14c7c000a3f999
        </div>
      </div>

      <!-- RBAC System -->
      <div class="card">
        <h2>👑 Hierarchical RBAC System</h2>
        <p>7-level privilege system with inheritance and permission-based access control:</p>

        <table class="hierarchy-table">
          <thead>
            <tr>
              <th>Level</th>
              <th>Role Name</th>
              <th>Scope</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><span class="level-badge level-0">0</span></td>
              <td>God Super User</td>
              <td>System</td>
              <td>Unrestricted system-wide access</td>
            </tr>
            <tr>
              <td><span class="level-badge level-1">1</span></td>
              <td>Organization Super User</td>
              <td>Organization</td>
              <td>Complete organization control</td>
            </tr>
            <tr>
              <td><span class="level-badge level-2">2</span></td>
              <td>View Manager</td>
              <td>Organization</td>
              <td>Read-only access to all data</td>
            </tr>
            <tr>
              <td><span class="level-badge level-3">3</span></td>
              <td>Admin</td>
              <td>Organization</td>
              <td>Administrative privileges</td>
            </tr>
            <tr>
              <td><span class="level-badge level-4">4</span></td>
              <td>Manager</td>
              <td>Organization</td>
              <td>Management-level access</td>
            </tr>
            <tr>
              <td><span class="level-badge level-5">5</span></td>
              <td>Member</td>
              <td>Organization</td>
              <td>Standard user access</td>
            </tr>
            <tr>
              <td><span class="level-badge level-6">6</span></td>
              <td>Guest</td>
              <td>Organization</td>
              <td>Read-only limited access</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Audit Logging -->
      <div class="card">
        <h2>📊 Comprehensive Audit Logging</h2>
        <p>Separate MongoDB database for complete API operation tracking:</p>
        <ul class="feature-list">
          <li>All CRUD operations logged with 20+ metadata fields</li>
          <li>Automatic data sanitization and PII protection</li>
          <li>Batch processing for optimal performance</li>
          <li>Configurable retention policies (5-10 years)</li>
          <li>Advanced filtering and analytics capabilities</li>
          <li>Compliance flags (PII_ACCESS, ADMIN_ACTION, etc.)</li>
        </ul>

        <h3>Audit API Endpoints</h3>
        <a href="/api/v1/audit-logs" class="button primary">📋 Query Audit Logs</a>
        <a href="/api/v1/audit-logs/stats/summary" class="button secondary">📈 Audit Statistics</a>
      </div>

      <!-- Organization Management -->
      <div class="card">
        <h2>🏢 Organization Management</h2>
        <p>Full CRUD operations with flexible identifier support:</p>
        <ul class="feature-list">
          <li>Create, read, update, delete organizations</li>
          <li>Add/remove users with role assignments</li>
          <li>Support for both ObjectId and subdomain identifiers</li>
          <li>Admin protection (prevents removing last admin)</li>
          <li>Organization member management</li>
        </ul>

        <h3>Key Endpoints</h3>
        <div class="code-example">
GET    /api/v1/organizations
GET    /api/v1/organizations/{idOrSubdomain}
PATCH  /api/v1/organizations/{idOrSubdomain}/add-user
PATCH  /api/v1/organizations/{idOrSubdomain}/remove-user
        </div>
      </div>

      <!-- Dynamic Roles Management -->
      <div class="card">
        <h2>🎭 Dynamic Roles Management</h2>
        <p>Custom role creation and management with organization scoping:</p>
        <ul class="feature-list">
          <li>Create custom roles with granular permissions</li>
          <li>Role CRUD operations using ObjectId or role name</li>
          <li>Role assignment/unassignment to users</li>
          <li>Organization-scoped and system roles</li>
          <li>Role hierarchy and permission inheritance</li>
          <li>Soft delete with activation/deactivation</li>
        </ul>

        <h3>Key Endpoints</h3>
        <div class="code-example">
GET    /api/v1/roles/org/{orgIdOrSubdomain}
GET    /api/v1/roles/{roleIdOrName}?orgId={context}
PUT    /api/v1/roles/{roleIdOrName}?orgId={context}
DELETE /api/v1/roles/{roleIdOrName}?orgId={context}
PATCH  /api/v1/roles/users/{userIdOrEmail}/org/{orgIdOrSubdomain}/assign-role
        </div>
      </div>

      <!-- Privilege Management -->
      <div class="card">
        <h2>👑 Privilege Management</h2>
        <p>God Super User bootstrap mechanism and hierarchical privilege assignment:</p>
        <ul class="feature-list">
          <li>God Super User bootstrap (first user becomes God Super User)</li>
          <li>Organization Super User creation and management</li>
          <li>Privilege inheritance and permission checks</li>
          <li>Role initialization for organizations</li>
          <li>Atomic privilege transition</li>
          <li>Comprehensive audit logging for privilege changes</li>
        </ul>

        <h3>Key Endpoints</h3>
        <div class="code-example">
GET  /api/v1/privileges/user/{userIdOrEmail}/roles
POST /api/v1/privileges/god-super-user
POST /api/v1/privileges/roles/initialize/{orgIdOrSubdomain}
        </div>
      </div>

      <!-- Authentication & Authorization -->
      <div class="card">
        <h2>🔐 Authentication & Authorization</h2>
        <p>Multiple authentication methods with comprehensive security:</p>
        <ul class="feature-list">
          <li>Email/Password authentication</li>
          <li>Phone OTP authentication</li>
          <li>OAuth integration (Google, LinkedIn)</li>
          <li>JWT token management with refresh tokens</li>
          <li>Password reset functionality</li>
          <li>Session management and security</li>
        </ul>

        <h3>Key Endpoints</h3>
        <div class="code-example">
POST /api/v1/auth/login
POST /api/v1/auth/request-reset
POST /api/v1/auth/reset-password
GET  /api/v1/oauth/google
GET  /api/v1/oauth/linkedin
        </div>
      </div>

      <!-- User Management -->
      <div class="card">
        <h2>👥 User Management</h2>
        <p>Comprehensive user lifecycle management:</p>
        <ul class="feature-list">
          <li>User registration and profile management</li>
          <li>Email and phone verification</li>
          <li>User status management (active, inactive, suspended)</li>
          <li>Profile updates and data management</li>
          <li>User search and filtering</li>
          <li>Integration with Firebase authentication</li>
        </ul>

        <h3>Key Endpoints</h3>
        <div class="code-example">
GET    /api/v1/users
GET    /api/v1/users/{userId}
PUT    /api/v1/users/{userId}
DELETE /api/v1/users/{userId}
        </div>
      </div>
    </div>

    <!-- Getting Started Section -->
    <div class="card">
      <h2>🚀 Getting Started</h2>
      <p>Quick start guide to begin using the API system:</p>

      <h3>1. Authentication</h3>
      <div class="code-example">
# Login with God Super User credentials
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>",
    "password": "@Ak720998"
  }'
      </div>

      <h3>2. Using Flexible Identifiers</h3>
      <div class="code-example">
# Get organization by subdomain (human-readable)
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/v1/organizations/acme-corp

# Get role by name with organization context
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:3000/api/v1/roles/editor?orgId=acme-corp"

# Assign role using email and subdomain
curl -X PATCH \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:3000/api/v1/roles/users/<EMAIL>/org/acme-corp/assign-role \
  -d '{"roleName": "editor"}'
      </div>

      <h3>3. Query Audit Logs</h3>
      <div class="code-example">
# Get recent audit logs
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/v1/audit-logs

# Get audit statistics
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/v1/audit-logs/stats/summary
      </div>

      <div class="highlight">
        <strong>💡 Pro Tip:</strong> All endpoints support both MongoDB ObjectIds and human-readable identifiers.
        Use whichever format is more convenient for your application!
      </div>
    </div>

    <!-- API Endpoint Categories -->
    <div class="card">
      <h2>📋 Complete API Endpoint Categories</h2>
      <div class="grid">
        <div>
          <h3>🔐 Authentication & Authorization</h3>
          <ul class="feature-list">
            <li>Login/Logout</li>
            <li>Password Reset</li>
            <li>OAuth Integration</li>
            <li>Token Management</li>
          </ul>
        </div>
        <div>
          <h3>👥 User Management</h3>
          <ul class="feature-list">
            <li>User CRUD Operations</li>
            <li>Profile Management</li>
            <li>Status Management</li>
            <li>Search & Filtering</li>
          </ul>
        </div>
        <div>
          <h3>🏢 Organization Management</h3>
          <ul class="feature-list">
            <li>Organization CRUD (Flexible IDs)</li>
            <li>Member Management</li>
            <li>Role Assignments</li>
            <li>Admin Protection</li>
          </ul>
        </div>
        <div>
          <h3>🎭 Dynamic Roles</h3>
          <ul class="feature-list">
            <li>Custom Role Creation</li>
            <li>Role CRUD (Flexible IDs)</li>
            <li>Permission Management</li>
            <li>Role Assignments</li>
          </ul>
        </div>
        <div>
          <h3>👑 Privilege Management</h3>
          <ul class="feature-list">
            <li>God Super User Bootstrap</li>
            <li>Hierarchical Privileges</li>
            <li>Role Initialization</li>
            <li>Permission Inheritance</li>
          </ul>
        </div>
        <div>
          <h3>📊 Audit Logs</h3>
          <ul class="feature-list">
            <li>Query with Advanced Filtering</li>
            <li>Statistics & Analytics</li>
            <li>User-Specific Logs</li>
            <li>Compliance Reporting</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <h3>🎯 Advanced RBAC API System</h3>
      <p>Enterprise-grade Role-Based Access Control with Flexible Identifiers & Comprehensive Audit Logging</p>
      <p><strong>Version 2.0.0</strong> | Production Ready | Full Audit Coverage</p>
      <p>Built with Node.js, Express, MongoDB, and comprehensive security features</p>

      <div style="margin-top: 20px;">
        <a href="/api-docs-ui" class="button primary">🚀 Start Exploring</a>
        <a href="/auth-guide" class="button secondary">📖 Read Guide</a>
        <a href="/health" class="button tertiary">💚 Health Check</a>
      </div>

      <p style="margin-top: 20px; color: #7f8c8d; font-size: 0.9em;">
        © 2025 Advanced RBAC API | Comprehensive Authentication & Authorization System
      </p>
    </div>

    <!-- Made by Aniket Footer -->
    <div class="made-by-footer">
      <div class="made-by-text">Made by Aniket</div>
    </div>
  </div>
</body>
</html>