// src/api/v1/routes/subdomains.js
const express = require('express');
const router = express.Router();
const organizationController = require('../controllers/organizationController');

/**
 * @swagger
 * tags:
 *   - name: Subdomains
 *     description: Public subdomain endpoints (No authentication required)
 */

/**
 * @swagger
 * /api/v1/subdomains:
 *   get:
 *     summary: Get all organization subdomains (PUBLIC - No Authentication Required)
 *     tags: [Subdomains]
 *     description: |
 *       **🌐 THE SINGLE PUBLIC SUBDOMAINS ENDPOINT**
 *
 *       Returns ALL organization subdomains without any authentication, authorization, or filtering.
 *       This is the **primary and only** public endpoint for accessing subdomains.
 *
 *       **✨ Why This Endpoint:**
 *       - 🎯 **Single Source of Truth** - One clean endpoint for all subdomain needs
 *       - 🌐 **Completely Public** - No authentication or authorization required
 *       - 📋 **All Subdomains** - Returns every subdomain from every organization
 *       - 🚫 **No Complexity** - No filtering, pagination, or query parameters
 *       - 🔄 **Status Independent** - Includes active, pending, inactive, blocked organizations
 *       - 📝 **Simple Response** - Clean array of subdomain strings only
 *       - 🔤 **Alphabetically Sorted** - Consistent ordering for easy consumption
 *       - ⚡ **Fast & Lightweight** - Optimized for quick responses
 *
 *       **🎯 Perfect For:**
 *       - ✅ Subdomain validation before creating new organizations
 *       - ✅ Public directory of all available subdomains
 *       - ✅ Integration with external systems without authentication
 *       - ✅ Frontend applications needing subdomain lists
 *       - ✅ API consumers who need simple subdomain enumeration
 *       - ✅ Dropdown population and autocomplete features
 *
 *       **🚀 Usage:** Just call the endpoint directly - no parameters or headers required!
 *     responses:
 *       200:
 *         description: All organization subdomains retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Indicates if the request was successful
 *                 data:
 *                   type: object
 *                   properties:
 *                     subdomains:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: Array of all organization subdomains (alphabetically sorted)
 *                       example: ["acme-corp", "beta-company", "demo-site", "test-org"]
 *                     total:
 *                       type: integer
 *                       description: Total number of subdomains in the system
 *                       example: 4
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *                       description: ISO timestamp when the data was retrieved
 *                       example: "2025-06-04T12:30:00.000Z"
 *             examples:
 *               success_response:
 *                 summary: Successful response with subdomains
 *                 value:
 *                   success: true
 *                   data:
 *                     subdomains: ["acme-corp", "beta-company", "demo-site", "test-org"]
 *                     total: 4
 *                     timestamp: "2025-06-04T12:30:00.000Z"
 *               empty_response:
 *                 summary: Successful response with no subdomains
 *                 value:
 *                   success: true
 *                   data:
 *                     subdomains: []
 *                     total: 0
 *                     timestamp: "2025-06-04T12:30:00.000Z"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to retrieve subdomains"
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "PUBLIC_SUBDOMAINS_FETCH_ERROR"
 *                     details:
 *                       type: string
 *                       example: "Internal server error"
 *             examples:
 *               server_error:
 *                 summary: Server error response
 *                 value:
 *                   success: false
 *                   message: "Failed to retrieve subdomains"
 *                   error:
 *                     code: "PUBLIC_SUBDOMAINS_FETCH_ERROR"
 *                     details: "Internal server error"
 */

// PUBLIC ROUTE - No authentication or authorization middleware
router.get('/', organizationController.getPublicSubdomains);

module.exports = router;
