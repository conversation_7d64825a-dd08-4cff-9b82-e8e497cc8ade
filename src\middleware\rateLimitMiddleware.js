// src/middleware/rateLimitMiddleware.js
const rateLimit = require('express-rate-limit');
const logger = require('../services/logger');
const rateLimitConfig = require('../config/rateLimitConfig');

// Optional Redis imports with fallback
let RedisStore = null;
let redis = null;

try {
  RedisStore = require('rate-limit-redis');
  redis = require('redis');
} catch (error) {
  console.warn('Redis packages not found, using in-memory rate limiting:', error.message);
}

/**
 * Advanced Rate Limiting Middleware
 * Supports multiple rate limiting strategies with Redis backing
 */
class RateLimitMiddleware {
  constructor() {
    this.config = rateLimitConfig.getConfig();
    this.redisClient = null;
    this.isRedisEnabled = this.config.redis.enabled && RedisStore && redis;
    this.initializeRedis();
  }

  /**
   * Initialize Redis client for distributed rate limiting
   */
  async initializeRedis() {
    if (!this.isRedisEnabled) {
      logger.info('Rate limiting initialized with in-memory store', {
        component: 'rate-limit-middleware',
        operation: 'initialization'
      });
      return;
    }

    try {
      const redisConfig = this.config.redis.url ?
        { url: this.config.redis.url } :
        {
          host: this.config.redis.host,
          port: this.config.redis.port,
          password: this.config.redis.password,
          db: this.config.redis.db
        };

      this.redisClient = redis.createClient(redisConfig);

      this.redisClient.on('error', (err) => {
        logger.error('Redis connection error', err, {
          component: 'rate-limit-middleware',
          operation: 'redis_connection'
        });
      });

      this.redisClient.on('connect', () => {
        logger.info('Redis connected for rate limiting', {
          component: 'rate-limit-middleware',
          operation: 'redis_connection'
        });
      });

      await this.redisClient.connect();
    } catch (error) {
      logger.error('Failed to initialize Redis for rate limiting', error, {
        component: 'rate-limit-middleware',
        operation: 'redis_initialization'
      });
      this.redisClient = null;
    }
  }

  /**
   * Get Redis store for rate limiting
   */
  getStore() {
    if (this.redisClient && RedisStore) {
      return new RedisStore({
        sendCommand: (...args) => this.redisClient.sendCommand(args),
        prefix: 'rl:',
      });
    }
    return undefined; // Use default memory store
  }

  /**
   * Generate rate limit key based on user and IP
   */
  generateKey(req) {
    // Priority: User ID > Firebase UID > IP Address
    if (req.user?._id) {
      return `user:${req.user._id}`;
    }
    if (req.user?.uid) {
      return `firebase:${req.user.uid}`;
    }
    return `ip:${req.ip || req.connection.remoteAddress}`;
  }

  /**
   * Custom rate limit handler with detailed logging
   */
  rateLimitHandler(req, res) {
    const key = this.generateKey(req);

    logger.warn('Rate limit exceeded', {
      component: 'rate-limit-middleware',
      operation: 'rate_limit_exceeded',
      metadata: {
        key: key,
        ip: req.ip,
        user_id: req.user?._id,
        firebase_uid: req.user?.uid,
        path: req.path,
        method: req.method,
        user_agent: req.get('user-agent'),
        organization: req.organization?.name
      }
    });

    res.status(429).json({
      success: false,
      message: 'Too many requests',
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        details: 'You have exceeded the rate limit. Please try again later.',
        retry_after: res.get('Retry-After')
      },
      metadata: {
        limit_type: 'api_requests',
        reset_time: new Date(Date.now() + (res.get('Retry-After') * 1000)).toISOString()
      }
    });
  }

  /**
   * Skip rate limiting for certain conditions
   */
  skipRateLimit(req) {
    // Skip for health checks
    if (req.path === '/health' || req.path === '/ping') {
      return true;
    }

    // Skip for god super users (optional)
    if (process.env.SKIP_RATE_LIMIT_FOR_GOD_USERS === 'true') {
      if (req.user?.systemPrivileges?.some(p => p.privilege === 'god_super_user')) {
        return true;
      }
    }

    // Skip for whitelisted IPs
    const whitelistedIPs = process.env.RATE_LIMIT_WHITELIST_IPS?.split(',') || [];
    if (whitelistedIPs.includes(req.ip)) {
      return true;
    }

    return false;
  }

  /**
   * General API rate limiting
   */
  generalApiLimit() {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: (req) => {
        // Different limits based on authentication status
        if (req.user) {
          return 1000; // 1000 requests per 15 minutes for authenticated users
        }
        return 100; // 100 requests per 15 minutes for unauthenticated users
      },
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit,
      onLimitReached: (req, _res, options) => {
        logger.warn('Rate limit reached', {
          component: 'rate-limit-middleware',
          operation: 'rate_limit_reached',
          metadata: {
            key: this.generateKey(req),
            limit: options.max,
            window: options.windowMs,
            path: req.path
          }
        });
      }
    });
  }

  /**
   * Authentication endpoint rate limiting (stricter)
   */
  authApiLimit() {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 10, // 10 login attempts per 15 minutes
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit
    });
  }

  /**
   * Registration endpoint rate limiting
   */
  registrationLimit() {
    return rateLimit({
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 5, // 5 registration attempts per hour
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit
    });
  }

  /**
   * Password reset rate limiting
   */
  passwordResetLimit() {
    return rateLimit({
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 3, // 3 password reset attempts per hour
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit
    });
  }

  /**
   * Admin operations rate limiting
   */
  adminApiLimit() {
    return rateLimit({
      windowMs: 5 * 60 * 1000, // 5 minutes
      max: 100, // 100 admin operations per 5 minutes
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit
    });
  }

  /**
   * File upload rate limiting
   */
  uploadLimit() {
    return rateLimit({
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 50, // 50 uploads per hour
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit
    });
  }

  /**
   * Search/query rate limiting
   */
  searchLimit() {
    return rateLimit({
      windowMs: 1 * 60 * 1000, // 1 minute
      max: 30, // 30 searches per minute
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit
    });
  }

  /**
   * Organization creation rate limiting
   */
  organizationCreationLimit() {
    return rateLimit({
      windowMs: 24 * 60 * 60 * 1000, // 24 hours
      max: 3, // 3 organization creations per day
      message: this.rateLimitHandler,
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey,
      skip: this.skipRateLimit
    });
  }

  /**
   * Dynamic rate limiting based on endpoint
   */
  dynamicLimit() {
    return (req, res, next) => {
      const limitConfig = rateLimitConfig.getLimitForEndpoint(req.path);
      const messageTemplates = rateLimitConfig.getMessageTemplates();

      // Determine which message template to use
      let messageType = 'general';
      if (req.path.includes('/auth/')) messageType = 'auth';
      else if (req.path.includes('/admin/')) messageType = 'admin';
      else if (req.path.includes('/upload/')) messageType = 'upload';
      else if (req.path.includes('/search/')) messageType = 'search';
      else if (req.path.includes('/organizations/create')) messageType = 'organizationCreation';
      else if (req.path.includes('/api-docs') || req.path.includes('/swagger')) messageType = 'docs';

      const messageTemplate = messageTemplates[messageType];

      const rateLimitMiddleware = rateLimit({
        windowMs: limitConfig.windowMs,
        max: (req) => {
          if (limitConfig.maxAuthenticated && limitConfig.maxUnauthenticated) {
            return req.user ? limitConfig.maxAuthenticated : limitConfig.maxUnauthenticated;
          }
          return limitConfig.max;
        },
        message: (req, res) => {
          logger.warn('Rate limit exceeded', {
            component: 'rate-limit-middleware',
            operation: 'rate_limit_exceeded',
            metadata: {
              key: this.generateKey(req),
              ip: req.ip,
              user_id: req.user?._id,
              path: req.path,
              method: req.method,
              limit_type: messageType
            }
          });

          res.status(429).json({
            success: false,
            message: messageTemplate.message,
            error: {
              code: messageTemplate.code,
              details: messageTemplate.details,
              retry_after: res.get('Retry-After')
            },
            metadata: {
              limit_type: messageType,
              reset_time: new Date(Date.now() + (res.get('Retry-After') * 1000)).toISOString()
            }
          });
        },
        standardHeaders: true,
        legacyHeaders: false,
        store: this.getStore(),
        keyGenerator: this.generateKey.bind(this),
        skip: this.skipRateLimit.bind(this)
      });

      rateLimitMiddleware(req, res, next);
    };
  }

  /**
   * Get middleware for specific rate limit type
   */
  getMiddleware(type = 'general') {
    const limits = this.config.limits;
    const limitConfig = limits[type] || limits.general;
    const messageTemplates = rateLimitConfig.getMessageTemplates();
    const messageTemplate = messageTemplates[type] || messageTemplates.general;

    return rateLimit({
      windowMs: limitConfig.windowMs,
      max: (req) => {
        if (limitConfig.maxAuthenticated && limitConfig.maxUnauthenticated) {
          return req.user ? limitConfig.maxAuthenticated : limitConfig.maxUnauthenticated;
        }
        return limitConfig.max;
      },
      message: (req, res) => {
        logger.warn('Rate limit exceeded', {
          component: 'rate-limit-middleware',
          operation: 'rate_limit_exceeded',
          metadata: {
            key: this.generateKey(req),
            ip: req.ip,
            user_id: req.user?._id,
            path: req.path,
            method: req.method,
            limit_type: type
          }
        });

        res.status(429).json({
          success: false,
          message: messageTemplate.message,
          error: {
            code: messageTemplate.code,
            details: messageTemplate.details,
            retry_after: res.get('Retry-After')
          },
          metadata: {
            limit_type: type,
            reset_time: new Date(Date.now() + (res.get('Retry-After') * 1000)).toISOString()
          }
        });
      },
      standardHeaders: true,
      legacyHeaders: false,
      store: this.getStore(),
      keyGenerator: this.generateKey.bind(this),
      skip: this.skipRateLimit.bind(this)
    });
  }

  /**
   * Cleanup Redis connections
   */
  async cleanup() {
    if (this.redisClient) {
      await this.redisClient.quit();
    }
  }
}

// Export singleton instance
module.exports = new RateLimitMiddleware();
