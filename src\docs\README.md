# Auth Arsenal API Documentation

Welcome to the Auth Arsenal API documentation. This guide will help you understand how to use the API documentation effectively.

## Documentation Endpoints

The API provides several documentation endpoints:

1. **Custom Documentation UI**
   - URL: `/api-docs`
   - Description: Enhanced, custom-styled documentation interface
   - Recommended for most users

2. **Standard Swagger UI**
   - URL: `/api-docs-ui`
   - Description: Standard Swagger UI interface
   - Useful for developers familiar with Swagger

3. **Raw OpenAPI Specification**
   - URL: `/swagger.json`
   - Description: Raw JSON OpenAPI specification
   - Useful for importing into API tools

4. **Authentication Guide**
   - URL: `/auth-guide`
   - Description: Comprehensive guide to authentication methods and flows
   - Recommended for understanding authentication

## Using the Documentation

### Interactive API Testing

Both documentation UIs allow you to test API endpoints directly from the browser:

1. Click on an endpoint to expand it
2. Click the "Try it out" button
3. Fill in the required parameters
4. Click "Execute" to make a real API call
5. View the response

### Authentication in Documentation

To test authenticated endpoints:

1. Click the "Authorize" button at the top of the page
2. Enter your authentication credentials or token
3. All subsequent API calls will include your authentication

## API Structure

The API is organized into the following sections:

- **Users**: User management and profile operations
- **Auth**: Authentication flows including login and registration
- **OAuth**: OAuth authentication with third-party providers
- **Otp-Auth**: One-time password authentication via email and phone
- **Config**: Runtime configuration settings (admin only)
- **Middleware**: Utility endpoints for token refresh
- **Health**: Service health and status checks
- **Documentation**: API documentation endpoints

## Common Response Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created (for POST requests that create resources) |
| 400 | Bad Request (invalid input) |
| 401 | Unauthorized (authentication required) |
| 403 | Forbidden (insufficient permissions) |
| 404 | Not Found (resource doesn't exist) |
| 500 | Internal Server Error |

## Error Handling

All error responses follow a consistent format:

```json
{
  "message": "error_code",
  "error": "Detailed error description"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated users

Exceeding these limits will result in a 429 (Too Many Requests) response.

## Support

If you encounter any issues with the API or documentation:

- Check the [Authentication Guide](/auth-guide) for authentication issues
- Ensure you're using the correct request format and parameters
- Contact <NAME_EMAIL>

## Version Information

- API Version: 1.0.0
- Documentation Last Updated: 2023-10-01
