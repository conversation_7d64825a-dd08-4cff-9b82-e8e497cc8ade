{"test_name": "Organization Creation - <PERSON><PERSON><PERSON>s", "description": "Test various error scenarios for organization creation", "tests": [{"case": "Missing required fields", "endpoint": "POST /api/v1/organizations", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "request_body": {"name": "Test Org"}, "expected_response": {"status": 400, "body": {"message": "Name and subdomain are required"}}, "curl_command": "curl -X POST 'https://your-domain.vercel.app/api/v1/organizations' -H 'Content-Type: application/json' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE' -d '{\"name\":\"Test Org\"}'"}, {"case": "Invalid subdomain format", "endpoint": "POST /api/v1/organizations", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "request_body": {"name": "Test Organization", "subdomain": "Test_Org_123!"}, "expected_response": {"status": 400, "body": {"message": "Subdomain can only contain lowercase letters, numbers, and hyphens"}}, "curl_command": "curl -X POST 'https://your-domain.vercel.app/api/v1/organizations' -H 'Content-Type: application/json' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE' -d '{\"name\":\"Test Organization\",\"subdomain\":\"Test_Org_123!\"}'"}, {"case": "Duplicate subdomain", "endpoint": "POST /api/v1/organizations", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "request_body": {"name": "Another Test Org", "subdomain": "test-org-001"}, "expected_response": {"status": 409, "body": {"message": "Subdomain already taken"}}, "curl_command": "curl -X POST 'https://your-domain.vercel.app/api/v1/organizations' -H 'Content-Type: application/json' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE' -d '{\"name\":\"Another Test Org\",\"subdomain\":\"test-org-001\"}'"}]}