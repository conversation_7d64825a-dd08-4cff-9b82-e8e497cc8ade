//src/api/v1/controllers/configController.js
const Config = require('../../../models/Config');
const logger = require('../../../services/logger');

/**
 * Configuration Controller
 * Handles system configuration management
 */
class ConfigController {

  /**
   * Get current SMTP settings
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getEmailConfig(req, res) {
    try {
      const cfg = await Config.findOne();
      
      if (!cfg) {
        return res.json({ 
          smtp: { 
            user: '', 
            fromAddress: '', 
            cc_users: [] 
          } 
        });
      }

      // Only return safe fields (no password)
      const { user = '', fromAddress = '', cc_users = [] } = cfg.smtp || {};
      
      res.json({ 
        smtp: { 
          user, 
          fromAddress, 
          cc_users 
        } 
      });
    } catch (err) {
      logger.error('Failed to fetch email config', err, {
        component: 'config-controller',
        operation: 'get_email_config',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ 
        message: 'Failed to fetch email configuration', 
        error: err.message 
      });
    }
  }

  /**
   * Update SMTP settings
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateEmailConfig(req, res) {
    const { smtp } = req.body;

    if (!smtp || !smtp.user || !smtp.pass || !smtp.fromAddress) {
      return res.status(400).json({ 
        message: 'smtp.user, pass, fromAddress required' 
      });
    }

    try {
      let cfg = await Config.findOne();
      if (!cfg) {
        cfg = new Config();
      }

      cfg.smtp = {
        ...smtp,
        cc_users: smtp.cc_users || [],
        updatedAt: new Date(),
      };

      await cfg.save();

      logger.info('SMTP configuration updated', {
        component: 'config-controller',
        operation: 'update_email_config',
        metadata: {
          user_id: req.user._id,
          smtp_user: smtp.user,
          from_address: smtp.fromAddress,
          cc_users_count: (smtp.cc_users || []).length
        }
      });

      res.json({ 
        message: 'smtp_config_updated', 
        smtp: {
          user: cfg.smtp.user,
          fromAddress: cfg.smtp.fromAddress,
          cc_users: cfg.smtp.cc_users,
          updatedAt: cfg.smtp.updatedAt
        }
      });
    } catch (err) {
      logger.error('Failed to update email config', err, {
        component: 'config-controller',
        operation: 'update_email_config',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ 
        message: 'Failed to update email configuration', 
        error: err.message 
      });
    }
  }

  /**
   * Get general system configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getSystemConfig(req, res) {
    try {
      const cfg = await Config.findOne();
      
      if (!cfg) {
        return res.json({
          system: {
            maintenance_mode: false,
            registration_enabled: true,
            max_organizations_per_user: 10
          }
        });
      }

      // Return safe system configuration
      const systemConfig = {
        maintenance_mode: cfg.system?.maintenance_mode || false,
        registration_enabled: cfg.system?.registration_enabled !== false,
        max_organizations_per_user: cfg.system?.max_organizations_per_user || 10,
        api_rate_limit: cfg.system?.api_rate_limit || 1000,
        session_timeout: cfg.system?.session_timeout || 3600
      };

      res.json({ system: systemConfig });
    } catch (err) {
      logger.error('Failed to fetch system config', err, {
        component: 'config-controller',
        operation: 'get_system_config',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ 
        message: 'Failed to fetch system configuration', 
        error: err.message 
      });
    }
  }

  /**
   * Update system configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateSystemConfig(req, res) {
    const { system } = req.body;

    if (!system || typeof system !== 'object') {
      return res.status(400).json({ 
        message: 'system configuration object is required' 
      });
    }

    try {
      let cfg = await Config.findOne();
      if (!cfg) {
        cfg = new Config();
      }

      // Update system configuration
      cfg.system = {
        ...cfg.system,
        ...system,
        updatedAt: new Date(),
        updatedBy: req.user._id
      };

      await cfg.save();

      logger.info('System configuration updated', {
        component: 'config-controller',
        operation: 'update_system_config',
        metadata: {
          user_id: req.user._id,
          updated_fields: Object.keys(system)
        }
      });

      res.json({ 
        message: 'system_config_updated', 
        system: cfg.system
      });
    } catch (err) {
      logger.error('Failed to update system config', err, {
        component: 'config-controller',
        operation: 'update_system_config',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ 
        message: 'Failed to update system configuration', 
        error: err.message 
      });
    }
  }

  /**
   * Get security configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getSecurityConfig(req, res) {
    try {
      const cfg = await Config.findOne();
      
      if (!cfg) {
        return res.json({
          security: {
            password_policy: {
              min_length: 8,
              require_uppercase: true,
              require_lowercase: true,
              require_numbers: true,
              require_symbols: false
            },
            session_security: {
              secure_cookies: true,
              same_site: 'strict',
              max_concurrent_sessions: 5
            }
          }
        });
      }

      // Return security configuration (safe fields only)
      const securityConfig = {
        password_policy: cfg.security?.password_policy || {
          min_length: 8,
          require_uppercase: true,
          require_lowercase: true,
          require_numbers: true,
          require_symbols: false
        },
        session_security: cfg.security?.session_security || {
          secure_cookies: true,
          same_site: 'strict',
          max_concurrent_sessions: 5
        },
        two_factor_auth: {
          enabled: cfg.security?.two_factor_auth?.enabled || false,
          required_for_admins: cfg.security?.two_factor_auth?.required_for_admins || false
        }
      };

      res.json({ security: securityConfig });
    } catch (err) {
      logger.error('Failed to fetch security config', err, {
        component: 'config-controller',
        operation: 'get_security_config',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ 
        message: 'Failed to fetch security configuration', 
        error: err.message 
      });
    }
  }

  /**
   * Update security configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateSecurityConfig(req, res) {
    const { security } = req.body;

    if (!security || typeof security !== 'object') {
      return res.status(400).json({ 
        message: 'security configuration object is required' 
      });
    }

    try {
      let cfg = await Config.findOne();
      if (!cfg) {
        cfg = new Config();
      }

      // Update security configuration
      cfg.security = {
        ...cfg.security,
        ...security,
        updatedAt: new Date(),
        updatedBy: req.user._id
      };

      await cfg.save();

      logger.info('Security configuration updated', {
        component: 'config-controller',
        operation: 'update_security_config',
        metadata: {
          user_id: req.user._id,
          updated_fields: Object.keys(security)
        }
      });

      res.json({ 
        message: 'security_config_updated', 
        security: cfg.security
      });
    } catch (err) {
      logger.error('Failed to update security config', err, {
        component: 'config-controller',
        operation: 'update_security_config',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ 
        message: 'Failed to update security configuration', 
        error: err.message 
      });
    }
  }
}

module.exports = new ConfigController();
