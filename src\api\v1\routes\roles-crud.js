const express = require('express');
const router = express.Router();
const { authenticate } = require('../../../middleware/auth');
const { rbac } = require('../../../middleware/rbac');
const roleController = require('../controllers/roleController');
const logger = require('../../../services/logger');
const rbacManager = require('../../../services/rbac-manager');
const { clearCaches } = require('../../../services/rbac-engine');
const RBACRole = require('../../../models/RBACRole');
const Organization = require('../../../models/Organization');
const mongoose = require('mongoose');

/**
 * Helper function to check if a string is a valid MongoDB ObjectId
 * @param {string} id - The string to check
 * @returns {boolean} True if valid ObjectId, false otherwise
 */
function isObjectId(id) {
  return mongoose.Types.ObjectId.isValid(id);
}

/**
 * Helper function to resolve organization by ID or subdomain
 * @param {string} identifier - Organization ID or subdomain
 * @returns {Object|null} Organization object or null
 */
async function resolveOrganization(identifier) {
  if (isObjectId(identifier)) {
    return await Organization.findById(identifier);
  } else {
    return await Organization.findOne({ subdomain: identifier.toLowerCase() });
  }
}

/**
 * Helper function to resolve role by ID or name within organization scope
 * @param {string} identifier - Role ID or name
 * @param {string} organizationId - Organization ID for scoped lookup
 * @returns {Object|null} Role object or null
 */
async function resolveRole(identifier, organizationId = null) {
  if (isObjectId(identifier)) {
    return await RBACRole.findById(identifier);
  } else {
    const query = { name: identifier };
    if (organizationId) {
      query.$or = [
        { scope: 'system' },
        { scope: 'global' },
        { scope: 'organization', organization: organizationId }
      ];
    }
    return await RBACRole.findOne(query);
  }
}

/**
 * @swagger
 * tags:
 *   - name: DynamicRoles
 *     description: CRUD for custom roles per organization using centralized RBAC database
 */



/**
 * Helper function to validate and transform permission structure for centralized RBAC
 * @param {Array} permissions - Array of permission objects
 * @returns {Object} Validation result with transformed permissions
 */
function validateAndTransformPermissions(permissions) {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return {
      valid: false,
      error: 'Permissions must be a non-empty array'
    };
  }

  const transformedPermissions = [];
  const validResources = [
    'user_object', 'org_object', 'role_object', 'event', 'ticket',
    'auth_prev', 'audit_log', 'settings', 'billing', 'analytics',
    'integration', 'notification', 'file_storage', 'workflow', 'api_access',
    // Legacy compatibility
    'user', 'org', 'role', 'config'
  ];

  const validActions = [
    'create', 'read', 'update', 'delete', 'assign', 'approve',
    'export', 'import', 'configure', 'monitor', 'audit',
    'backup', 'restore', 'publish', 'moderate', '*'
  ];

  for (const [index, permission] of permissions.entries()) {
    if (!permission.resource || !permission.actions) {
      return {
        valid: false,
        error: `Permission at index ${index} must have 'resource' and 'actions' properties`
      };
    }

    if (!validResources.includes(permission.resource)) {
      return {
        valid: false,
        error: `Invalid resource '${permission.resource}' at index ${index}. Valid resources: ${validResources.join(', ')}`
      };
    }

    if (!Array.isArray(permission.actions) || permission.actions.length === 0) {
      return {
        valid: false,
        error: `Actions must be a non-empty array at permission index ${index}`
      };
    }

    for (const action of permission.actions) {
      if (!validActions.includes(action)) {
        return {
          valid: false,
          error: `Invalid action '${action}' at permission index ${index}. Valid actions: ${validActions.join(', ')}`
        };
      }
    }

    // Transform to centralized RBAC format
    transformedPermissions.push({
      resource: permission.resource,
      actions: permission.actions,
      constraints: permission.constraints || {
        scope: 'organization'
      }
    });
  }

  return {
    valid: true,
    permissions: transformedPermissions
  };
}

/**
 * @swagger
 * /api/v1/roles:
 *   post:
 *     summary: Create a new custom role for an organization using centralized RBAC
 *     tags: [DynamicRoles]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [org, name, permissions]
 *             properties:
 *               org:
 *                 type: string
 *                 description: Organization ObjectId, subdomain, or unique name
 *                 example: "acme"
 *               name:
 *                 type: string
 *                 description: Machine-readable role name (unique within org)
 *                 example: "editor"
 *               displayName:
 *                 type: string
 *                 description: Human-friendly role label (shows in UI)
 *                 example: "Content Editor"
 *               description:
 *                 type: string
 *                 description: Optional role description (for admins)
 *                 example: "Can edit organization events and content"
 *               permissions:
 *                 type: array
 *                 description: List of permission objects (see below)
 *                 items:
 *                   type: object
 *                   required: [resource, actions]
 *                   properties:
 *                     resource:
 *                       type: string
 *                       description: What object/type this permission is for
 *                       example: "event"
 *                       enum: [user_object, org_object, role_object, event, ticket, auth_prev, audit_log, settings, billing, analytics, integration, notification, file_storage, workflow, api_access, user, org, role, config]
 *                     actions:
 *                       type: array
 *                       description: Allowed actions (verbs) on resource
 *                       items:
 *                         type: string
 *                         enum: [create, read, update, delete, assign, approve, export, import, configure, monitor, audit, backup, restore, publish, moderate, "*"]
 *                       example: ["create", "read", "update"]
 *                     constraints:
 *                       type: object
 *                       description: Permission constraints (scoping, limits)
 *                       properties:
 *                         scope:
 *                           type: string
 *                           enum: [self, team, department, organization, system]
 *                           default: organization
 *                           description: Scope/limit for this permission (org-level, self-only, etc.)
 *                         time:
 *                           type: object
 *                           description: Time-based constraint
 *                           properties:
 *                             start:
 *                               type: string
 *                               format: date-time
 *                             end:
 *                               type: string
 *                               format: date-time
 *               hierarchy:
 *                 type: object
 *                 description: Optional role hierarchy metadata (RBAC v2+)
 *                 properties:
 *                   level:
 *                     type: integer
 *                     description: Role's hierarchy level (lower = higher privilege)
 *                     minimum: 1
 *                     maximum: 100
 *                     default: 10
 *                   type:
 *                     type: string
 *                     enum: [system, organization, custom, temporary]
 *                     default: custom
 *                     description: System/global/organization/custom/temporary
 *             example:
 *               org: "acme"
 *               name: "editor"
 *               displayName: "Content Editor"
 *               description: "Can edit organization events and content"
 *               permissions:
 *                 - resource: "event"
 *                   actions: ["create", "read", "update"]
 *                 - resource: "config"
 *                   actions: ["read"]
 *               hierarchy:
 *                 level: 8
 *                 type: "custom"
 *     responses:
 *       201:
 *         description: Role created successfully in centralized RBAC database
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Custom role created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     role_id:
 *                       type: string
 *                       description: Created role ObjectId
 *                     name:
 *                       type: string
 *                     displayName:
 *                       type: string
 *                     description:
 *                       type: string
 *                     scope:
 *                       type: string
 *                       example: "organization"
 *                     organization:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         subdomain:
 *                           type: string
 *                     hierarchy:
 *                       type: object
 *                     permissions:
 *                       type: array
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad input - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "VALIDATION_ERROR"
 *                     message:
 *                       type: string
 *                     details:
 *                       type: string
 *       404:
 *         description: Organization not found
 *       409:
 *         description: Role name already exists in organization
 *       500:
 *         description: Internal server error
 */

router.post(
  '/',
  authenticate,
  rbac('role_object:create'),
  async (req, res) => {
    const correlationId = logger.generateCorrelationId();

    try {
      const { org, name, displayName, description, permissions, hierarchy } = req.body;

      // Input validation
      if (!org || !name || !permissions) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: 'Missing required fields',
            details: 'org, name, and permissions are required'
          }
        });
      }

      logger.info('Creating custom role via centralized RBAC', {
        component: 'roles-crud',
        operation: 'create_custom_role',
        correlation_id: correlationId,
        metadata: {
          org_identifier: org,
          role_name: name,
          created_by: req.user._id,
          permissions_count: permissions?.length || 0
        }
      });

      // Resolve organization
      const organization = await resolveOrganization(org);
      if (!organization) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            message: 'Organization not found',
            details: `No organization found with identifier: ${org}. Provide a valid ObjectId, subdomain, or name.`
          }
        });
      }

      // Validate and transform permissions
      const permissionValidation = validateAndTransformPermissions(permissions);
      if (!permissionValidation.valid) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PERMISSIONS',
            message: 'Invalid permission structure',
            details: permissionValidation.error
          }
        });
      }

      // Prepare role data for centralized RBAC
      const roleData = {
        name: name.toLowerCase().trim(),
        displayName: displayName || name,
        description: description || `Custom role: ${name}`,
        organization: organization._id,
        permissions: permissionValidation.permissions,
        hierarchy: {
          level: hierarchy?.level || 10,
          type: hierarchy?.type || 'custom',
          parent: null,
          children: [],
          inheritPermissions: true
        },
        metadata: {
          category: 'custom',
          tags: []
        }
      };

      // Create role using centralized RBAC manager
      const result = await rbacManager.createCustomRole(roleData, req.user._id);

      if (!result.success) {
        const statusCode = result.error.code === 'ROLE_ALREADY_EXISTS' ? 409 : 500;
        return res.status(statusCode).json({
          success: false,
          error: result.error
        });
      }

      // Clear caches to ensure fresh data
      clearCaches();

      logger.info('Custom role created successfully', {
        component: 'roles-crud',
        operation: 'create_custom_role',
        correlation_id: correlationId,
        metadata: {
          role_id: result.data.role_id,
          role_name: result.data.name,
          organization_id: organization._id,
          organization_name: organization.name,
          created_by: req.user._id
        }
      });

      // Return enhanced response
      res.status(201).json({
        success: true,
        message: 'Custom role created successfully',
        data: {
          ...result.data,
          organization: {
            id: organization._id,
            name: organization.name,
            subdomain: organization.subdomain
          }
        },
        metadata: {
          correlation_id: correlationId,
          timestamp: new Date().toISOString(),
          created_by: req.user._id,
          source: 'centralized_rbac_database'
        }
      });

    } catch (error) {
      logger.error('Failed to create custom role', error, {
        component: 'roles-crud',
        operation: 'create_custom_role',
        correlation_id: correlationId,
        metadata: {
          org_identifier: req.body.org,
          role_name: req.body.name,
          created_by: req.user._id
        }
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'ROLE_CREATION_FAILED',
          message: 'Failed to create custom role',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        },
        metadata: {
          correlation_id: correlationId,
          timestamp: new Date().toISOString()
        }
      });
    }
  }
);

/**
 * @swagger
 * /api/v1/roles:
 *   get:
 *     summary: Get all roles in the system with pagination and filtering
 *     description: |
 *       Retrieve all roles including both static/system roles and dynamic/custom roles.
 *       Supports pagination and filtering by organization context.
 *
 *       **Role Types:**
 *       - **System roles**: Predefined roles like admin, manager, member, guest
 *       - **Organization roles**: Organization-specific custom roles
 *       - **Global roles**: System-wide roles with global scope
 *     tags: [DynamicRoles]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of roles per page
 *       - in: query
 *         name: orgId
 *         schema:
 *           type: string
 *         description: Filter by organization (ObjectId or subdomain)
 *         example: "acme-corp"
 *       - in: query
 *         name: scope
 *         schema:
 *           type: string
 *           enum: [system, organization, global, all]
 *           default: all
 *         description: Filter by role scope
 *       - in: query
 *         name: includeInactive
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include inactive/disabled roles
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, hierarchy.level, createdAt, updatedAt]
 *           default: hierarchy.level
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: integer
 *           enum: [1, -1]
 *           default: 1
 *         description: Sort order (1 for ascending, -1 for descending)
 *     responses:
 *       200:
 *         description: Roles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 roles:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Role'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *                 metadata:
 *                   type: object
 *                   properties:
 *                     filters_applied:
 *                       type: object
 *                     total_by_scope:
 *                       type: object
 *                     timestamp:
 *                       type: string
 *       400:
 *         description: Invalid query parameters
 *       500:
 *         description: Internal server error
 */
router.get('/', authenticate, rbac('role:read'), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      orgId,
      scope = 'all',
      includeInactive = false,
      sortBy = 'hierarchy.level',
      sortOrder = 1
    } = req.query;

    // Validate pagination parameters
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const sortOrderNum = parseInt(sortOrder);

    if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({
        success: false,
        message: 'Invalid pagination parameters',
        error: {
          code: 'INVALID_PAGINATION',
          details: 'Page must be >= 1, limit must be between 1 and 100'
        }
      });
    }

    if (![-1, 1].includes(sortOrderNum)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid sort order',
        error: {
          code: 'INVALID_SORT_ORDER',
          details: 'Sort order must be 1 (ascending) or -1 (descending)'
        }
      });
    }

    // Build query based on filters
    let query = {};
    let organizationContext = null;

    // Handle organization filter
    if (orgId) {
      organizationContext = await resolveOrganization(orgId);
      if (!organizationContext) {
        return res.status(400).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: `No organization found with identifier: ${orgId}`
          }
        });
      }

      query.$or = [
        { scope: 'system' },
        { scope: 'global' },
        { scope: 'organization', organization: organizationContext._id }
      ];
    } else {
      // No organization filter - get all roles based on scope
      if (scope !== 'all') {
        query.scope = scope;
      }
    }

    // Handle active/inactive filter
    if (!includeInactive) {
      query['metadata.isActive'] = true;
    }

    // Calculate pagination
    const skip = (pageNum - 1) * limitNum;

    // Execute query with pagination
    const [roles, total] = await Promise.all([
      RBACRole.find(query)
        .sort({ [sortBy]: sortOrderNum })
        .skip(skip)
        .limit(limitNum)
        .populate('organization', 'name subdomain')
        .lean(),
      RBACRole.countDocuments(query)
    ]);

    // Get total counts by scope for metadata
    const scopeCounts = await RBACRole.aggregate([
      { $match: organizationContext ? {
        $or: [
          { scope: 'system' },
          { scope: 'global' },
          { scope: 'organization', organization: organizationContext._id }
        ]
      } : {} },
      { $group: { _id: '$scope', count: { $sum: 1 } } }
    ]);

    const totalByScope = scopeCounts.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {});

    logger.info('Retrieved all roles with pagination', {
      component: 'roles-crud',
      operation: 'get_all_roles',
      metadata: {
        page: pageNum,
        limit: limitNum,
        total_results: total,
        organization_filter: orgId || null,
        scope_filter: scope,
        include_inactive: includeInactive,
        sort_by: sortBy,
        sort_order: sortOrderNum,
        requested_by: req.user._id
      }
    });

    res.json({
      success: true,
      roles: roles,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: total,
        pages: Math.ceil(total / limitNum)
      },
      metadata: {
        filters_applied: {
          organization: organizationContext ? {
            id: organizationContext._id,
            name: organizationContext.name,
            subdomain: organizationContext.subdomain
          } : null,
          scope: scope,
          include_inactive: includeInactive,
          sort_by: sortBy,
          sort_order: sortOrderNum === 1 ? 'ascending' : 'descending'
        },
        total_by_scope: totalByScope,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Failed to retrieve all roles', {
      component: 'roles-crud',
      operation: 'get_all_roles',
      error: error.message,
      metadata: {
        query_params: req.query,
        requested_by: req.user._id
      }
    });

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve roles',
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        details: 'An error occurred while fetching roles'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/roles/org/{orgIdOrSubdomain}:
 *   get:
 *     summary: List all custom roles in an organization with flexible identifier support
 *     description: |
 *       Retrieve all roles for a specific organization.
 *       Supports both MongoDB ObjectId and subdomain for organization identification.
 *
 *       **Supported identifier formats:**
 *       - MongoDB ObjectId: `663041cf7a14c7c000a3f999`
 *       - Subdomain: `acme-corp`
 *     tags: [DynamicRoles]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: orgIdOrSubdomain
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization MongoDB ObjectId or subdomain
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           subdomain:
 *             value: "acme-corp"
 *             summary: Organization subdomain
 *     responses:
 *       200:
 *         description: List of roles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 roles:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Role'
 *                 metadata:
 *                   type: object
 *                   properties:
 *                     organization_id:
 *                       type: string
 *                     total_count:
 *                       type: integer
 *                     timestamp:
 *                       type: string
 *       400:
 *         description: Invalid organization identifier
 *       404:
 *         description: Organization not found
 *       500:
 *         description: Internal server error
 */
router.get('/org/:orgIdOrSubdomain', authenticate, rbac('role:read'), async (req, res) => {
  try {
    const { orgIdOrSubdomain } = req.params;

    // Validate identifier format (ObjectId or non-empty string for subdomain)
    if (!orgIdOrSubdomain || orgIdOrSubdomain.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid organization identifier',
        error: {
          code: 'INVALID_ORG_IDENTIFIER',
          details: 'Organization identifier must be either a valid MongoDB ObjectId or subdomain'
        }
      });
    }

    // Resolve organization using flexible identifier
    const organization = await resolveOrganization(orgIdOrSubdomain);
    if (!organization) {
      return res.status(404).json({
        success: false,
        message: 'Organization not found',
        error: {
          code: 'ORGANIZATION_NOT_FOUND',
          details: isObjectId(orgIdOrSubdomain)
            ? `Organization with ID ${orgIdOrSubdomain} does not exist`
            : `Organization with subdomain ${orgIdOrSubdomain} does not exist`
        }
      });
    }

    // Use RBACRole model with correct field name 'organization'
    // Include both organization-scoped roles and system roles
    const roles = await RBACRole.find({
      $or: [
        { scope: 'organization', organization: organization._id, 'metadata.isActive': true },
        { scope: 'system', 'metadata.isActive': true }
      ]
    })
    .populate('organization', 'name subdomain')
    .sort({ 'hierarchy.level': 1, name: 1 });

    logger.info('Retrieved roles for organization', {
      component: 'roles-crud',
      operation: 'get_org_roles',
      metadata: {
        organization_identifier: orgIdOrSubdomain,
        organization_id: organization._id,
        organization_name: organization.name,
        roles_count: roles.length,
        requested_by: req.user._id
      }
    });

    res.json({
      success: true,
      roles: roles,
      metadata: {
        organization: {
          id: organization._id,
          name: organization.name,
          subdomain: organization.subdomain
        },
        total_count: roles.length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to retrieve organization roles', {
      component: 'roles-crud',
      operation: 'get_org_roles',
      error: error.message,
      metadata: {
        organization_identifier: req.params.orgIdOrSubdomain,
        requested_by: req.user._id
      }
    });

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve roles',
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        details: 'An error occurred while fetching organization roles'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/roles/{roleIdOrName}:
 *   get:
 *     summary: Get a single custom role with flexible identifier support
 *     description: |
 *       Retrieve a specific role by MongoDB ObjectId or role name.
 *       When using role name, you can optionally provide organization context via query parameter.
 *
 *       **Supported identifier formats:**
 *       - MongoDB ObjectId: `663041cf7a14c7c000a3f999`
 *       - Role name: `editor` (requires orgId query parameter for scoping)
 *     tags: [DynamicRoles]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: roleIdOrName
 *         required: true
 *         schema:
 *           type: string
 *         description: Role MongoDB ObjectId or role name
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           roleName:
 *             value: "editor"
 *             summary: Role name (requires orgId query parameter)
 *       - in: query
 *         name: orgId
 *         required: false
 *         schema:
 *           type: string
 *         description: Organization ObjectId or subdomain (required when using role name)
 *         example: "acme-corp"
 *     responses:
 *       200:
 *         description: Role details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 role:
 *                   $ref: '#/components/schemas/Role'
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Invalid role identifier or missing organization context
 *       404:
 *         description: Role not found
 *       500:
 *         description: Internal server error
 */
router.get('/:roleIdOrName', authenticate, rbac('role:read'), async (req, res) => {
  try {
    const { roleIdOrName } = req.params;
    const { orgId } = req.query;

    // Validate identifier format
    if (!roleIdOrName || roleIdOrName.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role identifier',
        error: {
          code: 'INVALID_ROLE_IDENTIFIER',
          details: 'Role identifier must be either a valid MongoDB ObjectId or role name'
        }
      });
    }

    let role;
    let organizationContext = null;

    if (isObjectId(roleIdOrName)) {
      // Use ObjectId to find role directly
      role = await RBACRole.findById(roleIdOrName)
        .populate('organization', 'name subdomain');
    } else {
      // Role name provided - need organization context
      if (!orgId) {
        return res.status(400).json({
          success: false,
          message: 'Organization context required for role name lookup',
          error: {
            code: 'MISSING_ORG_CONTEXT',
            details: 'When using role name, provide orgId query parameter for organization context'
          }
        });
      }

      // Resolve organization first
      organizationContext = await resolveOrganization(orgId);
      if (!organizationContext) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: isObjectId(orgId)
              ? `Organization with ID ${orgId} does not exist`
              : `Organization with subdomain ${orgId} does not exist`
          }
        });
      }

      // Find role by name within organization context
      role = await resolveRole(roleIdOrName, organizationContext._id);
    }

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found',
        error: {
          code: 'ROLE_NOT_FOUND',
          details: isObjectId(roleIdOrName)
            ? `Role with ID ${roleIdOrName} does not exist`
            : `Role '${roleIdOrName}' not found in organization`
        }
      });
    }

    logger.info('Role retrieved successfully', {
      component: 'roles-crud',
      operation: 'get_role_by_identifier',
      metadata: {
        role_identifier: roleIdOrName,
        role_id: role._id,
        role_name: role.name,
        organization_id: role.organization?._id,
        organization_name: role.organization?.name,
        requested_by: req.user._id,
        lookup_method: isObjectId(roleIdOrName) ? 'objectId' : 'roleName'
      }
    });

    res.json({
      success: true,
      role: role,
      metadata: {
        lookup_method: isObjectId(roleIdOrName) ? 'objectId' : 'roleName',
        organization_context: organizationContext ? {
          id: organizationContext._id,
          name: organizationContext.name,
          subdomain: organizationContext.subdomain
        } : null,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to retrieve role', {
      component: 'roles-crud',
      operation: 'get_role_by_identifier',
      error: error.message,
      metadata: {
        role_identifier: req.params.roleIdOrName,
        org_context: req.query.orgId,
        requested_by: req.user._id
      }
    });

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve role',
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        details: 'An error occurred while fetching the role'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/roles/{roleIdOrName}:
 *   put:
 *     summary: Update a custom role with flexible identifier support
 *     description: |
 *       Update a role's name, description, or permissions using MongoDB ObjectId or role name.
 *       When using role name, you must provide organization context via query parameter.
 *
 *       **Supported identifier formats:**
 *       - MongoDB ObjectId: `663041cf7a14c7c000a3f999`
 *       - Role name: `editor` (requires orgId query parameter for scoping)
 *     tags: [DynamicRoles]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: roleIdOrName
 *         required: true
 *         schema:
 *           type: string
 *         description: Role MongoDB ObjectId or role name
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           roleName:
 *             value: "editor"
 *             summary: Role name (requires orgId query parameter)
 *       - in: query
 *         name: orgId
 *         required: false
 *         schema:
 *           type: string
 *         description: Organization ObjectId or subdomain (required when using role name)
 *         example: "acme-corp"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: New role name
 *               description:
 *                 type: string
 *                 description: New role description
 *               permissions:
 *                 type: array
 *                 description: Updated permissions array
 *                 items:
 *                   type: object
 *                   properties:
 *                     resource:
 *                       type: string
 *                       example: "user_object"
 *                     actions:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["read", "create", "update"]
 *     responses:
 *       200:
 *         description: Role updated successfully
 *       400:
 *         description: Invalid role identifier or missing organization context
 *       403:
 *         description: Cannot update system roles
 *       404:
 *         description: Role not found
 *       500:
 *         description: Internal server error
 */
router.put('/:roleIdOrName', authenticate, rbac('role:update'), async (req, res) => {
  try {
    const { roleIdOrName } = req.params;
    const { orgId } = req.query;
    const update = req.body;

    // Validate identifier format
    if (!roleIdOrName || roleIdOrName.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role identifier',
        error: {
          code: 'INVALID_ROLE_IDENTIFIER',
          details: 'Role identifier must be either a valid MongoDB ObjectId or role name'
        }
      });
    }

    let existingRole;
    let organizationContext = null;

    if (isObjectId(roleIdOrName)) {
      // Use ObjectId to find role directly
      existingRole = await RBACRole.findById(roleIdOrName)
        .populate('organization', 'name subdomain');
    } else {
      // Role name provided - need organization context
      if (!orgId) {
        return res.status(400).json({
          success: false,
          message: 'Organization context required for role name lookup',
          error: {
            code: 'MISSING_ORG_CONTEXT',
            details: 'When using role name, provide orgId query parameter for organization context'
          }
        });
      }

      // Resolve organization first
      organizationContext = await resolveOrganization(orgId);
      if (!organizationContext) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: isObjectId(orgId)
              ? `Organization with ID ${orgId} does not exist`
              : `Organization with subdomain ${orgId} does not exist`
          }
        });
      }

      // Find role by name within organization context
      existingRole = await resolveRole(roleIdOrName, organizationContext._id);
    }

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found',
        error: {
          code: 'ROLE_NOT_FOUND',
          details: isObjectId(roleIdOrName)
            ? `Role with ID ${roleIdOrName} does not exist`
            : `Role '${roleIdOrName}' not found in organization`
        }
      });
    }

    // Prevent updating system roles
    if (existingRole.metadata?.isSystem) {
      return res.status(403).json({
        success: false,
        message: 'Cannot update system roles',
        error: {
          code: 'SYSTEM_ROLE_UPDATE_FORBIDDEN',
          details: 'System roles cannot be modified'
        }
      });
    }

    // Update the role with audit trail
    const updatedRole = await RBACRole.findByIdAndUpdate(
      existingRole._id,
      {
        ...update,
        'audit.lastModified': new Date(),
        'audit.modifiedBy': req.user._id,
        $push: {
          'audit.changeHistory': {
            action: 'updated',
            timestamp: new Date(),
            performedBy: req.user._id,
            changes: update,
            reason: 'Role update via API'
          }
        }
      },
      { new: true }
    ).populate('organization', 'name subdomain');

    logger.info('Role updated successfully', {
      component: 'roles-crud',
      operation: 'update_role_by_identifier',
      metadata: {
        role_identifier: roleIdOrName,
        role_id: existingRole._id,
        role_name: existingRole.name,
        organization_id: existingRole.organization?._id,
        organization_name: existingRole.organization?.name,
        updated_by: req.user._id,
        lookup_method: isObjectId(roleIdOrName) ? 'objectId' : 'roleName',
        changes: Object.keys(update)
      }
    });

    res.json({
      success: true,
      message: 'Role updated successfully',
      role: updatedRole,
      metadata: {
        lookup_method: isObjectId(roleIdOrName) ? 'objectId' : 'roleName',
        organization_context: organizationContext ? {
          id: organizationContext._id,
          name: organizationContext.name,
          subdomain: organizationContext.subdomain
        } : null,
        timestamp: new Date().toISOString(),
        updated_by: req.user._id
      }
    });
  } catch (error) {
    logger.error('Failed to update role', {
      component: 'roles-crud',
      operation: 'update_role_by_identifier',
      error: error.message,
      metadata: {
        role_identifier: req.params.roleIdOrName,
        org_context: req.query.orgId,
        updated_by: req.user._id
      }
    });

    res.status(500).json({
      success: false,
      message: 'Failed to update role',
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        details: 'An error occurred while updating the role'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/roles/{roleIdOrName}:
 *   delete:
 *     summary: Delete a custom role with flexible identifier support
 *     description: |
 *       Soft delete a role by marking it as inactive using MongoDB ObjectId or role name.
 *       When using role name, you must provide organization context via query parameter.
 *
 *       **Supported identifier formats:**
 *       - MongoDB ObjectId: `663041cf7a14c7c000a3f999`
 *       - Role name: `editor` (requires orgId query parameter for scoping)
 *     tags: [DynamicRoles]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: roleIdOrName
 *         required: true
 *         schema:
 *           type: string
 *         description: Role MongoDB ObjectId or role name
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           roleName:
 *             value: "editor"
 *             summary: Role name (requires orgId query parameter)
 *       - in: query
 *         name: orgId
 *         required: false
 *         schema:
 *           type: string
 *         description: Organization ObjectId or subdomain (required when using role name)
 *         example: "acme-corp"
 *     responses:
 *       200:
 *         description: Role deleted successfully
 *       400:
 *         description: Invalid role identifier or missing organization context
 *       403:
 *         description: Cannot delete system roles
 *       404:
 *         description: Role not found
 *       500:
 *         description: Internal server error
 */
router.delete('/:roleIdOrName', authenticate, rbac('role:delete'), async (req, res) => {
  try {
    const { roleIdOrName } = req.params;
    const { orgId } = req.query;

    // Validate identifier format
    if (!roleIdOrName || roleIdOrName.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role identifier',
        error: {
          code: 'INVALID_ROLE_IDENTIFIER',
          details: 'Role identifier must be either a valid MongoDB ObjectId or role name'
        }
      });
    }

    let existingRole;
    let organizationContext = null;

    if (isObjectId(roleIdOrName)) {
      // Use ObjectId to find role directly
      existingRole = await RBACRole.findById(roleIdOrName)
        .populate('organization', 'name subdomain');
    } else {
      // Role name provided - need organization context
      if (!orgId) {
        return res.status(400).json({
          success: false,
          message: 'Organization context required for role name lookup',
          error: {
            code: 'MISSING_ORG_CONTEXT',
            details: 'When using role name, provide orgId query parameter for organization context'
          }
        });
      }

      // Resolve organization first
      organizationContext = await resolveOrganization(orgId);
      if (!organizationContext) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: isObjectId(orgId)
              ? `Organization with ID ${orgId} does not exist`
              : `Organization with subdomain ${orgId} does not exist`
          }
        });
      }

      // Find role by name within organization context
      existingRole = await resolveRole(roleIdOrName, organizationContext._id);
    }

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found',
        error: {
          code: 'ROLE_NOT_FOUND',
          details: isObjectId(roleIdOrName)
            ? `Role with ID ${roleIdOrName} does not exist`
            : `Role '${roleIdOrName}' not found in organization`
        }
      });
    }

    // Prevent deleting system roles
    if (existingRole.metadata?.isSystem) {
      return res.status(403).json({
        success: false,
        message: 'Cannot delete system roles',
        error: {
          code: 'SYSTEM_ROLE_DELETE_FORBIDDEN',
          details: 'System roles cannot be deleted'
        }
      });
    }

    // Soft delete by marking as inactive instead of hard delete
    await RBACRole.findByIdAndUpdate(
      existingRole._id,
      {
        'metadata.isActive': false,
        'audit.lastModified': new Date(),
        'audit.modifiedBy': req.user._id,
        $push: {
          'audit.changeHistory': {
            action: 'deleted',
            timestamp: new Date(),
            performedBy: req.user._id,
            reason: 'Role deletion via API'
          }
        }
      },
      { new: true }
    );

    // Clear caches to ensure fresh data
    clearCaches();

    logger.info('Role soft deleted', {
      component: 'roles-crud',
      operation: 'delete_role_by_identifier',
      metadata: {
        role_identifier: roleIdOrName,
        role_id: existingRole._id,
        role_name: existingRole.name,
        organization_id: existingRole.organization?._id,
        organization_name: existingRole.organization?.name,
        deleted_by: req.user._id,
        lookup_method: isObjectId(roleIdOrName) ? 'objectId' : 'roleName'
      }
    });

    res.json({
      success: true,
      message: 'Role deleted successfully',
      metadata: {
        role_id: existingRole._id,
        role_name: existingRole.name,
        lookup_method: isObjectId(roleIdOrName) ? 'objectId' : 'roleName',
        organization_context: organizationContext ? {
          id: organizationContext._id,
          name: organizationContext.name,
          subdomain: organizationContext.subdomain
        } : null,
        timestamp: new Date().toISOString(),
        deleted_by: req.user._id
      }
    });
  } catch (error) {
    logger.error('Failed to delete role', {
      component: 'roles-crud',
      operation: 'delete_role_by_identifier',
      error: error.message,
      metadata: {
        role_identifier: req.params.roleIdOrName,
        org_context: req.query.orgId,
        deleted_by: req.user._id
      }
    });

    res.status(500).json({
      success: false,
      message: 'Failed to delete role',
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        details: 'An error occurred while deleting the role'
      }
    });
  }
});

/**
 * @swagger
 * /api/v1/roles/users/{userIdOrEmail}/org/{orgIdOrSubdomain}/assign-role:
 *   patch:
 *     summary: Assign a custom role to a user in an organization with flexible identifier support
 *     description: |
 *       Assign a role to a user within an organization using flexible identifiers.
 *       Supports MongoDB ObjectIds and human-readable identifiers for all parameters.
 *
 *       **Supported identifier formats:**
 *       - User: MongoDB ObjectId (`663041cf7a14c7c000a3f999`) OR email address (`<EMAIL>`)
 *       - Organization: MongoDB ObjectId (`663041cf7a14c7c000a3f999`) OR subdomain (`acme-corp`)
 *       - Role: MongoDB ObjectId (`663041cf7a14c7c000a3f999`) OR role name (`editor`)
 *     tags: [DynamicRoles]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: userIdOrEmail
 *         required: true
 *         schema:
 *           type: string
 *         description: User MongoDB ObjectId or email address
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           email:
 *             value: "<EMAIL>"
 *             summary: Email address
 *       - in: path
 *         name: orgIdOrSubdomain
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization MongoDB ObjectId or subdomain
 *         examples:
 *           objectId:
 *             value: "663041cf7a14c7c000a3f999"
 *             summary: MongoDB ObjectId
 *           subdomain:
 *             value: "acme-corp"
 *             summary: Organization subdomain
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               roleId:
 *                 type: string
 *                 description: Role MongoDB ObjectId to assign
 *                 example: "663041cf7a14c7c000a3f999"
 *               roleName:
 *                 type: string
 *                 description: Role name to assign (alternative to roleId)
 *                 example: "editor"
 *             oneOf:
 *               - required: [roleId]
 *               - required: [roleName]
 *     responses:
 *       200:
 *         description: Role assigned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Role assigned successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                     organization:
 *                       type: object
 *                     role:
 *                       type: object
 *                     assignment:
 *                       type: object
 *       400:
 *         description: Invalid identifiers or missing role specification
 *       404:
 *         description: User, organization, or role not found
 *       500:
 *         description: Internal server error
 */

// PATCH /api/v1/roles/users/:userIdOrEmail/org/:orgIdOrSubdomain/assign-role
router.patch(
  '/users/:userIdOrEmail/org/:orgIdOrSubdomain/assign-role',
  authenticate,
  rbac('role:assign'),
  roleController.assignRole
);

/**
 * @swagger
 * /api/v1/roles/available:
 *   get:
 *     summary: Get all available roles and permissions
 *     tags: [Roles]
 *     description: |
 *       Retrieves a list of all available roles and their associated permissions.
 *       Requires authentication.
 *     responses:
 *       200:
 *         description: List of roles and permissions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: "admin"
 *                           permissions:
 *                             type: array
 *                             items:
 *                               type: string
 *                             example: ["config:read", "user:create"]
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/available', roleController.getAvailableRoles);

/**
 * @swagger
 * /api/v1/roles/available-roles:
 *   get:
 *     summary: Get all available roles and their permissions
 *     tags: [Roles]
 *     description: Returns a comprehensive list of all available roles and their associated permissions
 *     responses:
 *       200:
 *         description: List of all available roles and permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     systemRoles:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           description:
 *                             type: string
 *                           permissions:
 *                             type: array
 *                             items:
 *                               type: string
 *                     availablePermissions:
 *                       type: object
 *       500:
 *         description: Server error
 */
router.get('/available-roles', roleController.getAvailableRoles);

router.get('/test-public', (req, res) => res.json({ success: true, message: 'Public route works!' }));

module.exports = router;

/**
 * @swagger
 * components:
 *   schemas:
 *     Role:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         org:
 *           type: string
 *           description: Organization ObjectID
 *         name:
 *           type: string
 *           description: Unique role name within organization
 *         description:
 *           type: string
 *           description: Description of the role
 *         permissions:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               resource:
 *                 type: string
 *                 example: user_object
 *               actions:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: [read, create, update, delete]
 */

