// src/config/environment.js
require('dotenv').config();

/**
 * Centralized environment configuration
 * Validates and provides typed access to environment variables
 */
class EnvironmentConfig {
  constructor() {
    this.validateRequiredVariables();
  }

  // Database Configuration
  get database() {
    return {
      mongoUri: process.env.MONGO_URI,
      auditMongoUri: process.env.AUDIT_MONGO_URI
    };
  }

  // Server Configuration
  get server() {
    return {
      port: parseInt(process.env.PORT) || 3000,
      nodeEnv: process.env.NODE_ENV || 'development',
      sessionSecret: process.env.SESSION_SECRET || 'your-secret-key-change-this'
    };
  }

  // Authentication Configuration
  get auth() {
    return {
      jwtSecret: process.env.JWT_SECRET,
      docsAccessPassword: process.env.DOCS_ACCESS_PASSWORD || 'admin123',
      // Firebase
      firebaseProjectId: process.env.FIREBASE_PROJECT_ID,
      firebasePrivateKey: process.env.FIREBASE_PRIVATE_KEY,
      firebaseClientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      // Auth0
      auth0Secret: process.env.AUTH0_SECRET,
      auth0ClientId: process.env.AUTH0_CLIENT_ID,
      auth0IssuerBaseUrl: process.env.AUTH0_ISSUER_BASE_URL,
      auth0BaseUrl: process.env.AUTH0_BASE_URL,
      auth0Audience: process.env.AUTH0_AUDIENCE
    };
  }

  // Subdomain Configuration
  get subdomain() {
    return {
      extractionEnabled: process.env.SUBDOMAIN_EXTRACTION_ENABLED === 'true',
      organizationMapping: process.env.SUBDOMAIN_ORGANIZATION_MAPPING === 'true',
      rootDomain: process.env.ROOT_DOMAIN || 'digimeet.live',
      defaultOrganizationSubdomain: process.env.DEFAULT_ORGANIZATION_SUBDOMAIN || 'app',
      enableSubdomainRouting: process.env.ENABLE_SUBDOMAIN_ROUTING === 'true'
    };
  }

  // CORS Configuration
  get cors() {
    return {
      wildcardEnabled: process.env.CORS_WILDCARD_ENABLED === 'true',
      subdomainPattern: process.env.CORS_SUBDOMAIN_PATTERN,
      origins: process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : [],
      allowedOrigins: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : []
    };
  }

  // Domain Configuration
  get domain() {
    return {
      productionServerUrl: process.env.PRODUCTION_SERVER_URL,
      wildcardDomain: process.env.WILDCARD_DOMAIN,
      serverUrl: process.env.SERVER_URL
    };
  }

  // Email Configuration
  get email() {
    return {
      smtpUser: process.env.SMTP_USER,
      smtpPass: process.env.SMTP_PASS,
      smtpFrom: process.env.SMTP_FROM
    };
  }

  // Audit Configuration
  get audit() {
    return {
      loggingEnabled: process.env.AUDIT_LOGGING_ENABLED !== 'false',
      retentionDays: parseInt(process.env.AUDIT_RETENTION_DAYS) || 90,
      batchSize: parseInt(process.env.AUDIT_BATCH_SIZE) || 100
    };
  }

  // Development Configuration
  get development() {
    return {
      defaultOrgId: process.env.DEFAULT_ORG_ID || "6657c33e1596ac1f3aa7c456"
    };
  }

  // Deployment Configuration
  get deployment() {
    return {
      vercelEnv: process.env.VERCEL_ENV,
      isVercel: !!process.env.VERCEL,
      isProduction: process.env.NODE_ENV === 'production',
      isDevelopment: process.env.NODE_ENV === 'development'
    };
  }

  /**
   * Validate required environment variables
   */
  validateRequiredVariables() {
    const required = [
      'MONGO_URI'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      console.error('Missing required environment variables:', missing);
      if (process.env.NODE_ENV === 'production') {
        process.exit(1);
      }
    }
  }

  /**
   * Get all configuration as a single object
   */
  getAll() {
    return {
      database: this.database,
      server: this.server,
      auth: this.auth,
      subdomain: this.subdomain,
      cors: this.cors,
      domain: this.domain,
      email: this.email,
      audit: this.audit,
      development: this.development,
      deployment: this.deployment
    };
  }

  /**
   * Get safe configuration (without secrets) for debugging
   */
  getSafeConfig() {
    const config = this.getAll();
    
    // Remove sensitive information
    delete config.auth.jwtSecret;
    delete config.auth.firebasePrivateKey;
    delete config.auth.auth0Secret;
    delete config.auth.docsAccessPassword;
    delete config.email.smtpPass;
    delete config.database.mongoUri;
    delete config.database.auditMongoUri;
    
    return config;
  }

  /**
   * Check if Auth0 is properly configured
   */
  isAuth0Configured() {
    return !!(this.auth.auth0ClientId && this.auth.auth0Secret && this.auth.auth0IssuerBaseUrl);
  }

  /**
   * Check if Firebase is properly configured
   */
  isFirebaseConfigured() {
    return !!(this.auth.firebaseProjectId && this.auth.firebasePrivateKey && this.auth.firebaseClientEmail);
  }

  /**
   * Check if email is properly configured
   */
  isEmailConfigured() {
    return !!(this.email.smtpUser && this.email.smtpPass && this.email.smtpFrom);
  }
}

// Export singleton instance
module.exports = new EnvironmentConfig();
