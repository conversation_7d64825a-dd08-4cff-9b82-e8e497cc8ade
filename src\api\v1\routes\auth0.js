// src/api/v1/routes/auth0.js
const express = require('express');
const auth0Middleware = require('../../../middleware/auth0Middleware');
const logger = require('../../../services/logger');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Auth0
 *   description: Auth0 authentication endpoints with subdomain support
 */

/**
 * @swagger
 * /api/v1/auth0/profile:
 *   get:
 *     summary: Get Auth0 user profile
 *     tags: [Auth0]
 *     description: |
 *       Returns the current user's Auth0 profile and local user data.
 *       Works with subdomain-specific authentication.
 *     security:
 *       - Auth0: []
 *     responses:
 *       200:
 *         description: User profile data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isAuthenticated:
 *                   type: boolean
 *                 auth0User:
 *                   type: object
 *                   description: Auth0 user profile
 *                 localUser:
 *                   type: object
 *                   description: Local database user profile
 *                 organization:
 *                   type: object
 *                   description: Current organization context
 *       401:
 *         description: Not authenticated
 */
router.get('/profile', (req, res) => {
  try {
    if (req.oidc && req.oidc.isAuthenticated()) {
      res.json({
        isAuthenticated: true,
        auth0User: req.oidc.user,
        localUser: req.user ? {
          _id: req.user._id,
          email: req.user.email,
          name: req.user.name,
          status: req.user.status,
          organizations: req.user.organizations
        } : null,
        organization: req.organization ? {
          id: req.organization._id,
          name: req.organization.name,
          subdomain: req.organization.subdomain
        } : null,
        subdomain: req.subdomain
      });
    } else {
      res.json({
        isAuthenticated: false,
        message: 'User not authenticated via Auth0'
      });
    }
  } catch (error) {
    logger.error('Auth0 profile endpoint error', {
      component: 'auth0-routes',
      operation: 'get_profile',
      error: error.message
    });
    res.status(500).json({
      error: 'Failed to get profile',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/auth0/login-url:
 *   get:
 *     summary: Get Auth0 login URL for current subdomain
 *     tags: [Auth0]
 *     description: |
 *       Returns the Auth0 login URL configured for the current subdomain.
 *       Useful for frontend applications to redirect users to login.
 *     parameters:
 *       - in: query
 *         name: returnTo
 *         schema:
 *           type: string
 *         description: URL to redirect to after login
 *     responses:
 *       200:
 *         description: Login URL information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 loginUrl:
 *                   type: string
 *                   description: Auth0 login URL
 *                 subdomain:
 *                   type: string
 *                   description: Current subdomain
 *                 organization:
 *                   type: object
 *                   description: Organization context
 */
router.get('/login-url', (req, res) => {
  try {
    const returnTo = req.query.returnTo || '/';
    const loginUrl = auth0Middleware.getLoginUrl(req, returnTo);
    
    res.json({
      loginUrl,
      subdomain: req.subdomain?.subdomain || null,
      organization: req.organization ? {
        id: req.organization._id,
        name: req.organization.name,
        subdomain: req.organization.subdomain
      } : null,
      returnTo
    });
  } catch (error) {
    logger.error('Auth0 login URL endpoint error', {
      component: 'auth0-routes',
      operation: 'get_login_url',
      error: error.message
    });
    res.status(500).json({
      error: 'Failed to get login URL',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/auth0/logout-url:
 *   get:
 *     summary: Get Auth0 logout URL for current subdomain
 *     tags: [Auth0]
 *     description: |
 *       Returns the Auth0 logout URL configured for the current subdomain.
 *       Useful for frontend applications to redirect users to logout.
 *     parameters:
 *       - in: query
 *         name: returnTo
 *         schema:
 *           type: string
 *         description: URL to redirect to after logout
 *     responses:
 *       200:
 *         description: Logout URL information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 logoutUrl:
 *                   type: string
 *                   description: Auth0 logout URL
 *                 subdomain:
 *                   type: string
 *                   description: Current subdomain
 */
router.get('/logout-url', (req, res) => {
  try {
    const returnTo = req.query.returnTo || '/';
    const logoutUrl = auth0Middleware.getLogoutUrl(req, returnTo);
    
    res.json({
      logoutUrl,
      subdomain: req.subdomain?.subdomain || null,
      returnTo
    });
  } catch (error) {
    logger.error('Auth0 logout URL endpoint error', {
      component: 'auth0-routes',
      operation: 'get_logout_url',
      error: error.message
    });
    res.status(500).json({
      error: 'Failed to get logout URL',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/auth0/protected:
 *   get:
 *     summary: Protected route example using Auth0
 *     tags: [Auth0]
 *     description: |
 *       Example of a protected route that requires Auth0 authentication.
 *       Returns user and organization context.
 *     security:
 *       - Auth0: []
 *     responses:
 *       200:
 *         description: Protected content
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   type: object
 *                 organization:
 *                   type: object
 *                 subdomain:
 *                   type: object
 *       401:
 *         description: Authentication required
 */
router.get('/protected', auth0Middleware.requireAuth(), (req, res) => {
  try {
    res.json({
      message: 'This is a protected route using Auth0',
      user: req.oidc.user,
      localUser: req.user ? {
        _id: req.user._id,
        email: req.user.email,
        name: req.user.name,
        status: req.user.status
      } : null,
      organization: req.organization ? {
        id: req.organization._id,
        name: req.organization.name,
        subdomain: req.organization.subdomain
      } : null,
      subdomain: req.subdomain,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Auth0 protected route error', {
      component: 'auth0-routes',
      operation: 'protected_route',
      error: error.message
    });
    res.status(500).json({
      error: 'Protected route error',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/v1/auth0/status:
 *   get:
 *     summary: Get Auth0 authentication status
 *     tags: [Auth0]
 *     description: |
 *       Returns the current authentication status and configuration for the subdomain.
 *     responses:
 *       200:
 *         description: Authentication status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 auth0Enabled:
 *                   type: boolean
 *                 isAuthenticated:
 *                   type: boolean
 *                 subdomain:
 *                   type: object
 *                 organization:
 *                   type: object
 */
router.get('/status', (req, res) => {
  try {
    res.json({
      auth0Enabled: auth0Middleware.isEnabled,
      isAuthenticated: req.oidc ? req.oidc.isAuthenticated() : false,
      subdomain: req.subdomain || null,
      organization: req.organization ? {
        id: req.organization._id,
        name: req.organization.name,
        subdomain: req.organization.subdomain
      } : null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Auth0 status endpoint error', {
      component: 'auth0-routes',
      operation: 'get_status',
      error: error.message
    });
    res.status(500).json({
      error: 'Failed to get status',
      message: error.message
    });
  }
});

module.exports = router;
