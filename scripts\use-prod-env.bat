@echo off

REM Script to switch to production environment

echo 🔄 Switching to PRODUCTION environment...

REM Copy production environment file to .env
if exist ".env.production" (
    copy ".env.production" ".env" >nul
    echo ✅ Copied .env.production to .env
    echo 🌐 Now using PRODUCTION configuration
    echo.
    echo 📋 Production Environment Settings:
    echo    - Server: https://digimeet.live
    echo    - Database: production database
    echo    - CORS: Restricted to digimeet.live domains
    echo    - HTTPS: Enabled
    echo    - Subdomain routing: Enabled
    echo.
    echo ⚠️  WARNING: You are now using PRODUCTION settings!
    echo 🚀 Deploy with: git push origin main
) else (
    echo ❌ Error: .env.production file not found!
    exit /b 1
)

pause
