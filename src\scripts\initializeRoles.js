// src/scripts/initializeRoles.js
const mongoose = require('mongoose');
const RBACRole = require('../models/RBACRole');
const config = require('../config/environment');

/**
 * Initialize system-wide RBAC roles
 */
async function initializeSystemRoles() {
  try {
    console.log('🚀 Starting RBAC role initialization...');
    
    // Connect to database
    await mongoose.connect(config.database.mongoUri);
    console.log('✅ Connected to MongoDB');

    // Get system role templates
    const systemRoles = RBACRole.getSystemRoles();
    console.log(`📋 Found ${systemRoles.length} system role templates`);

    let createdCount = 0;
    let skippedCount = 0;

    for (const roleTemplate of systemRoles) {
      // Check if role already exists (for system-scoped roles)
      if (roleTemplate.scope === 'system') {
        const existingRole = await RBACRole.findOne({
          name: roleTemplate.name,
          scope: 'system'
        });

        if (!existingRole) {
          const newRole = await RBACRole.create({
            ...roleTemplate,
            audit: {
              createdBy: null, // System created
              createdAt: new Date(),
              usageCount: 0,
              changeHistory: [{
                action: 'created',
                timestamp: new Date(),
                performedBy: null,
                reason: 'System initialization'
              }]
            }
          });

          console.log(`✅ Created system role: ${newRole.name} (${newRole._id})`);
          createdCount++;
        } else {
          console.log(`⏭️  System role already exists: ${existingRole.name}`);
          skippedCount++;
        }
      }
    }

    console.log(`\n📊 Role initialization completed:`);
    console.log(`   - Created: ${createdCount} roles`);
    console.log(`   - Skipped: ${skippedCount} roles (already exist)`);
    console.log(`   - Total system roles in database: ${await RBACRole.countDocuments({ scope: 'system' })}`);

    // List all system roles
    const allSystemRoles = await RBACRole.find({ scope: 'system' });
    console.log(`\n📋 System roles in database:`);
    allSystemRoles.forEach(role => {
      console.log(`   - ${role.name} (Level ${role.hierarchy.level})`);
    });

  } catch (error) {
    console.error('❌ Failed to initialize roles:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n👋 Database connection closed');
  }
}

/**
 * Initialize roles for a specific organization
 */
async function initializeOrganizationRoles(organizationId) {
  try {
    console.log(`🏢 Initializing roles for organization: ${organizationId}`);
    
    const systemRoles = RBACRole.getSystemRoles();
    const orgRoles = systemRoles.filter(r => r.scope === 'organization');
    
    let createdCount = 0;
    let skippedCount = 0;

    for (const roleTemplate of orgRoles) {
      const existingRole = await RBACRole.findOne({
        name: roleTemplate.name,
        scope: 'organization',
        organization: organizationId
      });

      if (!existingRole) {
        const newRole = await RBACRole.create({
          ...roleTemplate,
          organization: organizationId,
          audit: {
            createdBy: null,
            createdAt: new Date(),
            usageCount: 0,
            changeHistory: [{
              action: 'created',
              timestamp: new Date(),
              performedBy: null,
              reason: 'Organization role initialization'
            }]
          }
        });

        console.log(`✅ Created org role: ${newRole.name} for org ${organizationId}`);
        createdCount++;
      } else {
        console.log(`⏭️  Org role already exists: ${existingRole.name}`);
        skippedCount++;
      }
    }

    console.log(`📊 Organization role initialization completed: ${createdCount} created, ${skippedCount} skipped`);
    return { success: true, created: createdCount, skipped: skippedCount };

  } catch (error) {
    console.error(`❌ Failed to initialize organization roles:`, error);
    return { success: false, error: error.message };
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (command === 'org' && args[1]) {
    // Initialize roles for specific organization
    await mongoose.connect(config.database.mongoUri);
    await initializeOrganizationRoles(args[1]);
    await mongoose.connection.close();
  } else {
    // Initialize system roles
    await initializeSystemRoles();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { initializeSystemRoles, initializeOrganizationRoles };
