<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Diggi API Documentation</title>
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist/swagger-ui.css" />
    <style>
      /* Custom styles for Swagger UI */
      .swagger-ui .topbar {
        display: none;
      }

      .custom-header {
        background-color: #1b1b1b;
        color: white;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
      }

      .custom-header h1 {
        margin: 0;
        font-size: 24px;
      }

      .custom-header p {
        margin: 10px 0 0;
        font-size: 16px;
      }

      .swagger-ui .info .title {
        font-size: 28px;
      }

      .swagger-ui .scheme-container {
        background-color: #f8f9fa;
        box-shadow: 0 1px 2px rgba(0,0,0,.1);
      }

      .swagger-ui .btn.authorize {
        background-color: #4CAF50;
        color: white;
        border-color: #4CAF50;
      }

      .swagger-ui .btn.authorize svg {
        fill: white;
      }

      .swagger-ui .opblock-tag {
        font-size: 18px;
      }

      .swagger-ui .opblock .opblock-summary-operation-id,
      .swagger-ui .opblock .opblock-summary-path,
      .swagger-ui .opblock .opblock-summary-path__deprecated {
        font-size: 16px;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .custom-header h1 {
          font-size: 20px;
        }

        .custom-header p {
          font-size: 14px;
        }
      }
    </style>
  </head>
  <body>
    <div class="custom-header">
      <h1>Auth Diggi API Documentation</h1>
      <p>Comprehensive authentication and user management API with multiple authentication methods</p>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist/swagger-ui-standalone-preset.js"></script>

    <script>
      window.onload = () => {
        const ui = SwaggerUIBundle({
          url: '/swagger.json',
          dom_id: '#swagger-ui',
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset
          ],
          layout: 'StandaloneLayout',
          deepLinking: true,
          docExpansion: 'none',
          defaultModelsExpandDepth: 3,
          defaultModelExpandDepth: 3,
          displayRequestDuration: true,
          filter: true,
          syntaxHighlight: {
            activate: true,
            theme: 'agate'
          },
          tryItOutEnabled: true
        });

        // Add event listener to expand all operations when clicking on a tag
        const expandTagOperations = () => {
          const tagElements = document.getElementsByClassName('opblock-tag');
          for (let i = 0; i < tagElements.length; i++) {
            tagElements[i].addEventListener('click', function() {
              // Wait for operations to be visible
              setTimeout(() => {
                const operations = this.nextElementSibling.getElementsByClassName('opblock');
                for (let j = 0; j < operations.length; j++) {
                  if (!operations[j].classList.contains('is-open')) {
                    operations[j].querySelector('.opblock-summary').click();
                  }
                }
              }, 300);
            });
          }
        };

        // Call after UI is loaded
        ui.initOAuth({
          clientId: "swagger-ui-client",
          appName: "Auth Diggi API",
          scopeSeparator: " ",
          additionalQueryStringParams: {},
          usePkceWithAuthorizationCodeGrant: false
        });

        // Add event listener after UI is loaded
        setTimeout(expandTagOperations, 1000);
      };
    </script>
  </body>
</html>
