// src/middleware/auth.js
const admin = require('../config/firebase');
const jwt   = require('jsonwebtoken');
const User  = require('../models/User');
const AuthBridge = require('../models/AuthBridge');
const TokenBlacklist = require('../api/v1/models/tokenBlacklist');
const { extractToken } = require('../utils/tokenUtils');

async function authenticate(req, res, next) {
  try {
    const token = extractToken(req);

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if token is blacklisted
    const isBlacklisted = await TokenBlacklist.findOne({ token });
    if (isBlacklisted) {
      return res.status(401).json({
        success: false,
        message: 'Token has been invalidated'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;

    let firebaseUid;
    let mongoUser;

    // 1) Try as Firebase ID token
    try {
      const decoded = await admin.auth().verifyIdToken(token);
      firebaseUid = decoded.uid;
      mongoUser = await User.findOne({ firebase_uid: firebaseUid });
    } catch (e) {
      // not a Firebase token – fall back to custom JWT
    }

    // 2) If not found yet, try your custom JWT (now using AuthBridge)
    if (!mongoUser) {
      try {
        const authUser = await AuthBridge.findById(decoded.sub);
        if (authUser) {
          // Find corresponding profile in User
          mongoUser = await User.findOne({ firebase_uid: authUser.firebase_uid });
          firebaseUid = authUser.firebase_uid;
        }
      } catch (e) {
        // neither token worked
      }
    }

    if (!mongoUser) {
      return res.status(403).json({ message: 'invalid_token_or_user_not_found' });
    }

    // ---- STATUS CHECK ----
    if (mongoUser.status && mongoUser.status !== 'active') {
      return res.status(403).json({
        message: `User account is ${mongoUser.status}. Please contact support.`
      });
    }
    // ----------------------

    // Attach full user object with enhanced methods
    // Populate roles and organizations for complete privilege checking
    const fullUser = await User.findById(mongoUser._id)
      .populate({
        path: 'roles.org',
        select: 'name subdomain'
      })
      .populate({
        path: 'roles.role',
        select: 'name description hierarchy permissions'
      });

    if (!fullUser) {
      return res.status(403).json({ message: 'user_profile_not_found' });
    }

    // Attach full user object with all enhanced methods
    req.user = fullUser;

    // Also attach legacy properties for backward compatibility
    req.user.uid = fullUser.firebase_uid;
    req.user.id = fullUser._id;

    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      error: error.message
    });
  }
}

module.exports = { authenticate };
