// src/services/auditLogger.js
const { v4: uuidv4 } = require('uuid');
const auditDatabase = require('./auditDatabase');
const logger = require('./logger');

/**
 * Comprehensive Audit Logging Service
 * Handles all API operation logging with data sanitization and performance optimization
 */
class AuditLogger {
  constructor() {
    this.isEnabled = process.env.AUDIT_LOGGING_ENABLED !== 'false';
    this.batchSize = parseInt(process.env.AUDIT_BATCH_SIZE) || 100;
    this.flushInterval = parseInt(process.env.AUDIT_FLUSH_INTERVAL) || 5000; // 5 seconds
    this.logQueue = [];
    this.isProcessing = false;

    // Start batch processing if enabled
    if (this.isEnabled) {
      this.startBatchProcessor();
    }
  }

  /**
   * Start batch processor for performance optimization
   */
  startBatchProcessor() {
    setInterval(async () => {
      if (this.logQueue.length > 0 && !this.isProcessing) {
        await this.flushLogs();
      }
    }, this.flushInterval);
  }

  /**
   * Main audit logging method
   */
  async logOperation(operationData) {
    if (!this.isEnabled) {
      return;
    }

    try {
      const auditEntry = this.createAuditEntry(operationData);

      // Add to queue for batch processing
      this.logQueue.push(auditEntry);

      // Flush immediately if queue is full or for critical operations
      if (this.logQueue.length >= this.batchSize || this.isCriticalOperation(operationData)) {
        await this.flushLogs();
      }

      // Also log to console/file for immediate visibility
      logger.info('Audit log entry created', {
        component: 'audit-logger',
        operation: 'log_operation',
        metadata: {
          operation_id: auditEntry.operation_id,
          operation_type: auditEntry.operation_type,
          resource_type: auditEntry.resource_type,
          endpoint: auditEntry.endpoint,
          user_id: auditEntry.user_id
        }
      });

    } catch (error) {
      logger.error('Failed to create audit log entry', {
        component: 'audit-logger',
        operation: 'log_operation_error',
        error: error.message,
        metadata: { operation_data: this.sanitizeForLogging(operationData) }
      });
    }
  }

  /**
   * Create standardized audit entry
   */
  createAuditEntry(data) {
    const operationId = uuidv4();
    const timestamp = new Date();

    return {
      operation_id: operationId,
      timestamp: timestamp,

      // User information
      user_id: data.user?.id || data.user?._id || data.userId,
      user_email: data.user?.email || data.userEmail,

      // Operation classification
      operation_type: this.determineOperationType(data),
      resource_type: this.determineResourceType(data),
      resource_id: data.resourceId || data.resource?.id || data.resource?._id,

      // API information
      endpoint: data.endpoint || data.req?.originalUrl || data.req?.url,
      http_method: data.method || data.req?.method,

      // Request/Response data (sanitized)
      request_data: this.sanitizeRequestData(data.requestData || data.req?.body),
      response_status: data.responseStatus || data.res?.statusCode,
      response_data: this.sanitizeResponseData(data.responseData),

      // Client information
      ip_address: this.extractIpAddress(data.req),
      user_agent: data.req?.get('User-Agent'),

      // Change tracking
      changes: this.extractChanges(data),

      // Metadata
      metadata: this.buildMetadata(data),

      // Retention settings
      retention: {
        retention_period_days: this.determineRetentionPeriod(data),
        is_archived: false
      }
    };
  }

  /**
   * Determine operation type from request data
   */
  determineOperationType(data) {
    if (data.operationType) return data.operationType;

    const method = data.method || data.req?.method;
    const endpoint = data.endpoint || data.req?.originalUrl || '';

    // Authentication operations
    if (endpoint.includes('/auth/')) return 'AUTH';

    // Assignment operations
    if (endpoint.includes('/assign') || endpoint.includes('/revoke')) {
      return endpoint.includes('/revoke') ? 'REVOKE' : 'ASSIGN';
    }

    // Standard CRUD operations
    switch (method) {
      case 'POST': return 'CREATE';
      case 'GET': return 'READ';
      case 'PUT':
      case 'PATCH': return 'UPDATE';
      case 'DELETE': return 'DELETE';
      default: return 'READ';
    }
  }

  /**
   * Determine resource type from request data
   */
  determineResourceType(data) {
    if (data.resourceType) return data.resourceType;

    const endpoint = data.endpoint || data.req?.originalUrl || '';

    if (endpoint.includes('/users')) return 'user';
    if (endpoint.includes('/organizations')) return 'organization';
    if (endpoint.includes('/roles')) return 'role';
    if (endpoint.includes('/privileges')) return 'privilege';
    if (endpoint.includes('/auth')) return 'auth';
    if (endpoint.includes('/config')) return 'config';

    return 'unknown';
  }

  /**
   * Sanitize request data to remove sensitive information
   */
  sanitizeRequestData(requestData) {
    if (!requestData) return {};

    const sanitized = JSON.parse(JSON.stringify(requestData));

    // Remove sensitive fields
    const sensitiveFields = [
      'password', 'token', 'access_token', 'refresh_token',
      'secret', 'key', 'private_key', 'api_key'
    ];

    this.removeSensitiveFields(sanitized, sensitiveFields);

    // Mask PII
    if (sanitized.email) {
      sanitized.email = this.maskEmail(sanitized.email);
    }

    if (sanitized.phone_number) {
      sanitized.phone_number = this.maskPhoneNumber(sanitized.phone_number);
    }

    return sanitized;
  }

  /**
   * Sanitize response data
   */
  sanitizeResponseData(responseData) {
    if (!responseData) return {};

    const sanitized = JSON.parse(JSON.stringify(responseData));

    // Remove sensitive fields
    const sensitiveFields = [
      'access_token', 'refresh_token', 'password', 'secret'
    ];

    this.removeSensitiveFields(sanitized, sensitiveFields);

    // Limit response data size
    const maxSize = 10000; // 10KB limit
    const stringified = JSON.stringify(sanitized);
    if (stringified.length > maxSize) {
      return {
        _truncated: true,
        _original_size: stringified.length,
        _preview: stringified.substring(0, 1000) + '...'
      };
    }

    return sanitized;
  }

  /**
   * Extract IP address from request
   */
  extractIpAddress(req) {
    if (!req) return null;

    return req.ip ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
           req.headers['x-real-ip'];
  }

  /**
   * Extract changes for UPDATE operations
   */
  extractChanges(data) {
    if (!data.changes) return {};

    return {
      before: this.sanitizeRequestData(data.changes.before),
      after: this.sanitizeRequestData(data.changes.after),
      fields_changed: data.changes.fields_changed || []
    };
  }

  /**
   * Build comprehensive metadata
   */
  buildMetadata(data) {
    const metadata = {
      identifier_types: data.identifierTypes || {},
      execution_time_ms: data.executionTime,
      session_id: data.sessionId,
      request_id: data.requestId || data.req?.id,
      organization_context: data.organizationContext,
      role_context: data.roleContext,
      compliance_flags: this.determineComplianceFlags(data),
      custom: data.customMetadata || {}
    };

    // Add error details if present
    if (data.error) {
      metadata.error_details = {
        error_code: data.error.code,
        error_message: data.error.message,
        stack_trace: process.env.NODE_ENV === 'development' ? data.error.stack : undefined
      };
    }

    return metadata;
  }

  /**
   * Determine compliance flags
   */
  determineComplianceFlags(data) {
    const flags = [];

    if (data.containsPII) flags.push('PII_ACCESS');
    if (data.isAdminAction) flags.push('ADMIN_ACTION');
    if (data.isPrivilegeEscalation) flags.push('PRIVILEGE_ESCALATION');
    if (data.isBulkOperation) flags.push('BULK_OPERATION');

    return flags;
  }

  /**
   * Determine retention period based on operation type
   */
  determineRetentionPeriod(data) {
    const operationType = this.determineOperationType(data);
    const resourceType = this.determineResourceType(data);

    // Extended retention for critical operations
    if (operationType === 'AUTH' || resourceType === 'privilege') {
      return 3650; // 10 years
    }

    if (operationType === 'DELETE' || data.isAdminAction) {
      return 2555; // 7 years
    }

    return 1825; // 5 years default
  }

  /**
   * Check if operation is critical and needs immediate logging
   */
  isCriticalOperation(data) {
    const operationType = this.determineOperationType(data);
    const resourceType = this.determineResourceType(data);

    return operationType === 'AUTH' ||
           resourceType === 'privilege' ||
           data.isAdminAction ||
           data.isPrivilegeEscalation;
  }

  /**
   * Flush logs to database
   */
  async flushLogs() {
    if (this.isProcessing || this.logQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const logsToProcess = [...this.logQueue];
    this.logQueue = [];

    try {
      const auditConnection = auditDatabase.getConnection();

      if (!auditConnection) {
        logger.warn('Audit database not available, logs will be queued', {
          component: 'audit-logger',
          operation: 'flush_logs_queued',
          metadata: { queued_logs: logsToProcess.length }
        });

        // Re-queue the logs
        this.logQueue.unshift(...logsToProcess);
        return;
      }

      // Get AuditLog model from audit connection
      const AuditLog = auditConnection.model('AuditLog', require('../models/AuditLog').schema);

      // Batch insert for performance
      await AuditLog.insertMany(logsToProcess, { ordered: false });

      logger.info('Audit logs flushed to database', {
        component: 'audit-logger',
        operation: 'flush_logs_success',
        metadata: { logs_count: logsToProcess.length }
      });

    } catch (error) {
      logger.error('Failed to flush audit logs to database', {
        component: 'audit-logger',
        operation: 'flush_logs_error',
        error: error.message,
        metadata: { failed_logs: logsToProcess.length }
      });

      // Re-queue failed logs for retry
      this.logQueue.unshift(...logsToProcess);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Utility methods for data sanitization
   */
  removeSensitiveFields(obj, sensitiveFields) {
    if (typeof obj !== 'object' || obj === null) return;

    for (const key in obj) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        this.removeSensitiveFields(obj[key], sensitiveFields);
      }
    }
  }

  maskEmail(email) {
    if (!email || typeof email !== 'string') return email;
    const [local, domain] = email.split('@');
    if (!domain) return email;

    const maskedLocal = local.length > 2 ?
      local[0] + '*'.repeat(local.length - 2) + local[local.length - 1] :
      local;
    return `${maskedLocal}@${domain}`;
  }

  maskPhoneNumber(phone) {
    if (!phone || typeof phone !== 'string') return phone;
    return phone.replace(/\d(?=\d{4})/g, '*');
  }

  sanitizeForLogging(data) {
    const sanitized = JSON.parse(JSON.stringify(data));
    this.removeSensitiveFields(sanitized, ['password', 'token', 'secret']);
    return sanitized;
  }

  /**
   * Force flush all pending logs (for graceful shutdown)
   */
  async forceFlush() {
    await this.flushLogs();
  }

  /**
   * Get audit logger statistics
   */
  getStats() {
    return {
      enabled: this.isEnabled,
      queue_size: this.logQueue.length,
      batch_size: this.batchSize,
      flush_interval: this.flushInterval,
      is_processing: this.isProcessing
    };
  }
}

// Singleton instance
const auditLogger = new AuditLogger();

module.exports = auditLogger;
