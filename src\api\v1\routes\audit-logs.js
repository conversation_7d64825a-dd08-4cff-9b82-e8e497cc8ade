// src/api/v1/routes/audit-logs.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../../middleware/auth');
const { rbac } = require('../../../middleware/rbac');
const auditController = require('../controllers/auditController');

/**
 * @swagger
 * tags:
 *   - name: AuditLogs
 *     description: Audit log querying and management endpoints
 */

/**
 * @swagger
 * /api/v1/audit-logs:
 *   get:
 *     summary: Query audit logs with filtering and pagination
 *     description: |
 *       Retrieve audit logs with comprehensive filtering options.
 *       Requires audit_log:read permission.
 *
 *       **Available Filters:**
 *       - Date range (start_date, end_date)
 *       - User (user_id, user_email)
 *       - Operation type (operation_type)
 *       - Resource type (resource_type)
 *       - HTTP status (response_status)
 *       - Organization context (organization_id)
 *     tags: [AuditLogs]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *           maximum: 1000
 *         description: Number of records per page
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for filtering (ISO 8601)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for filtering (ISO 8601)
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *         description: Filter by user ID
 *       - in: query
 *         name: user_email
 *         schema:
 *           type: string
 *         description: Filter by user email
 *       - in: query
 *         name: operation_type
 *         schema:
 *           type: string
 *           enum: [CREATE, READ, UPDATE, DELETE, AUTH, ASSIGN, REVOKE]
 *         description: Filter by operation type
 *       - in: query
 *         name: resource_type
 *         schema:
 *           type: string
 *           enum: [user, organization, role, privilege, auth, config]
 *         description: Filter by resource type
 *       - in: query
 *         name: response_status
 *         schema:
 *           type: integer
 *         description: Filter by HTTP response status
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Filter by organization context
 *       - in: query
 *         name: endpoint
 *         schema:
 *           type: string
 *         description: Filter by API endpoint
 *     responses:
 *       200:
 *         description: Audit logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *       400:
 *         description: Invalid query parameters
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Internal server error
 */
router.get('/', authenticate, rbac('audit_log:read'), auditController.queryAuditLogs);

/**
 * @swagger
 * /api/v1/audit-logs/{operation_id}:
 *   get:
 *     summary: Get specific audit log entry by operation ID
 *     tags: [AuditLogs]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: operation_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique operation ID of the audit log entry
 *     responses:
 *       200:
 *         description: Audit log entry retrieved successfully
 *       404:
 *         description: Audit log entry not found
 *       403:
 *         description: Insufficient permissions
 */
router.get('/:operation_id', authenticate, rbac('audit_log:read'), auditController.getAuditLogEntry);

/**
 * @swagger
 * /api/v1/audit-logs/stats/summary:
 *   get:
 *     summary: Get audit log statistics and summary
 *     tags: [AuditLogs]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for statistics (ISO 8601)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for statistics (ISO 8601)
 *     responses:
 *       200:
 *         description: Audit log statistics retrieved successfully
 */
router.get('/stats/summary', authenticate, rbac('audit_log:read'), auditController.getAuditStatistics);

/**
 * @swagger
 * /api/v1/audit-logs/user/{userIdOrEmail}:
 *   get:
 *     summary: Get audit logs for specific user
 *     tags: [AuditLogs]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: path
 *         name: userIdOrEmail
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID or email address
 *     responses:
 *       200:
 *         description: User audit logs retrieved successfully
 */
router.get('/user/:userIdOrEmail', authenticate, rbac('audit_log:read'), auditController.getUserAuditLogs);

module.exports = router;
