// src/config/auth0Config.js
const logger = require('../services/logger');

/**
 * Auth0 Configuration with Multi-Tenant Subdomain Support
 * Handles dynamic Auth0 configuration based on subdomain context
 */
class Auth0Config {
  constructor() {
    this.baseConfig = {
      authRequired: false,
      auth0Logout: true,
      secret: process.env.AUTH0_SECRET,
      clientID: process.env.AUTH0_CLIENT_ID,
      issuerBaseURL: process.env.AUTH0_ISSUER_BASE_URL,
      session: {
        rolling: true,
        rollingDuration: 24 * 60 * 60, // 24 hours
        absoluteDuration: 7 * 24 * 60 * 60 // 7 days
      },
      routes: {
        login: '/auth/login',
        logout: '/auth/logout',
        callback: '/auth/callback',
        postLogoutRedirect: '/'
      }
    };
  }

  /**
   * Get Auth0 configuration for a specific subdomain/organization
   */
  getConfigForSubdomain(subdomain, organization = null) {
    const rootDomain = process.env.ROOT_DOMAIN || 'digimeet.live';
    const baseURL = subdomain && subdomain !== 'app'
      ? `https://${subdomain}.${rootDomain}`
      : `https://${rootDomain}`;

    const config = {
      ...this.baseConfig,
      baseURL: baseURL,
      session: {
        ...this.baseConfig.session,
        name: `auth0_session_${subdomain || 'root'}`,
        cookie: {
          domain: `.${rootDomain}`,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'Lax'
        }
      }
    };

    // Add organization-specific configuration if available
    if (organization) {
      config.authorizationParams = {
        organization: organization._id,
        // Add custom claims for organization context
        'https://digimeet.live/organization': {
          id: organization._id,
          name: organization.name,
          subdomain: organization.subdomain
        }
      };
    }

    logger.debug('Auth0 config generated for subdomain', {
      component: 'auth0-config',
      operation: 'get_config',
      metadata: {
        subdomain,
        baseURL,
        organization_id: organization?._id,
        organization_name: organization?.name
      }
    });

    return config;
  }

  /**
   * Middleware to set Auth0 configuration based on subdomain context
   */
  subdomainConfigMiddleware() {
    return (req, res, next) => {
      try {
        // Get subdomain and organization from subdomain middleware
        const subdomain = req.subdomain?.subdomain || null;
        const organization = req.organization || null;

        // Generate Auth0 config for this subdomain
        const auth0Config = this.getConfigForSubdomain(subdomain, organization);

        // Attach to request for use by Auth0 middleware
        req.auth0Config = auth0Config;

        // Also attach helper methods
        req.getAuth0LoginUrl = (returnTo = '/') => {
          const baseURL = auth0Config.baseURL;
          return `${baseURL}/auth/login?returnTo=${encodeURIComponent(returnTo)}`;
        };

        req.getAuth0LogoutUrl = (returnTo = '/') => {
          const baseURL = auth0Config.baseURL;
          return `${baseURL}/auth/logout?returnTo=${encodeURIComponent(returnTo)}`;
        };

        next();
      } catch (error) {
        logger.error('Auth0 subdomain config middleware error', {
          component: 'auth0-config',
          operation: 'subdomain_config_middleware',
          error: error.message,
          metadata: {
            subdomain: req.subdomain?.subdomain,
            organization_id: req.organization?._id
          }
        });
        next();
      }
    };
  }

  /**
   * Get default Auth0 configuration (fallback)
   */
  getDefaultConfig() {
    return this.getConfigForSubdomain(null, null);
  }

  /**
   * Validate Auth0 environment variables
   */
  validateConfig() {
    const required = [
      'AUTH0_SECRET',
      'AUTH0_CLIENT_ID',
      'AUTH0_ISSUER_BASE_URL'
    ];

    const missing = required.filter(key => !process.env[key]);

    if (missing.length > 0) {
      logger.error('Missing Auth0 environment variables', {
        component: 'auth0-config',
        operation: 'validate_config',
        metadata: {
          missing_variables: missing
        }
      });
      return false;
    }

    logger.info('Auth0 configuration validated successfully', {
      component: 'auth0-config',
      operation: 'validate_config'
    });
    return true;
  }

  /**
   * Create organization-specific Auth0 rules/actions
   * This would be used to customize Auth0 behavior per organization
   */
  getOrganizationRules(organization) {
    return {
      // Custom claims to add to tokens
      customClaims: {
        'https://digimeet.live/organization_id': organization._id,
        'https://digimeet.live/organization_name': organization.name,
        'https://digimeet.live/subdomain': organization.subdomain,
        'https://digimeet.live/branding': organization.branding
      },

      // Redirect rules
      redirectRules: {
        login: `https://${organization.subdomain}.${process.env.ROOT_DOMAIN}/dashboard`,
        logout: `https://${organization.subdomain}.${process.env.ROOT_DOMAIN}/`
      }
    };
  }
}

// Export singleton instance
module.exports = new Auth0Config();
