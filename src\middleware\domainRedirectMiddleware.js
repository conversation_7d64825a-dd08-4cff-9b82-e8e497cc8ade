/**
 * Middleware to handle domain redirects (www to non-www or vice versa)
 */
module.exports = function domainRedirectMiddleware(options = {}) {
  const { removeWww = true } = options;
  
  return (req, res, next) => {
    // Skip for OPTIONS requests
    if (req.method === 'OPTIONS') {
      return next();
    }
    
    const host = req.headers.host;
    
    if (removeWww && host.startsWith('www.')) {
      // Redirect www to non-www
      const newHost = host.replace(/^www\./, '');
      return res.redirect(301, `${req.protocol}://${newHost}${req.originalUrl}`);
    }
    if (!removeWww && !host.startsWith('www.') && !host.includes('.') && !host.includes('localhost')) {
      // Redirect non-www to www (skip for subdomains and localhost)
      return res.redirect(301, `${req.protocol}://www.${host}${req.originalUrl}`);
    }
    
    next();
  };
};