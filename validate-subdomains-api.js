#!/usr/bin/env node

/**
 * Validation Script for Available Subdomains API
 * 
 * This script validates that the new subdomains API endpoint is working correctly
 * by checking the route registration and controller method existence.
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 Validating PUBLIC Subdomains API Implementation...\n');

// Check if public controller method exists
const controllerPath = path.join(__dirname, 'src/api/v1/controllers/organizationController.js');
if (fs.existsSync(controllerPath)) {
  const controllerContent = fs.readFileSync(controllerPath, 'utf8');

  if (controllerContent.includes('getPublicSubdomains')) {
    console.log('✅ Controller method `getPublicSubdomains` found');
  } else {
    console.log('❌ Controller method `getPublicSubdomains` NOT found');
  }

  if (controllerContent.includes('Organization.find({})')) {
    console.log('✅ Public database query implementation found');
  } else {
    console.log('❌ Public database query implementation NOT found');
  }

  if (controllerContent.includes('.select(\'subdomain\')')) {
    console.log('✅ Subdomain selection logic found');
  } else {
    console.log('❌ Subdomain selection logic NOT found');
  }
} else {
  console.log('❌ Organization controller file not found');
}

// Check if public routes exist
const subdomainRoutePath = path.join(__dirname, 'src/api/v1/routes/subdomains.js');
if (fs.existsSync(subdomainRoutePath)) {
  console.log('✅ Public subdomains route file found');

  const subdomainRouteContent = fs.readFileSync(subdomainRoutePath, 'utf8');

  if (subdomainRouteContent.includes("router.get('/', organizationController.getPublicSubdomains)")) {
    console.log('✅ Public route `/api/v1/subdomains` found');
  } else {
    console.log('❌ Public route `/api/v1/subdomains` NOT found');
  }

  if (!subdomainRouteContent.includes('authenticate')) {
    console.log('✅ No authentication middleware (PUBLIC endpoint)');
  } else {
    console.log('❌ Authentication middleware found (should be PUBLIC)');
  }
} else {
  console.log('❌ Public subdomains route file not found');
}

// Check that alternative public route is removed
const orgRoutePath = path.join(__dirname, 'src/api/v1/routes/organizations.js');
if (fs.existsSync(orgRoutePath)) {
  const orgRouteContent = fs.readFileSync(orgRoutePath, 'utf8');

  if (!orgRouteContent.includes("router.get('/subdomains/public'")) {
    console.log('✅ Alternative public route removed (clean single endpoint)');
  } else {
    console.log('❌ Alternative public route still exists (should be removed)');
  }

  // Check that old authenticated route is removed
  if (!orgRouteContent.includes("router.get('/subdomains', authenticate")) {
    console.log('✅ Old authenticated route removed');
  } else {
    console.log('❌ Old authenticated route still exists');
  }
} else {
  console.log('❌ Organizations route file not found');
}

// Check main app registration
const appPath = path.join(__dirname, 'src/index.js');
if (fs.existsSync(appPath)) {
  const appContent = fs.readFileSync(appPath, 'utf8');

  if (appContent.includes("app.use('/api/v1/subdomains', subdomainRoutes)")) {
    console.log('✅ Public subdomains route registered in main app');
  } else {
    console.log('❌ Public subdomains route NOT registered in main app');
  }
} else {
  console.log('❌ Main app file not found');
}

console.log('\n📋 SINGLE PUBLIC API Endpoint Summary:');
console.log('   Endpoint: GET /api/v1/subdomains');
console.log('   Authentication: NONE (Completely Public)');
console.log('   Authorization: NONE (No RBAC)');
console.log('   Features: Simple subdomain array, No filtering, No redundancy');

console.log('\n🧪 Test the PUBLIC API:');
console.log('   1. No authentication needed!');
console.log('   2. Call: GET http://localhost:3000/api/v1/subdomains');
console.log('   3. No headers required (even Content-Type is optional)');
console.log('   4. Single, clean endpoint - no alternatives needed');

console.log('\n📚 Documentation:');
console.log('   Swagger UI: http://localhost:3000/api-docs-ui');
console.log('   Look for "Subdomains" section');

console.log('\n✅ Validation Complete!');
