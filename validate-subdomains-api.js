#!/usr/bin/env node

/**
 * Validation Script for Available Subdomains API
 * 
 * This script validates that the new subdomains API endpoint is working correctly
 * by checking the route registration and controller method existence.
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 Validating Available Subdomains API Implementation...\n');

// Check if controller method exists
const controllerPath = path.join(__dirname, 'src/api/v1/controllers/organizationController.js');
if (fs.existsSync(controllerPath)) {
  const controllerContent = fs.readFileSync(controllerPath, 'utf8');
  
  if (controllerContent.includes('getAvailableSubdomains')) {
    console.log('✅ Controller method `getAvailableSubdomains` found');
  } else {
    console.log('❌ Controller method `getAvailableSubdomains` NOT found');
  }
  
  if (controllerContent.includes('Organization.find(filter)')) {
    console.log('✅ Database query implementation found');
  } else {
    console.log('❌ Database query implementation NOT found');
  }
  
  if (controllerContent.includes('pagination')) {
    console.log('✅ Pagination logic found');
  } else {
    console.log('❌ Pagination logic NOT found');
  }
} else {
  console.log('❌ Organization controller file not found');
}

// Check if route exists
const routePath = path.join(__dirname, 'src/api/v1/routes/organizations.js');
if (fs.existsSync(routePath)) {
  const routeContent = fs.readFileSync(routePath, 'utf8');
  
  if (routeContent.includes("router.get('/subdomains'")) {
    console.log('✅ Route `/subdomains` found');
  } else {
    console.log('❌ Route `/subdomains` NOT found');
  }
  
  if (routeContent.includes('getAvailableSubdomains')) {
    console.log('✅ Route handler `getAvailableSubdomains` found');
  } else {
    console.log('❌ Route handler `getAvailableSubdomains` NOT found');
  }
  
  if (routeContent.includes("rbac('org_object:read'")) {
    console.log('✅ RBAC authorization found');
  } else {
    console.log('❌ RBAC authorization NOT found');
  }
  
  if (routeContent.includes('authenticate')) {
    console.log('✅ Authentication middleware found');
  } else {
    console.log('❌ Authentication middleware NOT found');
  }
} else {
  console.log('❌ Organizations route file not found');
}

// Check Swagger documentation
if (fs.existsSync(routePath)) {
  const routeContent = fs.readFileSync(routePath, 'utf8');
  
  if (routeContent.includes('/api/v1/organizations/subdomains:')) {
    console.log('✅ Swagger documentation found');
  } else {
    console.log('❌ Swagger documentation NOT found');
  }
  
  if (routeContent.includes('Get all available organization subdomains')) {
    console.log('✅ Swagger summary found');
  } else {
    console.log('❌ Swagger summary NOT found');
  }
}

console.log('\n📋 API Endpoint Summary:');
console.log('   Endpoint: GET /api/v1/organizations/subdomains');
console.log('   Authentication: Required (JWT Token)');
console.log('   Authorization: org_object:read (system-wide)');
console.log('   Features: Pagination, Filtering, Search, Sorting');

console.log('\n🧪 Test the API:');
console.log('   1. Get JWT token from login endpoint');
console.log('   2. Call: GET http://localhost:3000/api/v1/organizations/subdomains');
console.log('   3. Include Authorization header: Bearer <your-token>');

console.log('\n📚 Documentation:');
console.log('   Swagger UI: http://localhost:3000/api-docs-ui');
console.log('   Look for "Organizations" section');

console.log('\n✅ Validation Complete!');
