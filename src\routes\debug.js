// src/routes/debug.js
const express = require('express');
const router = express.Router();

/**
 * @swagger
 * /debug/subdomain:
 *   get:
 *     tags: [Health]
 *     summary: Subdomain Debug Information
 *     description: |
 *       Returns detailed subdomain extraction and organization resolution information.
 *       Use this endpoint to debug subdomain functionality.
 *     security: []
 *     responses:
 *       200:
 *         description: Subdomain debug information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 subdomain_info:
 *                   type: object
 *                   description: Detailed subdomain extraction information
 *                 organization_info:
 *                   type: object
 *                   description: Organization resolution details
 *                 request_headers:
 *                   type: object
 *                   description: Relevant request headers
 */
router.get('/subdomain', (req, res) => {
  try {
    const response = {
      timestamp: new Date().toISOString(),
      subdomain_info: req.subdomain || null,
      organization_info: {
        organization: req.organization || null,
        context: req.organizationContext || null
      },
      request_headers: {
        host: req.get('host'),
        origin: req.get('origin'),
        'x-forwarded-proto': req.get('x-forwarded-proto'),
        'x-forwarded-host': req.get('x-forwarded-host'),
        'user-agent': req.get('user-agent')
      },
      environment_config: {
        subdomain_extraction_enabled: process.env.SUBDOMAIN_EXTRACTION_ENABLED,
        subdomain_organization_mapping: process.env.SUBDOMAIN_ORGANIZATION_MAPPING,
        root_domain: process.env.ROOT_DOMAIN,
        default_organization_subdomain: process.env.DEFAULT_ORGANIZATION_SUBDOMAIN
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Debug endpoint error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /debug/env:
 *   get:
 *     tags: [Health]
 *     summary: Environment Variables Debug
 *     description: |
 *       Returns environment variables related to subdomain functionality.
 *       Use this endpoint to debug environment configuration.
 *     security: []
 *     responses:
 *       200:
 *         description: Environment variables information
 */
router.get('/env', (req, res) => {
  try {
    const response = {
      timestamp: new Date().toISOString(),
      subdomain_env_vars: {
        SUBDOMAIN_EXTRACTION_ENABLED: process.env.SUBDOMAIN_EXTRACTION_ENABLED,
        SUBDOMAIN_ORGANIZATION_MAPPING: process.env.SUBDOMAIN_ORGANIZATION_MAPPING,
        ROOT_DOMAIN: process.env.ROOT_DOMAIN,
        DEFAULT_ORGANIZATION_SUBDOMAIN: process.env.DEFAULT_ORGANIZATION_SUBDOMAIN,
        ENABLE_SUBDOMAIN_ROUTING: process.env.ENABLE_SUBDOMAIN_ROUTING
      },
      cors_env_vars: {
        CORS_WILDCARD_ENABLED: process.env.CORS_WILDCARD_ENABLED,
        CORS_SUBDOMAIN_PATTERN: process.env.CORS_SUBDOMAIN_PATTERN,
        CORS_ORIGINS: process.env.CORS_ORIGINS
      },
      domain_env_vars: {
        PRODUCTION_SERVER_URL: process.env.PRODUCTION_SERVER_URL,
        WILDCARD_DOMAIN: process.env.WILDCARD_DOMAIN,
        SERVER_URL: process.env.SERVER_URL
      },
      node_env: process.env.NODE_ENV,
      vercel_env: process.env.VERCEL_ENV || 'not-vercel'
    };

    res.json(response);
  } catch (error) {
    console.error('Environment debug endpoint error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /debug/csp:
 *   get:
 *     tags: [Health]
 *     summary: Content Security Policy Debug
 *     description: |
 *       Returns the current Content Security Policy configuration.
 *       Use this endpoint to debug CSP issues.
 *     security: []
 *     responses:
 *       200:
 *         description: CSP configuration information
 */
router.get('/csp', (req, res) => {
  try {
    const productionConfig = require('../config/productionConfig');

    const response = {
      timestamp: new Date().toISOString(),
      csp_header: productionConfig.getCSPHeader(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        ROOT_DOMAIN: process.env.ROOT_DOMAIN,
        AUTH0_ISSUER_BASE_URL: process.env.AUTH0_ISSUER_BASE_URL ? 'configured' : 'not configured'
      },
      request_info: {
        host: req.get('host'),
        origin: req.get('origin'),
        user_agent: req.get('user-agent'),
        referer: req.get('referer')
      }
    };

    res.json(response);
  } catch (error) {
    console.error('CSP debug endpoint error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
