#!/usr/bin/env node

/**
 * Debug script for add-user endpoint
 * This script helps debug the add-user to organization endpoint
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Organization = require('./src/models/Organization');
const User = require('./src/models/User');
const RBACRole = require('./src/models/RBACRole');

async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function debugAddUser() {
  console.log('🔍 Debugging add-user endpoint...\n');

  try {
    // Test data from your request
    const orgId = '68348c6da5dfc35a3f39612b';
    const userEmail = '<EMAIL>';
    const roleId = '68349666aa3afe6946461a1a';

    console.log('📋 Test Parameters:');
    console.log(`   Organization ID: ${orgId}`);
    console.log(`   User Email: ${userEmail}`);
    console.log(`   Role ID: ${roleId}\n`);

    // Check if organization exists
    console.log('🏢 Checking organization...');
    const org = await Organization.findById(orgId);
    if (!org) {
      console.log('❌ Organization not found');
      return;
    }
    console.log(`✅ Organization found: ${org.name} (${org.subdomain})`);
    console.log(`   Status: ${org.status}`);
    console.log(`   Members count: ${org.members?.length || 0}\n`);

    // Check if user exists
    console.log('👤 Checking user...');
    const user = await User.findOne({ email: userEmail.toLowerCase() });
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    console.log(`✅ User found: ${user.name} (${user.email})`);
    console.log(`   Status: ${user.status}`);
    console.log(`   Current roles: ${user.roles?.length || 0}\n`);

    // Check if role exists
    console.log('🎭 Checking role...');
    const role = await RBACRole.findById(roleId);
    if (!role) {
      console.log('❌ Role not found');
      return;
    }
    console.log(`✅ Role found: ${role.name} (${role.displayName})`);
    console.log(`   Scope: ${role.scope}`);
    console.log(`   Organization: ${role.organization || 'N/A'}`);
    console.log(`   Active: ${role.metadata?.isActive}\n`);

    // Check if role is valid for this organization
    if (role.scope === 'organization' && role.organization?.toString() !== orgId) {
      console.log('⚠️  Role organization mismatch!');
      console.log(`   Role org: ${role.organization}`);
      console.log(`   Target org: ${orgId}\n`);
    }

    // Check if user is already in organization
    console.log('🔄 Checking existing membership...');
    const existingRole = user.roles?.find(r => r.org?.toString() === orgId);
    if (existingRole) {
      console.log('⚠️  User already has role in this organization');
      console.log(`   Current role: ${existingRole.role}`);
      console.log(`   Assigned at: ${existingRole.assignedAt}\n`);
    } else {
      console.log('✅ User not currently in organization\n');
    }

    // Check organization members structure
    console.log('👥 Organization members structure:');
    if (org.members && org.members.length > 0) {
      org.members.forEach((member, index) => {
        console.log(`   ${index + 1}. User: ${member.user}`);
        console.log(`      Role: ${member.role || 'N/A'}`);
        console.log(`      Status: ${member.status || 'N/A'}`);
        console.log(`      Joined: ${member.joinedAt || 'N/A'}\n`);
      });
    } else {
      console.log('   No members found\n');
    }

    console.log('🎯 Summary:');
    console.log(`   ✅ Organization exists and is ${org.status}`);
    console.log(`   ✅ User exists and is ${user.status}`);
    console.log(`   ✅ Role exists and is ${role.metadata?.isActive ? 'active' : 'inactive'}`);
    console.log(`   ${existingRole ? '⚠️' : '✅'} User ${existingRole ? 'already has' : 'does not have'} role in org`);

    if (role.scope === 'organization' && role.organization?.toString() !== orgId) {
      console.log('   ❌ Role-organization mismatch detected');
    } else {
      console.log('   ✅ Role is valid for this organization');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

async function main() {
  await connectDB();
  await debugAddUser();
  await mongoose.connection.close();
  console.log('\n👋 Debug completed');
}

if (require.main === module) {
  main();
}

module.exports = { debugAddUser };
