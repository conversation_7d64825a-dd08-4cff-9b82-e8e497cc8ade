# Pending Organizations Endpoint

## Overview

This document describes the new API endpoint that allows users with `god_super_user` privileges to retrieve all organizations that have a status of "pending" and are awaiting approval.

## Endpoint Details

### **GET** `/api/v1/organizations/pending`

Retrieve a paginated list of organizations with "pending" status that are awaiting approval.

#### **Authentication & Authorization**
- **Authentication**: Required (Bear<PERSON> token)
- **Authorization**: God Super User privileges only
- **RBAC Permission**: `org_object:read` with system-wide access

#### **Query Parameters**

| Parameter | Type | Default | Description | Validation |
|-----------|------|---------|-------------|------------|
| `page` | integer | 1 | Page number for pagination | min: 1 |
| `limit` | integer | 10 | Number of organizations per page | min: 1, max: 100 |

#### **Response Format**

**Success Response (200)**
```json
{
  "success": true,
  "data": {
    "organizations": [
      {
        "_id": "6838bfd6278ccc88e4179049",
        "name": "Acme Corporation",
        "subdomain": "acme-corp",
        "branding": {
          "logoUrl": "https://acme.com/logo.png",
          "primaryColor": "#336699"
        },
        "createdAt": "2025-05-29T20:13:10.956Z",
        "createdBy": {
          "_id": "68330577e2576bff416a2ae7",
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "memberCount": 1
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "metadata": {
    "retrieved_at": "2025-05-29T20:20:52.975Z",
    "duration_ms": 57,
    "privilege_level": "god_super_user"
  }
}
```

**Error Responses**

| Status | Code | Description |
|--------|------|-------------|
| 401 | `AUTHENTICATION_REQUIRED` | No valid authentication token |
| 403 | `INSUFFICIENT_PRIVILEGES` | User is not a God Super User |
| 500 | `PENDING_ORGS_FETCH_ERROR` | Internal server error |

## Implementation Details

### **Controller Method**
- **File**: `src/api/v1/controllers/organizationController.js`
- **Method**: `getPendingOrganizations(req, res)`

### **Route Definition**
- **File**: `src/api/v1/routes/organizations.js`
- **Route**: `GET /pending`
- **Middleware**: `authenticate`, `rbac('org_object:read')`

### **Key Features**

#### 🔒 **Security**
- **Double privilege check**: RBAC middleware + controller-level god_super_user verification
- **Audit logging**: All access attempts are logged for security monitoring
- **IP tracking**: Request IP and user agent are recorded

#### 📊 **Data Enhancement**
- **Creator information**: Populated user details for organization creators
- **Member count**: Calculated member count for each organization
- **Branding data**: Organization branding information included

#### 📄 **Pagination**
- **Configurable limits**: 1-100 organizations per page
- **Navigation helpers**: `hasNext`, `hasPrev` flags
- **Total count**: Complete pagination metadata

#### 📝 **Logging & Auditing**
- **Operation logging**: Standard application logging
- **Audit trail**: Detailed audit logs for compliance
- **Performance tracking**: Response time monitoring

## Usage Examples

### **cURL Example**
```bash
# Get first page of pending organizations
curl -H "Authorization: Bearer <god_super_user_token>" \
     "https://api.yourdomain.com/api/v1/organizations/pending"

# Get specific page with custom limit
curl -H "Authorization: Bearer <god_super_user_token>" \
     "https://api.yourdomain.com/api/v1/organizations/pending?page=2&limit=5"
```

### **JavaScript Example**
```javascript
const response = await fetch('/api/v1/organizations/pending?page=1&limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();

if (data.success) {
  console.log(`Found ${data.data.pagination.total} pending organizations`);
  data.data.organizations.forEach(org => {
    console.log(`${org.name} (${org.subdomain}) - Created by ${org.createdBy.email}`);
  });
}
```

## Administrative Workflow

### **Typical Use Case**
1. **God Super User** logs into admin dashboard
2. **Accesses pending organizations** via this endpoint
3. **Reviews organization details** and creator information
4. **Makes approval decisions** using existing approval endpoints
5. **Tracks approval history** through audit logs

### **Integration Points**
- **Admin Dashboard**: Display pending organizations list
- **Approval Workflow**: Link to organization approval endpoints
- **Audit System**: Track all administrative access
- **Notification System**: Alert on new pending organizations

## Security Considerations

### **Privilege Escalation Prevention**
- **Dual verification**: Both RBAC and controller-level checks
- **Immutable privileges**: God Super User status cannot be modified via API
- **Session validation**: Token validity checked on every request

### **Data Protection**
- **Minimal exposure**: Only necessary organization data returned
- **No sensitive data**: User passwords, tokens, etc. not included
- **Audit compliance**: All access logged for security review

### **Rate Limiting**
- **Standard limits**: Subject to general API rate limiting
- **Pagination caps**: Maximum 100 organizations per request
- **Resource protection**: Prevents excessive database queries

## Testing

### **Test Script**
Run the provided test script to verify functionality:
```bash
node src/scripts/testPendingOrgsEndpoint.js
```

### **Test Cases**
1. **God Super User Access**: ✅ Should return pending organizations
2. **Regular User Access**: ❌ Should return 403 Forbidden
3. **Pagination**: ✅ Should respect page/limit parameters
4. **Empty Results**: ✅ Should handle no pending organizations gracefully
5. **Error Handling**: ✅ Should handle database errors properly

### **Manual Testing**
1. **Create test organizations** with "pending" status
2. **Test with god_super_user token** - should succeed
3. **Test with regular user token** - should fail with 403
4. **Test pagination** with different page/limit values
5. **Verify audit logs** are created for each access

## Monitoring & Maintenance

### **Key Metrics**
- **Response time**: Monitor endpoint performance
- **Access frequency**: Track god_super_user usage patterns
- **Error rates**: Monitor 403/500 error frequencies
- **Pending org growth**: Track pending organization accumulation

### **Alerts**
- **High pending count**: Alert when pending organizations exceed threshold
- **Unauthorized access**: Alert on 403 errors from this endpoint
- **Performance degradation**: Alert on slow response times
- **Audit anomalies**: Alert on unusual access patterns

### **Maintenance Tasks**
- **Regular cleanup**: Archive old audit logs
- **Performance optimization**: Monitor and optimize database queries
- **Security review**: Periodic review of access patterns
- **Documentation updates**: Keep API docs current

## Future Enhancements

### **Potential Improvements**
1. **Filtering**: Add filters by creation date, creator, etc.
2. **Sorting**: Allow sorting by different fields
3. **Bulk operations**: Enable bulk approval/rejection
4. **Export functionality**: CSV/Excel export of pending organizations
5. **Real-time updates**: WebSocket notifications for new pending orgs

### **Integration Opportunities**
1. **Email notifications**: Notify god_super_users of new pending orgs
2. **Dashboard widgets**: Real-time pending count displays
3. **Approval workflows**: Integration with approval management systems
4. **Analytics**: Detailed reporting on organization approval patterns
