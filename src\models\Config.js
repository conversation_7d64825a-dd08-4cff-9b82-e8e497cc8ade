//model/Config.js
const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     ConfigInput:
 *       type: object
 *       properties:
 *         smtp:
 *           type: object
 *           required:
 *             - user
 *             - pass
 *             - fromAddress
 *           properties:
 *             user:
 *               type: string
 *               description: SMTP username/email
 *               example: "<EMAIL>"
 *             pass:
 *               type: string
 *               description: SMTP password or app-specific password
 *               example: "app-password-or-oauth2-token"
 *             fromAddress:
 *               type: string
 *               description: Email sender address
 *               example: "Your App <<EMAIL>>"
 *             cc_users:
 *               type: array
 *               items:
 *                 type: string
 *               description: List of CC email addresses (optional)
 *               example: ["<EMAIL>", "<EMAIL>"]
 *     ConfigResponse:
 *       type: object
 *       properties:
 *         smtp:
 *           type: object
 *           properties:
 *             user:
 *               type: string
 *               description: SMTP username/email
 *             pass:
 *               type: string
 *               description: SMTP password (masked for security)
 *             fromAddress:
 *               type: string
 *               description: Email sender address
 *             cc_users:
 *               type: array
 *               items:
 *                 type: string
 *               description: List of CC email addresses
 *             updatedAt:
 *               type: string
 *               format: date-time
 *               description: Last update timestamp
 */
const ConfigSchema = new mongoose.Schema({
  smtp: {
    user:  { type: String, required: true },
    pass:  { type: String, required: true },
    cc_users: {type:[String], default: []},
    fromAddress: { type: String, required: true },
    updatedAt:   { type: Date, default: Date.now }
  }
});

module.exports = mongoose.model('Config', ConfigSchema);
