{
  "description": "Test cases for the PUBLIC Subdomains API (No Authentication Required)",
  "endpoints": [
    {
      "name": "Single Public Endpoint",
      "url": "GET /api/v1/subdomains",
      "description": "The one and only public endpoint for accessing all organization subdomains"
    }
  ],
  "authentication": "NONE - Completely public endpoints",
  "authorization": "NONE - No RBAC or permissions required",
  "testCases": [
    {
      "name": "Get all subdomains (PUBLIC - No Auth)",
      "method": "GET",
      "url": "/api/v1/subdomains",
      "headers": {
        "Content-Type": "application/json"
      },
      "description": "No authentication headers required - completely public",
      "expectedResponse": {
        "success": true,
        "data": {
          "subdomains": [
            "acme-corp",
            "beta-company", 
            "demo-site",
            "test-org"
          ],
          "total": 4,
          "timestamp": "2025-06-04T12:30:00.000Z"
        }
      }
    },

  ],
  "curlExamples": [
    {
      "description": "Public request (no auth headers required)",
      "command": "curl -X GET 'http://localhost:3000/api/v1/subdomains' -H 'Content-Type: application/json'"
    },
    {
      "description": "Production example",
      "command": "curl -X GET 'https://your-domain.com/api/v1/subdomains' -H 'Content-Type: application/json'"
    },
    {
      "description": "Minimal request (even Content-Type is optional)",
      "command": "curl -X GET 'http://localhost:3000/api/v1/subdomains'"
    }
  ],
  "javascriptExamples": [
    {
      "description": "Fetch API example",
      "code": "fetch('http://localhost:3000/api/v1/subdomains')\n  .then(response => response.json())\n  .then(data => {\n    console.log('Subdomains:', data.data.subdomains);\n    console.log('Total:', data.data.total);\n  })\n  .catch(error => console.error('Error:', error));"
    },
    {
      "description": "Axios example",
      "code": "import axios from 'axios';\n\nconst getSubdomains = async () => {\n  try {\n    const response = await axios.get('/api/v1/subdomains');\n    return response.data.data.subdomains;\n  } catch (error) {\n    console.error('Error fetching subdomains:', error);\n    return [];\n  }\n};"
    }
  ],
  "responseFormat": {
    "description": "Simple, clean response format",
    "structure": {
      "success": "boolean - Always true for successful requests",
      "data": {
        "subdomains": "array of strings - All subdomain names alphabetically sorted",
        "total": "integer - Total count of subdomains",
        "timestamp": "string - ISO timestamp when data was retrieved"
      }
    },
    "example": {
      "success": true,
      "data": {
        "subdomains": ["acme-corp", "beta-company", "demo-site", "test-org"],
        "total": 4,
        "timestamp": "2025-06-04T12:30:00.000Z"
      }
    }
  },
  "useCases": [
    {
      "name": "Subdomain Validation",
      "description": "Check if a subdomain already exists before creating new organization",
      "example": "const subdomains = await fetch('/api/v1/subdomains').then(r => r.json());\nconst exists = subdomains.data.subdomains.includes('new-subdomain');"
    },
    {
      "name": "Public Directory",
      "description": "Display all available subdomains in a public directory",
      "example": "Perfect for public-facing subdomain listings or organization discovery"
    },
    {
      "name": "Frontend Integration",
      "description": "Populate dropdowns or autocomplete without authentication",
      "example": "Load subdomain options in forms without requiring user login"
    },
    {
      "name": "External Integration",
      "description": "Allow external systems to access subdomain list without API keys",
      "example": "Third-party services can validate subdomains without authentication"
    }
  ],
  "features": [
    "✅ No Authentication Required",
    "✅ No Authorization Required", 
    "✅ No Query Parameters",
    "✅ No Filtering or Pagination",
    "✅ Returns ALL Subdomains",
    "✅ Status Independent (active, pending, inactive, blocked)",
    "✅ Alphabetically Sorted",
    "✅ Simple Response Format",
    "✅ Fast and Lightweight",
    "✅ Perfect for Public Consumption"
  ],
  "notes": [
    "This endpoint bypasses ALL authentication and authorization middleware",
    "Returns subdomains from organizations regardless of their status",
    "Response is cached-friendly due to simple structure",
    "Perfect for public APIs and external integrations",
    "No rate limiting applied (uses global rate limiting only)",
    "Logs requests for monitoring but requires no user context"
  ]
}
