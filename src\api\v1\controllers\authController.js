//src/api/v1/controllers/authController.js
require('dotenv').config();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const AuthBridge = require('../../../models/AuthBridge');
const User = require('../../../models/User');
const admin = require('../../../config/firebase');
const { sendResetPasswordEmail } = require('../../../utils/mailer');
const logger = require('../../../services/logger');
const TokenBlacklist = require('../models/tokenBlacklist');
const UserSession = require('../models/userSession');
const { extractToken } = require('../../../utils/tokenUtils');
const crypto = require('crypto');
const mongoose = require('mongoose');

/**
 * Authentication Controller
 * Handles all authentication-related business logic
 */
class AuthController {

  /**
   * Custom login with email/phone and password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async login(req, res) {
    const { identifier, password } = req.body;

    if (!identifier || !password) {
      return res.status(400).json({ 
        error: 'identifier_and_password_required',
        message: 'Both identifier and password are required'
      });
    }

    try {
      // Find in AuthBridge (not User)
      const authUser = await AuthBridge.findOne({
        $or: [{ email: identifier }, { phone_number: identifier }]
      });

      if (!authUser)
        return res.status(404).json({ error: 'User not found' });

      // Use AuthBridge _id as JWT_UID
      if (!authUser._id) {
        return res.status(403).json({
          error: 'Please use a different authentication method (e.g., Web/Firebase Login).'
        });
      }

      if (!await bcrypt.compare(password, authUser.password))
        return res.status(400).json({ error: 'Invalid password' });

      // Fetch profile info from User with populated defaultOrganization
      const user = await User.findOne({ firebase_uid: authUser.firebase_uid })
        .populate({
          path: 'defaultOrganization',
          select: '_id name subdomain' // Include the fields you want
        });

      if (!user)
        return res.status(404).json({ error: 'User not found' });

      if (user.status !== 'active') {
        return res.status(403).json({ error: `User account is ${user.status}. Access denied.` });
      }

      const idToken = jwt.sign({ sub: authUser._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
      const refreshToken = jwt.sign({ sub: authUser._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });

      res.json({
        access_token: idToken,
        refresh_token: refreshToken,
        expires_in: 3600,
        token_type: "bearer",
        message: 'Login successful',
        user: {
          _id: user?._id,
          JWT_UID: authUser._id,
          email: user?.email,
          name: user?.name,
          phone_number: user?.phone_number,
          status: user?.status,
          type: user?.type,
          defaultOrganization: user?.defaultOrganization ? {
            _id: user.defaultOrganization._id,
            name: user.defaultOrganization.name,
            subdomain: user.defaultOrganization.subdomain
          } : null
        }
      });
    } catch (err) {
      logger.error('Custom login failed', err, {
        component: 'auth-controller',
        operation: 'login',
        metadata: { identifier }
      });
      res.status(500).json({ error: 'Custom login failed', details: err.message });
    }
  }

  /**
   * Firebase login with ID token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async firebaseLogin(req, res) {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({ error: 'idToken is required' });
    }

    try {
      const decoded = await admin.auth().verifyIdToken(idToken);
      const firebaseUid = decoded.uid;

      let user = await User.findOne({ firebase_uid: firebaseUid });

      if (!user) {
        // Auto-create user from Firebase data
        user = await new User({
          firebase_uid: firebaseUid,
          email: decoded.email,
          name: decoded.name || decoded.email?.split('@')[0],
          phone_number: decoded.phone_number,
          status: 'active'
        }).save();

        logger.info('Auto-created user from Firebase login', {
          component: 'auth-controller',
          operation: 'firebase_login_auto_create',
          metadata: { firebase_uid: firebaseUid, email: decoded.email }
        });
      }

      if (user.status !== 'active') {
        return res.status(403).json({ error: `User account is ${user.status}. Access denied.` });
      }

      // Create custom JWT for consistency
      const customToken = jwt.sign({ sub: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
      const refreshToken = jwt.sign({ sub: user._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });

      res.json({
        access_token: customToken,
        refresh_token: refreshToken,
        expires_in: 3600,
        token_type: "bearer",
        message: 'Firebase login successful',
        user: {
          _id: user._id,
          firebase_uid: user.firebase_uid,
          email: user.email,
          name: user.name,
          phone_number: user.phone_number,
          status: user.status
        }
      });
    } catch (err) {
      logger.error('Firebase login failed', err, {
        component: 'auth-controller',
        operation: 'firebase_login'
      });
      res.status(401).json({ error: 'Invalid Firebase token', details: err.message });
    }
  }

  /**
   * Send password reset email
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async forgotPassword(req, res) {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    try {
      const user = await User.findOne({ email });
      if (!user) {
        // Don't reveal if email exists for security
        return res.json({ message: 'If the email exists, a reset link has been sent.' });
      }

      // Generate reset token
      const resetToken = jwt.sign(
        { userId: user._id, email: user.email },
        process.env.JWT_SECRET,
        { expiresIn: '1h' }
      );

      const resetUrl = `${process.env.SERVER_URL}/reset-password?token=${resetToken}`;

      await sendResetPasswordEmail(email, user.name, resetUrl);

      logger.info('Password reset email sent', {
        component: 'auth-controller',
        operation: 'forgot_password',
        metadata: { email, user_id: user._id }
      });

      res.json({ message: 'If the email exists, a reset link has been sent.' });
    } catch (err) {
      logger.error('Forgot password failed', err, {
        component: 'auth-controller',
        operation: 'forgot_password',
        metadata: { email }
      });
      res.status(500).json({ message: 'Failed to send reset email', error: err.message });
    }
  }

  /**
   * Reset password with token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async resetPassword(req, res) {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({ message: 'Token and new password are required' });
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Update password in AuthBridge
      const authUser = await AuthBridge.findOne({ firebase_uid: user.firebase_uid });
      if (authUser) {
        authUser.password = await bcrypt.hash(newPassword, 10);
        await authUser.save();
      }

      // Update Firebase password if possible
      try {
        await admin.auth().updateUser(user.firebase_uid, { password: newPassword });
      } catch (firebaseErr) {
        logger.warn('Failed to update Firebase password', firebaseErr, {
          component: 'auth-controller',
          operation: 'reset_password_firebase_update',
          metadata: { user_id: user._id }
        });
      }

      logger.info('Password reset successful', {
        component: 'auth-controller',
        operation: 'reset_password',
        metadata: { user_id: user._id, email: user.email }
      });

      res.json({ message: 'Password reset successful' });
    } catch (err) {
      logger.error('Password reset failed', err, {
        component: 'auth-controller',
        operation: 'reset_password'
      });
      res.status(400).json({ message: 'Invalid or expired token', error: err.message });
    }
  }

  /**
   * Refresh access token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async refreshToken(req, res) {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(400).json({ error: 'Refresh token is required' });
    }

    try {
      const decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET);
      const user = await User.findById(decoded.sub);

      if (!user || user.status !== 'active') {
        return res.status(401).json({ error: 'Invalid refresh token or inactive user' });
      }

      const newAccessToken = jwt.sign({ sub: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });

      res.json({
        access_token: newAccessToken,
        expires_in: 3600,
        token_type: "bearer"
      });
    } catch (err) {
      logger.error('Token refresh failed', err, {
        component: 'auth-controller',
        operation: 'refresh_token'
      });
      res.status(401).json({ error: 'Invalid refresh token', details: err.message });
    }
  }

  /**
   * Logout user and invalidate JWT token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async logout(req, res) {
    try {
      const token = extractToken(req);
      
      if (!token) {
        return res.status(401).json({
          success: false,
          message: 'No token provided'
        });
      }

      const userId = req.user.id;
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // Get client information
      const clientInfo = {
        userAgent: req.get('user-agent'),
        ipAddress: req.ip,
        location: req.get('x-forwarded-for') || req.connection.remoteAddress
      };

      // Start transaction
      const session = await mongoose.startSession();
      await session.startTransaction();

      try {
        // Calculate token expiration
        const decoded = jwt.decode(token);
        const expiresAt = decoded?.exp 
          ? new Date(decoded.exp * 1000) // Convert UNIX timestamp to Date
          : new Date(Date.now() + 24 * 60 * 60 * 1000); // Default to 24 hours from now

        // Add token to blacklist
        await TokenBlacklist.create([{
          token: token, // Store original token
          userId,
          tokenHash,
          expiresAt,
          metadata: clientInfo,
          reason: 'logout'
        }], { session });

        // Handle all devices logout
        if (req.query.all_devices === 'true') {
          await UserSession.updateMany(
            { userId, status: 'active' },
            { 
              $set: { 
                status: 'revoked',
                lastActive: new Date()
              }
            },
            { session }
          );
        } else {
          // Update single session
          await UserSession.findOneAndUpdate(
            { userId, token: tokenHash },
            { 
              $set: { 
                status: 'revoked',
                lastActive: new Date()
              }
            },
            { session }
          );
        }

        // Commit transaction
        await session.commitTransaction();

        // Clear cookies if present
        if (req.cookies?.jwt) {
          res.clearCookie('jwt', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict'
          });
        }

        // Log the logout event
        logger.info('User logged out successfully', {
          userId,
          allDevices: req.query.all_devices === 'true',
          metadata: clientInfo
        });

        return res.status(200).json({
          success: true,
          message: 'Successfully logged out',
          details: {
            allDevices: req.query.all_devices === 'true',
            timestamp: new Date()
          }
        });

      } catch (error) {
        // Rollback transaction on error
        await session.abortTransaction();
        throw error;
      } finally {
        session.endSession();
      }

    } catch (error) {
      logger.error('Logout error', {
        error: error.message,
        stack: error.stack,
        userId: req.user?.id
      });

      return res.status(500).json({
        success: false,
        message: 'An error occurred during logout',
        error: process.env.NODE_ENV === 'production' ? 'Internal server error' : error.message
      });
    }
  }
}

module.exports = new AuthController();
