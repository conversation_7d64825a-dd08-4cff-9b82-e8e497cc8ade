const mongoose = require('mongoose');

const availableRoleSchema = new mongoose.Schema({
  role: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  metadata: {
    isSystem: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index for category and role
availableRoleSchema.index({ category: 1, role: 1 });

module.exports = mongoose.model('AvailableRole', availableRoleSchema); 