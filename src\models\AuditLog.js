// src/models/AuditLog.js
const mongoose = require('mongoose');

/**
 * Comprehensive Audit Log Schema for tracking all API operations
 * Stored in separate audit database for security and performance
 */
const auditLogSchema = new mongoose.Schema({
  // Unique identifier for each audit operation
  operation_id: {
    type: String,
    required: true,
    unique: true,
    index: true
  },

  // Timestamp in ISO 8601 format
  timestamp: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  },

  // User information
  user_id: {
    type: String,
    required: false, // Some operations might not have authenticated user
    index: true
  },

  user_email: {
    type: String,
    required: false,
    index: true
  },

  // Operation classification
  operation_type: {
    type: String,
    required: true,
    enum: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'AUTH', 'ASSIGN', 'REVOKE'],
    index: true
  },

  // Resource information
  resource_type: {
    type: String,
    required: true,
    enum: [
      'user', 'organization', 'role', 'privilege', 'auth', 
      'config', 'audit_log', 'rbac_role', 'auth_bridge'
    ],
    index: true
  },

  resource_id: {
    type: String,
    required: false, // Some operations might affect multiple resources
    index: true
  },

  // API endpoint information
  endpoint: {
    type: String,
    required: true,
    index: true
  },

  http_method: {
    type: String,
    required: true,
    enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
    index: true
  },

  // Request/Response data (sanitized)
  request_data: {
    type: mongoose.Schema.Types.Mixed,
    required: false,
    default: {}
  },

  response_status: {
    type: Number,
    required: true,
    index: true
  },

  response_data: {
    type: mongoose.Schema.Types.Mixed,
    required: false,
    default: {}
  },

  // Client information
  ip_address: {
    type: String,
    required: false,
    index: true
  },

  user_agent: {
    type: String,
    required: false
  },

  // Change tracking for UPDATE operations
  changes: {
    before: {
      type: mongoose.Schema.Types.Mixed,
      required: false
    },
    after: {
      type: mongoose.Schema.Types.Mixed,
      required: false
    },
    fields_changed: [{
      type: String
    }]
  },

  // Additional context and metadata
  metadata: {
    // Flexible identifier information
    identifier_types: {
      user_lookup_method: {
        type: String,
        enum: ['objectId', 'email', 'phone', 'username']
      },
      organization_lookup_method: {
        type: String,
        enum: ['objectId', 'subdomain', 'name']
      },
      role_lookup_method: {
        type: String,
        enum: ['objectId', 'roleName']
      }
    },

    // Performance metrics
    execution_time_ms: {
      type: Number,
      required: false
    },

    // Error information
    error_details: {
      error_code: String,
      error_message: String,
      stack_trace: String
    },

    // Session information
    session_id: String,
    request_id: String,

    // Additional context
    organization_context: {
      organization_id: String,
      organization_name: String,
      organization_subdomain: String
    },

    role_context: {
      role_id: String,
      role_name: String,
      role_level: Number
    },

    // Compliance and security
    compliance_flags: [{
      type: String,
      enum: ['PII_ACCESS', 'ADMIN_ACTION', 'PRIVILEGE_ESCALATION', 'BULK_OPERATION']
    }],

    // Custom metadata for specific operations
    custom: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },

  // Data retention and archival
  retention: {
    archive_date: Date,
    retention_period_days: {
      type: Number,
      default: 2555 // 7 years default retention
    },
    is_archived: {
      type: Boolean,
      default: false,
      index: true
    }
  }
}, {
  timestamps: true, // Adds createdAt and updatedAt
  collection: 'audit_logs'
});

// Compound indexes for efficient querying
auditLogSchema.index({ user_id: 1, timestamp: -1 });
auditLogSchema.index({ resource_type: 1, operation_type: 1, timestamp: -1 });
auditLogSchema.index({ endpoint: 1, timestamp: -1 });
auditLogSchema.index({ 'metadata.organization_context.organization_id': 1, timestamp: -1 });
auditLogSchema.index({ response_status: 1, timestamp: -1 });
auditLogSchema.index({ operation_type: 1, resource_type: 1, timestamp: -1 });

// TTL index for automatic cleanup of archived logs (optional)
auditLogSchema.index({ 'retention.archive_date': 1 }, { 
  expireAfterSeconds: 0,
  partialFilterExpression: { 'retention.is_archived': true }
});

// Static methods for common queries
auditLogSchema.statics.findByUser = function(userId, options = {}) {
  const query = { user_id: userId };
  if (options.startDate) query.timestamp = { $gte: options.startDate };
  if (options.endDate) query.timestamp = { ...query.timestamp, $lte: options.endDate };
  if (options.operation_type) query.operation_type = options.operation_type;
  if (options.resource_type) query.resource_type = options.resource_type;
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(options.limit || 100);
};

auditLogSchema.statics.findByResource = function(resourceType, resourceId, options = {}) {
  const query = { resource_type: resourceType, resource_id: resourceId };
  if (options.startDate) query.timestamp = { $gte: options.startDate };
  if (options.endDate) query.timestamp = { ...query.timestamp, $lte: options.endDate };
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(options.limit || 100);
};

auditLogSchema.statics.findByOrganization = function(organizationId, options = {}) {
  const query = { 'metadata.organization_context.organization_id': organizationId };
  if (options.startDate) query.timestamp = { $gte: options.startDate };
  if (options.endDate) query.timestamp = { ...query.timestamp, $lte: options.endDate };
  if (options.operation_type) query.operation_type = options.operation_type;
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(options.limit || 100);
};

// Instance methods
auditLogSchema.methods.sanitize = function() {
  // Remove sensitive data for external API responses
  const sanitized = this.toObject();
  delete sanitized.request_data?.password;
  delete sanitized.request_data?.token;
  delete sanitized.response_data?.access_token;
  delete sanitized.response_data?.refresh_token;
  delete sanitized.metadata?.error_details?.stack_trace;
  return sanitized;
};

module.exports = mongoose.model('AuditLog', auditLogSchema);
