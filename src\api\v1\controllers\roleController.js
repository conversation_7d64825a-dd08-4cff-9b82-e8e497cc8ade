//src/api/v1/controllers/roleController.js
const User = require('../../../models/User');
const RBACRole = require('../../../models/RBACRole');
const Organization = require('../../../models/Organization');
const rbacManager = require('../../../services/rbac-manager');
const { clearCaches } = require('../../../services/rbac-engine');
const logger = require('../../../services/logger');
const mongoose = require('mongoose');

/**
 * Role Management Controller
 * Handles RBAC role operations and assignments
 */
class RoleController {

  /**
   * Helper functions for flexible identifier support
   */
  isObjectId(id) {
    return mongoose.Types.ObjectId.isValid(id);
  }

  isEmail(identifier) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(identifier);
  }

  /**
   * Resolve user by ID or email
   * @param {string} identifier - User ID or email
   * @returns {Object|null} User object or null
   */
  async resolveUser(identifier) {
    if (this.isObjectId(identifier)) {
      return await User.findById(identifier);
    } else if (this.isEmail(identifier)) {
      return await User.findOne({ email: identifier.toLowerCase() });
    }
    return null;
  }

  /**
   * Resolve organization by ID or subdomain
   * @param {string} identifier - Organization ID or subdomain
   * @returns {Object|null} Organization object or null
   */
  async resolveOrganization(identifier) {
    if (this.isObjectId(identifier)) {
      return await Organization.findById(identifier);
    } else {
      return await Organization.findOne({ subdomain: identifier.toLowerCase() });
    }
  }

  /**
   * Resolve role by ID or name within organization scope
   * @param {string} identifier - Role ID or name
   * @param {string} organizationId - Organization ID for scoped lookup
   * @returns {Object|null} Role object or null
   */
  async resolveRole(identifier, organizationId = null) {
    if (this.isObjectId(identifier)) {
      return await RBACRole.findById(identifier);
    } else {
      const query = { name: identifier };
      if (organizationId) {
        query.$or = [
          { scope: 'system' },
          { scope: 'global' },
          { scope: 'organization', organization: organizationId }
        ];
      }
      return await RBACRole.findOne(query);
    }
  }

  /**
   * Initialize system roles for organization
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async initializeRoles(req, res) {
    const { orgIdOrSubdomain } = req.params;

    try {
      const organization = await this.resolveOrganization(orgIdOrSubdomain);
      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: `No organization found with identifier: ${orgIdOrSubdomain}`
          }
        });
      }

      const result = await rbacManager.initializeOrganizationRoles(organization._id);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }

      logger.info('Organization roles initialized', {
        component: 'role-controller',
        operation: 'initialize_roles',
        metadata: {
          organization_id: organization._id,
          roles_created: result.createdRoles.length,
          roles_skipped: result.skippedRoles.length,
          initialized_by: req.user._id
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          organization: {
            _id: organization._id,
            name: organization.name,
            subdomain: organization.subdomain
          },
          rolesCreated: result.createdRoles,
          rolesSkipped: result.skippedRoles,
          summary: {
            totalCreated: result.createdRoles.length,
            totalSkipped: result.skippedRoles.length
          }
        }
      });
    } catch (err) {
      logger.error('Failed to initialize organization roles', err, {
        component: 'role-controller',
        operation: 'initialize_roles',
        metadata: { orgIdOrSubdomain, initialized_by: req.user._id }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to initialize organization roles',
        error: {
          code: 'ROLE_INITIALIZATION_ERROR',
          details: 'An error occurred while initializing roles'
        }
      });
    }
  }

  /**
   * Assign role to user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async assignRole(req, res) {
    const { userIdOrEmail, roleIdOrName, orgIdOrSubdomain } = req.body;

    if (!userIdOrEmail || !roleIdOrName) {
      return res.status(400).json({
        success: false,
        message: 'User identifier and role identifier are required',
        error: {
          code: 'MISSING_REQUIRED_FIELDS',
          details: 'userIdOrEmail and roleIdOrName fields are required'
        }
      });
    }

    try {
      const targetUser = await this.resolveUser(userIdOrEmail);
      if (!targetUser) {
        return res.status(404).json({
          success: false,
          message: 'Target user not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: `No user found with identifier: ${userIdOrEmail}`
          }
        });
      }

      let organization = null;
      if (orgIdOrSubdomain) {
        organization = await this.resolveOrganization(orgIdOrSubdomain);
        if (!organization) {
          return res.status(404).json({
            success: false,
            message: 'Organization not found',
            error: {
              code: 'ORGANIZATION_NOT_FOUND',
              details: `No organization found with identifier: ${orgIdOrSubdomain}`
            }
          });
        }
      }

      const role = await this.resolveRole(roleIdOrName, organization?._id);
      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role not found',
          error: {
            code: 'ROLE_NOT_FOUND',
            details: `No role found with identifier: ${roleIdOrName}`
          }
        });
      }

      const result = await rbacManager.assignUserRole(
        targetUser._id,
        role._id,
        organization?._id,
        req.user._id
      );

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }

      // Clear RBAC caches after role assignment
      await clearCaches();

      logger.info('Role assigned to user', {
        component: 'role-controller',
        operation: 'assign_role',
        metadata: {
          target_user_id: targetUser._id,
          role_id: role._id,
          organization_id: organization?._id,
          assigned_by: req.user._id
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          user: {
            _id: targetUser._id,
            email: targetUser.email,
            name: targetUser.name
          },
          role: {
            _id: role._id,
            name: role.name,
            description: role.description,
            scope: role.scope
          },
          organization: organization ? {
            _id: organization._id,
            name: organization.name,
            subdomain: organization.subdomain
          } : null,
          assignedAt: new Date().toISOString()
        }
      });
    } catch (err) {
      logger.error('Failed to assign role to user', err, {
        component: 'role-controller',
        operation: 'assign_role',
        metadata: { userIdOrEmail, roleIdOrName, orgIdOrSubdomain, assigned_by: req.user._id }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to assign role to user',
        error: {
          code: 'ROLE_ASSIGNMENT_ERROR',
          details: 'An error occurred while assigning role'
        }
      });
    }
  }

  /**
   * Revoke role from user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async revokeRole(req, res) {
    const { userIdOrEmail, roleIdOrName, orgIdOrSubdomain } = req.body;

    if (!userIdOrEmail || !roleIdOrName) {
      return res.status(400).json({
        success: false,
        message: 'User identifier and role identifier are required'
      });
    }

    try {
      const targetUser = await this.resolveUser(userIdOrEmail);
      if (!targetUser) {
        return res.status(404).json({
          success: false,
          message: 'Target user not found'
        });
      }

      let organization = null;
      if (orgIdOrSubdomain) {
        organization = await this.resolveOrganization(orgIdOrSubdomain);
        if (!organization) {
          return res.status(404).json({
            success: false,
            message: 'Organization not found'
          });
        }
      }

      const role = await this.resolveRole(roleIdOrName, organization?._id);
      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role not found'
        });
      }

      const result = await rbacManager.revokeUserRole(
        targetUser._id,
        role._id,
        organization?._id,
        req.user._id
      );

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }

      // Clear RBAC caches after role revocation
      await clearCaches();

      logger.info('Role revoked from user', {
        component: 'role-controller',
        operation: 'revoke_role',
        metadata: {
          target_user_id: targetUser._id,
          role_id: role._id,
          organization_id: organization?._id,
          revoked_by: req.user._id
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          user: {
            _id: targetUser._id,
            email: targetUser.email,
            name: targetUser.name
          },
          role: {
            _id: role._id,
            name: role.name,
            description: role.description
          },
          organization: organization ? {
            _id: organization._id,
            name: organization.name,
            subdomain: organization.subdomain
          } : null,
          revokedAt: new Date().toISOString()
        }
      });
    } catch (err) {
      logger.error('Failed to revoke role from user', err, {
        component: 'role-controller',
        operation: 'revoke_role',
        metadata: { userIdOrEmail, roleIdOrName, orgIdOrSubdomain, revoked_by: req.user._id }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to revoke role from user',
        error: {
          code: 'ROLE_REVOCATION_ERROR',
          details: 'An error occurred while revoking role'
        }
      });
    }
  }

  /**
   * Get available roles for organization
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getOrganizationRoles(req, res) {
    const { orgIdOrSubdomain } = req.params;

    try {
      const organization = await this.resolveOrganization(orgIdOrSubdomain);
      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found'
        });
      }

      const options = {
        includeInactive: req.query.includeInactive === 'true',
        sortBy: req.query.sortBy || 'hierarchy.level',
        sortOrder: parseInt(req.query.sortOrder) || 1,
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 50
      };

      const result = await rbacManager.getOrganizationRoles(organization._id, options);

      res.json({
        success: true,
        data: {
          organization: {
            _id: organization._id,
            name: organization.name,
            subdomain: organization.subdomain
          },
          roles: result.roles,
          pagination: {
            page: options.page,
            limit: options.limit,
            total: result.total,
            pages: Math.ceil(result.total / options.limit)
          }
        }
      });
    } catch (err) {
      logger.error('Failed to get organization roles', err, {
        component: 'role-controller',
        operation: 'get_organization_roles',
        metadata: { orgIdOrSubdomain }
      });
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve organization roles',
        error: {
          code: 'ROLES_FETCH_ERROR',
          details: 'An error occurred while retrieving roles'
        }
      });
    }
  }

  /**
   * Get all available roles and permissions
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAvailableRoles(req, res) {
    try {
      // Add logging to debug
      logger.info('getAvailableRoles called', {
        component: 'role-controller',
        operation: 'get_available_roles',
        metadata: {
          path: req.path,
          endpoint: req.originalUrl
        }
      });

      // Define all available permissions by category
      const availablePermissions = {
        user: [
          "user:create",
          "user:read",
          "user:update",
          "user:delete",
          "user:assign"
        ],
        organization: [
          "org:create",
          "org:read",
          "org:update",
          "org:delete",
          "org:manage"
        ],
        role: [
          "role:create",
          "role:read",
          "role:update",
          "role:delete",
          "role:assign"
        ],
        config: [
          "config:read",
          "config:write",
          "config:delete"
        ],
        event: [
          "event:create",
          "event:read",
          "event:update",
          "event:delete",
          "event:publish"
        ],
        ticket: [
          "ticket:create",
          "ticket:read",
          "ticket:update",
          "ticket:delete",
          "ticket:assign"
        ]
      };

      // Define system roles with their permissions
      const systemRoles = [
        {
          name: "SUPER_ADMIN",
          description: "Super Administrator with full system access",
          permissions: [
            "user:create", "user:read", "user:update", "user:delete",
            "org:create", "org:read", "org:update", "org:delete",
            "role:create", "role:read", "role:update", "role:delete",
            "config:read", "config:write", "config:delete"
          ]
        },
        {
          name: "ORG_ADMIN",
          description: "Organization Administrator",
          permissions: [
            "user:read", "user:update",
            "org:read", "org:update",
            "role:read",
            "config:read"
          ]
        },
        {
          name: "USER",
          description: "Regular User",
          permissions: [
            "user:read",
            "org:read",
            "config:read"
          ]
        }
      ];

      // Check which endpoint was called
      const isAvailableRolesEndpoint = req.path.includes('available-roles');

      // Log the response being sent
      logger.info('Sending roles response', {
        component: 'role-controller',
        operation: 'get_available_roles',
        metadata: {
          endpoint: isAvailableRolesEndpoint ? '/available-roles' : '/available',
          rolesCount: systemRoles.length,
          permissionsCount: Object.values(availablePermissions).flat().length
        }
      });

      // Format response based on endpoint
      if (isAvailableRolesEndpoint) {
        return res.status(200).json({
          success: true,
          data: {
            systemRoles,
            availablePermissions,
            metadata: {
              totalRoles: systemRoles.length,
              totalPermissions: Object.values(availablePermissions).flat().length,
              permissionCategories: Object.keys(availablePermissions),
              timestamp: new Date().toISOString()
            }
          }
        });
      } else {
        return res.status(200).json({
          success: true,
          data: {
            roles: systemRoles.map(role => ({
              name: role.name,
              permissions: role.permissions
            })),
            metadata: {
              timestamp: new Date().toISOString()
            }
          }
        });
      }

    } catch (error) {
      logger.error('Error in getAvailableRoles', {
        component: 'role-controller',
        operation: 'get_available_roles',
        error: error.message,
        stack: error.stack
      });

      return res.status(500).json({
        success: false,
        message: 'Failed to fetch available roles',
        error: {
          code: 'ROLES_FETCH_ERROR',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        }
      });
    }
  }
}

module.exports = new RoleController();
