//src/api/v1/controllers/userController.js
require('dotenv').config();
const jwt = require('jsonwebtoken');
const AuthBridge = require('../../../models/AuthBridge');
const User = require('../../../models/User');
const Organization = require('../../../models/Organization');
const admin = require('../../../config/firebase');
const { welcomeEmail } = require('../../../utils/mailer');
const logger = require('../../../services/logger');
const auditLogger = require('../../../services/auditLogger');

/**
 * User Controller
 * Handles all user-related business logic
 */
class UserController {

  /**
   * Register a new user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async register(req, res) {
    const { name, email, password, phone_number } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        message: 'Email and password are required'
      });
    }

    try {
      if (await AuthBridge.findOne({ email }))
        return res.status(400).json({ message: 'User already exists.' });

      // Firebase
      let fbUser;
      try {
        const firebaseUserData = {
          email,
          password
        };

        // Add optional fields only if provided
        if (name) firebaseUserData.displayName = name;
        if (phone_number) firebaseUserData.phoneNumber = phone_number;

        fbUser = await admin.auth().createUser(firebaseUserData);
      } catch (err) {
        if (
          err.code === 'auth/email-already-exists' ||
          err.code === 'auth/phone-number-already-exists'
        ) {
          return res.status(400).json({
            message: 'User already exists in Firebase via email or phone.'
          });
        }
        return res.status(500).json({ message: 'Firebase registration failed', error: err.message });
      }

      const authUser = await new AuthBridge({
        email,
        password,
        firebase_uid: fbUser.uid
      }).save();

      const userData = {
        email,
        firebase_uid: fbUser.uid
      };

      // Add optional fields only if provided
      if (name) userData.name = name;
      if (phone_number) userData.phone_number = phone_number;

      const user = await new User(userData).save();

      const idToken = jwt.sign({ sub: authUser._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
      const refreshToken = jwt.sign({ sub: authUser._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });

      res.status(201).json({
        message: 'user_registered',
        user: {
          _id: user._id,
          firebase_uid: user.firebase_uid,
          email: user.email,
          name: user.name,
          phone_number: user.phone_number
        },
        idToken,
        refreshToken,
        expiresIn: '3600'
      });

      welcomeEmail(email, name);
    } catch (err) {
      logger.error('User registration failed', err, {
        component: 'user-controller',
        operation: 'register',
        metadata: { email, name }
      });
      res.status(500).json({ message: 'Registration failed', error: err.message });
    }
  }

  /**
   * Get paginated list of users
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getUsers(req, res) {
    try {
      const page = Number.parseInt(req.query.page, 10) || 1;
      const limit = Number.parseInt(req.query.limit, 10) || 10;
      const skip = (page - 1) * limit;

      const [users, total] = await Promise.all([
        User.find({}, 'email name phone_number status')
          .skip(skip)
          .limit(limit)
          .lean(),
        User.countDocuments()
      ]);

      res.json({
        page,
        limit,
        total,
        users
      });
    } catch (err) {
      logger.error('Failed to fetch users', err, {
        component: 'user-controller',
        operation: 'get_users'
      });
      res.status(500).json({ message: 'Failed to fetch users', error: err.message });
    }
  }



  /**
   * Update current user profile (for /update_user endpoint)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateUserProfile(req, res) {
    try {
      const {
        name,
        email,
        phone_number,
        type,
        fullName,
        jobTitle,
        companyName,
        bio,
        industryTags,
        networkingGoal,
        delegateEmail
      } = req.body;

      const user = await User.findOne({ firebase_uid: req.user.uid });
      if (!user) return res.status(404).json({ message: 'User not found' });

      // Validate bio length
      if (bio && bio.length > 200) {
        return res.status(400).json({
          message: 'Bio cannot exceed 200 characters',
          field: 'bio',
          maxLength: 200,
          currentLength: bio.length
        });
      }

      // Validate delegate email format if provided
      if (delegateEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(delegateEmail)) {
        return res.status(400).json({
          message: 'Invalid delegate email format',
          field: 'delegateEmail'
        });
      }

      // Validate industry tags (ensure it's an array)
      if (industryTags && !Array.isArray(industryTags)) {
        return res.status(400).json({
          message: 'Industry tags must be an array',
          field: 'industryTags'
        });
      }

      // Validate phone number format if provided
      if (phone_number !== undefined && phone_number !== null && phone_number !== '') {
        if (!/^\+[1-9]\d{1,14}$/.test(phone_number)) {
          return res.status(400).json({
            message: 'Phone number must be in E.164 format (e.g., +1234567890)',
            field: 'phone_number'
          });
        }
      }

      // Validate user type if provided
      if (type !== undefined && type !== null && !['individual', 'organization'].includes(type)) {
        return res.status(400).json({
          message: 'User type must be either "individual" or "organization"',
          field: 'type'
        });
      }

      // Update Firebase user (only basic fields)
      if (name !== undefined || email !== undefined || phone_number !== undefined) {
        const firebaseUpdateData = {};

        if (name !== undefined) firebaseUpdateData.displayName = name || null;
        if (email !== undefined) firebaseUpdateData.email = email;
        if (phone_number !== undefined) {
          // Only set phoneNumber if it's a valid non-empty string
          if (phone_number && phone_number.trim() !== '') {
            firebaseUpdateData.phoneNumber = phone_number;
          }
        }

        await admin.auth().updateUser(user.firebase_uid, firebaseUpdateData);
      }

      // Update MongoDB user - basic fields
      if (name !== undefined) user.name = name;
      if (email !== undefined) user.email = email;
      if (phone_number !== undefined) user.phone_number = phone_number;
      if (type !== undefined) user.type = type;

      // Update profile fields
      if (!user.profile) user.profile = {};

      if (fullName !== undefined) user.profile.fullName = fullName;
      if (jobTitle !== undefined) user.profile.jobTitle = jobTitle;
      if (companyName !== undefined) user.profile.companyName = companyName;
      if (bio !== undefined) user.profile.bio = bio;
      if (industryTags !== undefined) user.profile.industryTags = industryTags;
      if (networkingGoal !== undefined) user.profile.networkingGoal = networkingGoal;
      if (delegateEmail !== undefined) user.profile.delegateEmail = delegateEmail;

      await user.save();

      res.json({
        message: 'user_updated',
        user: {
          _id: user._id,
          name: user.name,
          email: user.email,
          phone_number: user.phone_number,
          type: user.type,
          profile: {
            fullName: user.profile.fullName,
            jobTitle: user.profile.jobTitle,
            companyName: user.profile.companyName,
            bio: user.profile.bio,
            industryTags: user.profile.industryTags,
            networkingGoal: user.profile.networkingGoal,
            delegateEmail: user.profile.delegateEmail,
            avatar: user.profile.avatar,
            timezone: user.profile.timezone,
            language: user.profile.language
          }
        }
      });
    } catch (err) {
      let msg = err.message;
      if (err.code === 'auth/email-already-exists' ||
          err.code === 'auth/phone-number-already-exists') {
        msg = 'Email or phone number already exists in Firebase';
      }

      // Handle validation errors
      if (err.name === 'ValidationError') {
        const validationErrors = Object.values(err.errors).map(e => ({
          field: e.path,
          message: e.message
        }));
        return res.status(400).json({
          message: 'Validation failed',
          errors: validationErrors
        });
      }

      logger.error('Failed to update user profile', err, {
        component: 'user-controller',
        operation: 'update_user_profile',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ message: 'update_failed', error: msg });
    }
  }



  /**
   * Delete current user account (for /delete_user endpoint)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteUser(req, res) {
    try {
      const user = await User.findOne({ firebase_uid: req.user.uid });
      if (!user) return res.status(404).json({ message: 'User not found' });

      // Check if user is God Super User
      if (user.isGodSuperUser()) {
        return res.status(403).json({
          message: 'Cannot delete God Super User. Transfer privileges first.'
        });
      }

      // Delete from Firebase
      try {
        await admin.auth().deleteUser(user.firebase_uid);
      } catch (firebaseErr) {
        logger.warn('Failed to delete user from Firebase', firebaseErr, {
          component: 'user-controller',
          operation: 'delete_user_firebase',
          metadata: { user_id: user._id, firebase_uid: user.firebase_uid }
        });
      }

      // Remove from organizations
      await Organization.updateMany(
        { members: user._id },
        { $pull: { members: user._id } }
      );

      // Delete from AuthBridge
      await AuthBridge.deleteOne({ firebase_uid: req.user.uid });

      // Delete from User collection
      await User.deleteOne({ firebase_uid: req.user.uid });

      logger.info('User deleted successfully', {
        component: 'user-controller',
        operation: 'delete_user',
        metadata: { user_id: user._id, email: user.email }
      });

      res.json({ message: 'user_deleted' });
    } catch (err) {
      logger.error('Failed to delete user', err, {
        component: 'user-controller',
        operation: 'delete_user',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ message: 'delete_failed', error: err.message });
    }
  }



  /**
   * Get current user profile (authenticated user)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getCurrentUser(req, res) {
    try {
      const user = await User.findOne({ firebase_uid: req.user.uid })
        .populate({
          path: 'roles.org',
          select: 'name subdomain branding'
        })
        .populate({
          path: 'roles.role',
          select: 'name description hierarchy'
        })
        .populate('defaultOrganization', 'name subdomain branding status');

      if (!user) return res.status(404).json({ message: 'User not found' });

      // Get organizations using the new method that checks both systems
      const organizationsWithRoles = await user.getOrganizationsWithRoles();
      const organizations = organizationsWithRoles.map(item => item.organization);

      res.json({
        _id: user._id,
        email: user.email,
        phone_number: user.phone_number,
        type: user.type,
        status: user.status,
        profile: user.profile,
        roles: user.roles,
        systemPrivileges: user.systemPrivileges,
        defaultOrganization: user.defaultOrganization,
        organizations: organizations,
        // Privilege summary
        privilegeSummary: {
          isGodSuperUser: user.isGodSuperUser(),
          hasSystemPrivileges: user.hasSystemPrivileges(),
          highestPrivilegeLevel: user.getHighestPrivilegeLevel(),
          organizationCount: organizations.length,
          hasDefaultOrganization: !!user.defaultOrganization
        }
      });
    } catch (err) {
      logger.error('Failed to fetch current user', err, {
        component: 'user-controller',
        operation: 'get_current_user',
        metadata: { user_id: req.user._id }
      });
      res.status(500).json({ message: 'fetch_failed', error: err.message });
    }
  }

  /**
   * Update user status by identifier
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateUserStatus(req, res) {
    let { identifier, status } = req.body;
    identifier = identifier?.trim();

    if (!identifier || !status) {
      return res.status(400).json({ message: 'Identifier and status are required.' });
    }

    const allowedStatuses = ['active', 'inactive', 'banned', 'blocked'];
    if (!allowedStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status value.' });
    }

    const query = [
      { email: new RegExp(`^${identifier}$`, 'i') }
    ];
    if (/^\+?\d{7,15}$/.test(identifier)) {
      query.push({ phone_number: identifier });
    }

    try {
      const user = await User.findOneAndUpdate(
        { $or: query },
        { status },
        { new: true }
      );

      if (!user) {
        return res.status(404).json({ message: 'User not found.' });
      }

      logger.info('User status updated', {
        component: 'user-controller',
        operation: 'update_user_status',
        metadata: {
          user_id: user._id,
          identifier,
          new_status: status,
          updated_by: req.user._id
        }
      });

      res.json({
        message: 'User status updated.',
        user: {
          _id: user._id,
          email: user.email,
          phone_number: user.phone_number,
          status: user.status
        }
      });
    } catch (err) {
      logger.error('Failed to update user status', err, {
        component: 'user-controller',
        operation: 'update_user_status',
        metadata: { identifier, status }
      });
      res.status(500).json({ message: 'Update failed.', error: err.message });
    }
  }

  /**
   * Get user status by identifier
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getUserStatus(req, res) {
    const identifier = req.query.identifier?.trim();

    if (!identifier) {
      return res.status(400).json({ message: 'Identifier required.' });
    }

    const query = [
      { email: new RegExp(`^${identifier}$`, 'i') }
    ];
    if (/^\+?\d{7,15}$/.test(identifier)) {
      query.push({ phone_number: identifier });
    }

    try {
      const user = await User.findOne({ $or: query });

      if (!user) {
        return res.status(404).json({ message: 'User not found.' });
      }

      res.json({
        _id: user._id,
        email: user.email,
        phone_number: user.phone_number,
        status: user.status
      });
    } catch (err) {
      logger.error('Failed to fetch user status', err, {
        component: 'user-controller',
        operation: 'get_user_status',
        metadata: { identifier }
      });
      res.status(500).json({ message: 'Fetch failed.', error: err.message });
    }
  }

  /**
   * Update user's default organization
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateDefaultOrganization(req, res) {
    try {
      const { organizationId, organizationSubdomain } = req.body;
      const userId = req.user._id;

      // Validate input - require either organizationId or organizationSubdomain
      if (!organizationId && !organizationSubdomain) {
        return res.status(400).json({
          success: false,
          message: 'Organization identifier is required',
          error: {
            code: 'MISSING_ORGANIZATION_IDENTIFIER',
            details: 'Either organizationId or organizationSubdomain field is required in request body'
          }
        });
      }

      // If both are provided, prioritize organizationId
      const identifier = organizationId || organizationSubdomain;
      const isObjectId = identifier.match(/^[0-9a-fA-F]{24}$/);

      // Validate ObjectId format if using ID
      if (organizationId && !isObjectId) {
        return res.status(400).json({
          success: false,
          message: 'Invalid organization ID format',
          error: {
            code: 'INVALID_ORGANIZATION_ID',
            details: 'Organization ID must be a valid MongoDB ObjectId'
          }
        });
      }

      // Validate subdomain format if using subdomain
      if (organizationSubdomain && !organizationId && !/^[a-z0-9-]+$/.test(organizationSubdomain)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid organization subdomain format',
          error: {
            code: 'INVALID_ORGANIZATION_SUBDOMAIN',
            details: 'Organization subdomain must contain only lowercase letters, numbers, and hyphens'
          }
        });
      }

      // Find the user
      const user = await User.findById(userId).populate('defaultOrganization', 'name subdomain');
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: 'Authenticated user not found in database'
          }
        });
      }

      // Find organization by ID or subdomain
      let organization;
      if (isObjectId) {
        organization = await Organization.findById(identifier);
      } else {
        organization = await Organization.findOne({ subdomain: identifier.toLowerCase() });
      }

      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found',
          error: {
            code: 'ORGANIZATION_NOT_FOUND',
            details: isObjectId
              ? `Organization with ID ${identifier} does not exist`
              : `Organization with subdomain '${identifier}' does not exist`
          }
        });
      }

      // Check if user is a member of the organization
      const isMember = user.roles.some(role => role.org.toString() === organization._id.toString());
      if (!isMember) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: User is not a member of this organization',
          error: {
            code: 'NOT_ORGANIZATION_MEMBER',
            details: `User must be a member of organization '${organization.name}' to set it as default`
          }
        });
      }

      // Store previous default organization for logging
      const previousDefaultOrg = user.defaultOrganization;

      // Update default organization
      await user.setDefaultOrganization(organization._id);

      // Populate the updated default organization
      await user.populate('defaultOrganization', 'name subdomain branding status');

      // Log the change
      logger.info('User default organization updated', {
        component: 'user-controller',
        operation: 'update_default_organization',
        metadata: {
          user_id: userId,
          user_email: user.email,
          previous_default_org: previousDefaultOrg,
          new_default_org: organization._id,
          organization_name: organization.name,
          organization_subdomain: organization.subdomain,
          identifier_used: identifier,
          identifier_type: isObjectId ? 'objectId' : 'subdomain'
        }
      });

      // Audit logging
      await auditLogger.logOperation({
        user: req.user,
        operationType: 'UPDATE',
        resourceType: 'user_default_organization',
        resourceId: userId,
        endpoint: req.originalUrl,
        method: req.method,
        requestData: { organizationId, organizationSubdomain, identifier_used: identifier },
        responseStatus: 200,
        organizationContext: {
          organization_id: organization._id,
          organization_name: organization.name,
          organization_subdomain: organization.subdomain
        },
        customMetadata: {
          previous_default_org: previousDefaultOrg,
          new_default_org: organization._id,
          identifier_type: isObjectId ? 'objectId' : 'subdomain'
        }
      });

      res.json({
        success: true,
        message: `Default organization updated to '${organization.name}'`,
        data: {
          user: {
            _id: user._id,
            email: user.email,
            name: user.name,
            defaultOrganization: {
              _id: user.defaultOrganization._id,
              name: user.defaultOrganization.name,
              subdomain: user.defaultOrganization.subdomain,
              branding: user.defaultOrganization.branding,
              status: user.defaultOrganization.status
            }
          },
          previousDefaultOrganization: previousDefaultOrg,
          updatedAt: new Date()
        },
        metadata: {
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to update default organization', {
        component: 'user-controller',
        operation: 'update_default_organization',
        error: error.message,
        metadata: {
          user_id: req.user._id,
          organization_id: req.body.organizationId,
          organization_subdomain: req.body.organizationSubdomain,
          identifier_used: req.body.organizationId || req.body.organizationSubdomain
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to update default organization',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          details: 'An error occurred while updating the default organization'
        }
      });
    }
  }

  /**
   * Clear user's default organization
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async clearDefaultOrganization(req, res) {
    try {
      const userId = req.user._id;

      const user = await User.findById(userId).populate('defaultOrganization', 'name subdomain');
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
          error: {
            code: 'USER_NOT_FOUND',
            details: 'Authenticated user not found in database'
          }
        });
      }

      const previousDefaultOrg = user.defaultOrganization;

      // Clear default organization
      await user.setDefaultOrganization(null);

      // Log the change
      logger.info('User default organization cleared', {
        component: 'user-controller',
        operation: 'clear_default_organization',
        metadata: {
          user_id: userId,
          user_email: user.email,
          previous_default_org: previousDefaultOrg?._id,
          previous_org_name: previousDefaultOrg?.name
        }
      });

      res.json({
        success: true,
        message: 'Default organization cleared successfully',
        data: {
          user: {
            _id: user._id,
            email: user.email,
            name: user.name,
            defaultOrganization: null
          },
          previousDefaultOrganization: previousDefaultOrg,
          clearedAt: new Date()
        },
        metadata: {
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to clear default organization', {
        component: 'user-controller',
        operation: 'clear_default_organization',
        error: error.message,
        metadata: {
          user_id: req.user._id
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to clear default organization',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          details: 'An error occurred while clearing the default organization'
        }
      });
    }
  }
}

module.exports = new UserController();
