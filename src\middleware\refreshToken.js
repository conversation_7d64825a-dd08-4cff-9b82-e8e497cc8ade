//src/middleware/refreshToken.js
const express = require('express');
const router = express.Router();
const fetch = require('node-fetch');
const jwt = require('jsonwebtoken');

/**
 * @swagger
 * /api/v1/middleware/refreshToken-firebase:
 *   post:
 *     tags: [Middleware]
 *     summary: Refresh Firebase ID token
 *     description: |
 *       Exchanges a Firebase refresh token for a new ID token and refresh token.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: Firebase refresh token
 *             example:
 *               refreshToken: "AEu4IL2...your_refresh_token..."
 *     responses:
 *       200:
 *         description: Successfully refreshed tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 idToken:
 *                   type: string
 *                 refreshToken:
 *                   type: string
 *                 expiresIn:
 *                   type: integer
 *       400:
 *         description: Missing refreshToken
 *       401:
 *         description: Invalid or expired refresh token
 *       500:
 *         description: Internal server error
 */
router.post('/refreshToken-firebase', async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(400).json({ error: 'Refresh token is required' });
  }

  try {
    const response = await fetch(
      `https://securetoken.googleapis.com/v1/token?key=${process.env.FIREBASE_API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          grant_type:    'refresh_token',
          refresh_token: refreshToken,
        }),
      }
    );

    const data = await response.json();

    if (data.error) {
      return res.status(401).json({ error: data.error.message });
    }

    res.status(200).json({
      idToken:      data.id_token,
      refreshToken: data.refresh_token,
      expiresIn:    data.expires_in,
    });
  } catch (error) {
    res.status(500).json({
      error:   'Error refreshing token',
      details: error.message,
    });
  }
});
/**
 * @swagger
 * /api/v1/middleware/refreshToken-jwt:
 *   post:
 *     tags: [Middleware]
 *     summary: Refresh JWT access token using refresh token
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [refreshToken]
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: Your current JWT refresh token
 *                 example: eyJh...
 *     responses:
 *       200:
 *         description: New access & refresh tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 accessToken:
 *                   type: string
 *                 refreshToken:
 *                   type: string
 *       400:
 *         description: Missing refreshToken
 *       401:
 *         description: Invalid or expired refresh token
 *       500:
 *         description: Server error
 */
router.post('/refreshToken-jwt', (req, res) => {
  const { refreshToken } = req.body;
  if (!refreshToken) {
    return res.status(400).json({ message: 'refreshToken is required' });
  }

  jwt.verify(
    refreshToken,
    process.env.JWT_REFRESH_SECRET,
    (err, payload) => {
      if (err) {
        return res
          .status(401)
          .json({ message: 'Invalid refresh token', error: err.message });
      }

      const userId = payload.sub;

      const accessToken = jwt.sign(
        { sub: userId },
        process.env.JWT_SECRET,
        { expiresIn: '15m' }
      );

      const newRefreshToken = jwt.sign(
        { sub: userId },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: '30d' }
      );

      res.json({ accessToken, refreshToken: newRefreshToken });
    }
  );
});

module.exports = router;