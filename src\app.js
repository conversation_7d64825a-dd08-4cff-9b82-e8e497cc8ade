require('dotenv').config();
const express  = require('express');
const cors     = require('cors');
const path     = require('path');
const { swaggerSpec, swaggerUi } = require('./docs/swagger');
const userRoutes   = require('./api/v1/routes/users');
const authRoutes   = require('./api/v1/routes/auth');
const oauthRoutes  = require('./api/v1/routes/oauth');
const configRoutes = require('./api/v1/routes/config');
const organizationRoutes = require('./api/v1/routes/organizations');
const refreshMw    = require('./middleware/refreshToken');
const roleRoutes   = require('./api/v1/routes/roles-crud');
const privilegeRoutes = require('./api/v1/routes/privilege-management');

const app = express();

// Health endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'healthy' });
});

// Welcome page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'docs', 'welcome.html'));
});

// Documentation endpoints
app.use('/api-docs-ui', swaggerUi.serve, swaggerUi.setup(swaggerSpec, { explorer: true }));
app.get('/swagger.json', (req, res) => res.json(swaggerSpec));
app.get('/api-docs', (req, res) => res.sendFile(path.join(__dirname,'docs', 'swagger.html')));
app.get('/auth-guide', (req, res) => {
  res.setHeader('Content-Type', 'text/markdown');
  res.sendFile(path.join(__dirname, 'docs', 'auth-guide.md'));
});
app.get('/docs', (req, res) => {
  res.setHeader('Content-Type', 'text/markdown');
  res.sendFile(path.join(__dirname, 'docs', 'README.md'));
});

// Middleware
app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || true,
  credentials: true
}));

// API routes
app.use('/api/v1/users',   userRoutes);
app.use('/api/v1/auth',    authRoutes);
app.use('/api/v1/oauth',   oauthRoutes);
app.use('/api/v1/organizations', organizationRoutes);
app.use('/api/v1/config',  configRoutes);
app.use('/api/v1/middleware', refreshMw);
app.use('/api/v1/roles',   roleRoutes);
app.use('/api/v1/privileges', privilegeRoutes);

module.exports = app;
