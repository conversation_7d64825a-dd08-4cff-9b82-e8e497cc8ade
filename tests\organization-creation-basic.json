{"test_name": "Create Organization - Basic", "description": "Test basic organization creation with minimal required fields", "endpoint": "POST /api/v1/organizations", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "request_body": {"name": "Test Organization", "subdomain": "test-org-001"}, "expected_response": {"status": 201, "body": {"message": "Organization created successfully. Pending approval from system administrator.", "organization": {"_id": "string", "name": "Test Organization", "subdomain": "test-org-001", "status": "pending", "branding": {}, "createdBy": "string", "createdByRole": "super_user", "createdAt": "string", "members": [{"user": "string", "role": "super_user", "joinedAt": "string", "status": "active"}]}}}, "curl_command": "curl -X POST 'https://your-domain.vercel.app/api/v1/organizations' -H 'Content-Type: application/json' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE' -d '{\"name\":\"Test Organization\",\"subdomain\":\"test-org-001\"}'"}