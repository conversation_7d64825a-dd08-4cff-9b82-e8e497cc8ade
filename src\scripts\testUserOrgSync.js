#!/usr/bin/env node

/**
 * Test Script: Verify User-Organization Synchronization
 * 
 * This script tests that the user-organization bidirectional relationship
 * is working correctly after the fix.
 * 
 * Usage: node src/scripts/testUserOrgSync.js [userId]
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Organization = require('../models/Organization');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test a specific user's organization relationships
const testUserOrganizations = async (userId) => {
  console.log(`\n🔍 Testing user: ${userId}`);
  
  const user = await User.findById(userId)
    .populate('roles.org')
    .populate('roles.role');
    
  if (!user) {
    console.log('❌ User not found');
    return;
  }
  
  console.log(`👤 User: ${user.email}`);
  console.log(`📧 Name: ${user.name || 'N/A'}`);
  
  // Test old method (roles-based)
  console.log('\n📋 Old Method (user.roles):');
  if (user.roles && user.roles.length > 0) {
    user.roles.forEach((role, index) => {
      console.log(`   ${index + 1}. Org: ${role.org?.name || 'Unknown'} (${role.org?._id})`);
      console.log(`      Role: ${role.role?.name || 'Unknown'}`);
      console.log(`      Assigned: ${role.assignedAt}`);
    });
  } else {
    console.log('   No roles found');
  }
  
  // Test new method (organization members)
  console.log('\n🏢 New Method (user.getOrganizationsWithRoles):');
  try {
    const organizationsWithRoles = await user.getOrganizationsWithRoles();
    if (organizationsWithRoles && organizationsWithRoles.length > 0) {
      organizationsWithRoles.forEach((item, index) => {
        console.log(`   ${index + 1}. Org: ${item.organization.name} (${item.organization._id})`);
        console.log(`      Role: ${item.role}`);
        console.log(`      Joined: ${item.joinedAt}`);
        console.log(`      Status: ${item.status}`);
      });
    } else {
      console.log('   No organizations found');
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }
  
  // Verify bidirectional consistency
  console.log('\n🔄 Bidirectional Consistency Check:');
  const organizations = await Organization.find({ 'members.user': userId });
  
  if (organizations.length > 0) {
    for (const org of organizations) {
      const member = org.members.find(m => m.user.toString() === userId);
      const userRole = user.roles.find(r => r.org && r.org._id.toString() === org._id.toString());
      
      console.log(`   📋 ${org.name}:`);
      console.log(`      In org.members: ✅ (role: ${member.role})`);
      console.log(`      In user.roles: ${userRole ? '✅' : '❌'} ${userRole ? `(role: ${userRole.role?.name})` : ''}`);
      
      if (!userRole) {
        console.log(`      ⚠️  INCONSISTENCY DETECTED!`);
      }
    }
  } else {
    console.log('   User is not a member of any organizations');
  }
};

// Test all users with organization relationships
const testAllUsers = async () => {
  console.log('\n🔍 Testing all users with organization relationships...');
  
  const users = await User.find({ 'roles.0': { $exists: true } });
  console.log(`Found ${users.length} users with roles`);
  
  let inconsistentUsers = 0;
  
  for (const user of users) {
    const organizations = await Organization.find({ 'members.user': user._id });
    const userOrgCount = user.roles.filter(r => r.org).length;
    const memberOrgCount = organizations.length;
    
    if (userOrgCount !== memberOrgCount) {
      inconsistentUsers++;
      console.log(`❌ ${user.email}: roles=${userOrgCount}, members=${memberOrgCount}`);
    } else {
      console.log(`✅ ${user.email}: consistent (${userOrgCount} orgs)`);
    }
  }
  
  if (inconsistentUsers === 0) {
    console.log('\n🎉 All users have consistent organization relationships!');
  } else {
    console.log(`\n⚠️  Found ${inconsistentUsers} users with inconsistent relationships`);
  }
};

// Test organization perspective
const testOrganizationMembers = async (orgId) => {
  console.log(`\n🏢 Testing organization: ${orgId}`);
  
  const org = await Organization.findById(orgId);
  if (!org) {
    console.log('❌ Organization not found');
    return;
  }
  
  console.log(`🏢 Organization: ${org.name} (${org.subdomain})`);
  console.log(`👥 Members count: ${org.members.length}`);
  
  for (const member of org.members) {
    const user = await User.findById(member.user);
    if (user) {
      const userRole = user.roles.find(r => r.org && r.org.toString() === orgId);
      console.log(`   👤 ${user.email}:`);
      console.log(`      In org.members: ✅ (role: ${member.role})`);
      console.log(`      In user.roles: ${userRole ? '✅' : '❌'} ${userRole ? `(role: ${userRole.role})` : ''}`);
    } else {
      console.log(`   ❌ User not found: ${member.user}`);
    }
  }
};

// Main execution function
const main = async () => {
  const args = process.argv.slice(2);
  const userId = args[0];
  const orgId = args[1];
  
  console.log('🧪 Starting User-Organization Synchronization Test...');
  
  try {
    await connectDB();
    
    if (userId) {
      await testUserOrganizations(userId);
    } else if (orgId) {
      await testOrganizationMembers(orgId);
    } else {
      await testAllUsers();
    }
    
    console.log('\n✅ Test completed!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n👋 Database connection closed');
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, testUserOrganizations, testAllUsers, testOrganizationMembers };
