// migrations/001-hierarchical-privileges.js
const mongoose = require('mongoose');
const User = require('../models/User');
const Role = require('../models/Role');
const Organization = require('../models/Organization');
const privilegeManager = require('../services/privilege-manager');
const logger = require('../services/logger');

/**
 * Migration: Hierarchical Privilege System
 * 
 * This migration:
 * 1. Updates existing users to new schema format
 * 2. Creates system roles for all organizations
 * 3. Migrates existing role assignments to new format
 * 4. Sets up God Super User if specified
 */

class HierarchicalPrivilegesMigration {
  
  constructor() {
    this.migrationName = 'hierarchical-privileges';
    this.version = '001';
  }

  /**
   * Run the migration
   * @param {Object} options - Migration options
   * @param {string} options.godSuperUserEmail - Email of user to make God Super User
   * @param {boolean} options.dryRun - If true, only log what would be done
   */
  async up(options = {}) {
    const { godSuperUserEmail, dryRun = false } = options;
    const correlationId = logger.generateCorrelationId();
    
    try {
      logger.info('Starting hierarchical privileges migration', {
        component: 'migration',
        operation: 'hierarchical_privileges_up',
        correlation_id: correlationId,
        metadata: {
          migration_name: this.migrationName,
          version: this.version,
          dry_run: dryRun,
          god_super_user_email: godSuperUserEmail
        }
      });

      const results = {
        users_updated: 0,
        organizations_processed: 0,
        roles_created: 0,
        god_super_user_assigned: false,
        errors: []
      };

      // Step 1: Update user schema - add systemPrivileges and enhance roles
      logger.info('Step 1: Updating user schema');
      const users = await User.find({});
      
      for (const user of users) {
        try {
          let updated = false;

          // Initialize systemPrivileges if not exists
          if (!user.systemPrivileges) {
            if (!dryRun) {
              user.systemPrivileges = [];
              updated = true;
            }
            logger.debug('Added systemPrivileges array to user', {
              user_id: user._id,
              user_email: user.email
            });
          }

          // Update roles format if needed
          if (user.roles && user.roles.length > 0) {
            for (const role of user.roles) {
              if (!role.assignedAt) {
                if (!dryRun) {
                  role.assignedAt = new Date();
                  updated = true;
                }
              }
            }
          }

          // Initialize security object if not exists
          if (!user.security) {
            if (!dryRun) {
              user.security = {
                failedLoginAttempts: 0,
                mfaEnabled: false
              };
              updated = true;
            }
          }

          // Initialize profile object if not exists
          if (!user.profile) {
            if (!dryRun) {
              user.profile = {
                timezone: 'UTC',
                language: 'en'
              };
              updated = true;
            }
          }

          if (updated && !dryRun) {
            await user.save();
            results.users_updated++;
          } else if (dryRun && updated) {
            results.users_updated++;
          }

        } catch (error) {
          logger.error('Error updating user', error, {
            user_id: user._id,
            user_email: user.email
          });
          results.errors.push(`User ${user._id}: ${error.message}`);
        }
      }

      // Step 2: Create system roles for all organizations
      logger.info('Step 2: Creating system roles for organizations');
      const organizations = await Organization.find({});
      
      for (const org of organizations) {
        try {
          if (!dryRun) {
            const roleResult = await privilegeManager.initializeSystemRoles(org._id);
            if (roleResult.success) {
              results.roles_created += roleResult.data.created_roles.length;
            } else {
              results.errors.push(`Org ${org._id}: ${roleResult.error.message}`);
            }
          } else {
            // In dry run, just count what would be created
            const systemRoles = Role.getSystemRoles();
            const existingRoles = await Role.find({ org: org._id });
            const existingRoleNames = existingRoles.map(r => r.name);
            const newRoles = systemRoles.filter(r => !existingRoleNames.includes(r.name));
            results.roles_created += newRoles.length;
          }
          
          results.organizations_processed++;

        } catch (error) {
          logger.error('Error creating system roles for organization', error, {
            org_id: org._id,
            org_name: org.name
          });
          results.errors.push(`Org ${org._id}: ${error.message}`);
        }
      }

      // Step 3: Assign God Super User if specified
      if (godSuperUserEmail) {
        logger.info('Step 3: Assigning God Super User');
        try {
          const godUser = await User.findOne({ email: godSuperUserEmail.toLowerCase() });
          if (godUser) {
            if (!dryRun) {
              const godResult = await privilegeManager.assignGodSuperUser(
                godUser._id,
                'system',
                {
                  allowedIPs: ['*'],
                  requireMFA: true,
                  sessionTimeout: 60
                }
              );
              
              if (godResult.success) {
                results.god_super_user_assigned = true;
                logger.info('God Super User assigned successfully', {
                  user_id: godUser._id,
                  user_email: godUser.email
                });
              } else {
                results.errors.push(`God Super User assignment: ${godResult.error.message}`);
              }
            } else {
              results.god_super_user_assigned = true;
              logger.info('Would assign God Super User (dry run)', {
                user_email: godSuperUserEmail
              });
            }
          } else {
            results.errors.push(`God Super User email not found: ${godSuperUserEmail}`);
          }
        } catch (error) {
          logger.error('Error assigning God Super User', error, {
            god_super_user_email: godSuperUserEmail
          });
          results.errors.push(`God Super User assignment: ${error.message}`);
        }
      }

      // Step 4: Update existing role assignments to use new role IDs
      logger.info('Step 4: Updating existing role assignments');
      const usersWithRoles = await User.find({ 'roles.0': { $exists: true } });
      
      for (const user of usersWithRoles) {
        try {
          let updated = false;
          
          for (const userRole of user.roles) {
            // If role is stored as string, convert to ObjectId reference
            if (typeof userRole.role === 'string') {
              const roleDoc = await Role.findOne({
                org: userRole.org,
                name: userRole.role
              });
              
              if (roleDoc) {
                if (!dryRun) {
                  userRole.role = roleDoc._id;
                  updated = true;
                }
                logger.debug('Updated role reference', {
                  user_id: user._id,
                  org_id: userRole.org,
                  role_name: userRole.role,
                  role_id: roleDoc._id
                });
              }
            }
          }
          
          if (updated && !dryRun) {
            await user.save();
          }

        } catch (error) {
          logger.error('Error updating user role assignments', error, {
            user_id: user._id,
            user_email: user.email
          });
          results.errors.push(`User role update ${user._id}: ${error.message}`);
        }
      }

      logger.info('Hierarchical privileges migration completed', {
        component: 'migration',
        operation: 'hierarchical_privileges_up',
        correlation_id: correlationId,
        metadata: {
          migration_name: this.migrationName,
          version: this.version,
          dry_run: dryRun,
          results
        }
      });

      return {
        success: true,
        migration: this.migrationName,
        version: this.version,
        results
      };

    } catch (error) {
      logger.error('Hierarchical privileges migration failed', error, {
        component: 'migration',
        operation: 'hierarchical_privileges_up',
        correlation_id: correlationId,
        metadata: {
          migration_name: this.migrationName,
          version: this.version
        }
      });

      return {
        success: false,
        migration: this.migrationName,
        version: this.version,
        error: error.message
      };
    }
  }

  /**
   * Rollback the migration (if needed)
   */
  async down() {
    const correlationId = logger.generateCorrelationId();
    
    logger.warn('Rolling back hierarchical privileges migration', {
      component: 'migration',
      operation: 'hierarchical_privileges_down',
      correlation_id: correlationId,
      metadata: {
        migration_name: this.migrationName,
        version: this.version
      }
    });

    // Note: This is a destructive operation and should be used carefully
    // In production, consider backing up data before rollback

    try {
      // Remove systemPrivileges from all users
      await User.updateMany(
        {},
        { $unset: { systemPrivileges: 1, security: 1, profile: 1 } }
      );

      // Remove system roles (keep custom roles)
      await Role.deleteMany({
        'hierarchy.type': 'system'
      });

      logger.info('Hierarchical privileges migration rolled back successfully', {
        component: 'migration',
        operation: 'hierarchical_privileges_down',
        correlation_id: correlationId
      });

      return {
        success: true,
        migration: this.migrationName,
        version: this.version,
        action: 'rollback_completed'
      };

    } catch (error) {
      logger.error('Hierarchical privileges migration rollback failed', error, {
        component: 'migration',
        operation: 'hierarchical_privileges_down',
        correlation_id: correlationId
      });

      return {
        success: false,
        migration: this.migrationName,
        version: this.version,
        error: error.message
      };
    }
  }
}

module.exports = HierarchicalPrivilegesMigration;
