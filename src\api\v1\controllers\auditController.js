//src/api/v1/controllers/auditController.js
const auditDatabase = require('../../../services/auditDatabase');
const logger = require('../../../services/logger');

/**
 * Audit Log Controller
 * Handles audit log querying and management
 */
class AuditController {

  /**
   * Query audit logs with filtering and pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async queryAuditLogs(req, res) {
    try {
      const auditConnection = auditDatabase.getConnection();

      if (!auditConnection) {
        return res.status(503).json({
          success: false,
          message: 'Audit database not available',
          error: {
            code: 'AUDIT_DB_UNAVAILABLE',
            details: 'Audit logging database is currently not accessible'
          }
        });
      }

      // Get AuditLog model from audit connection
      const AuditLog = auditConnection.model('AuditLog', require('../../../models/AuditLog').schema);

      // Parse and validate query parameters
      const {
        page = 1,
        limit = 50,
        start_date,
        end_date,
        user_id,
        user_email,
        operation_type,
        resource_type,
        response_status,
        organization_id,
        endpoint,
        sort_by = 'timestamp',
        sort_order = 'desc'
      } = req.query;

      // Validate pagination parameters
      const pageNum = Math.max(1, parseInt(page));
      const limitNum = Math.min(1000, Math.max(1, parseInt(limit)));
      const skip = (pageNum - 1) * limitNum;

      // Build query filter
      const filter = {};

      // Date range filter
      if (start_date || end_date) {
        filter.timestamp = {};
        if (start_date) {
          filter.timestamp.$gte = new Date(start_date);
        }
        if (end_date) {
          filter.timestamp.$lte = new Date(end_date);
        }
      }

      // User filters
      if (user_id) filter.user_id = user_id;
      if (user_email) filter.user_email = new RegExp(user_email, 'i');

      // Operation filters
      if (operation_type) filter.operation_type = operation_type;
      if (resource_type) filter.resource_type = resource_type;
      if (response_status) filter.response_status = parseInt(response_status);
      if (endpoint) filter.endpoint = new RegExp(endpoint, 'i');

      // Organization context filter
      if (organization_id) {
        filter['metadata.organization_context.organization_id'] = organization_id;
      }

      // Build sort options
      const sortOptions = {};
      sortOptions[sort_by] = sort_order === 'asc' ? 1 : -1;

      // Execute query with pagination
      const [logs, total] = await Promise.all([
        AuditLog.find(filter)
          .sort(sortOptions)
          .skip(skip)
          .limit(limitNum)
          .lean(),
        AuditLog.countDocuments(filter)
      ]);

      // Sanitize logs for response
      const sanitizedLogs = logs.map(log => {
        const sanitized = { ...log };

        // Remove sensitive data
        delete sanitized.request_data?.password;
        delete sanitized.response_data?.access_token;
        delete sanitized.response_data?.refresh_token;
        delete sanitized.metadata?.error_details?.stack_trace;

        return sanitized;
      });

      const totalPages = Math.ceil(total / limitNum);

      logger.info('Audit logs queried', {
        component: 'audit-controller',
        operation: 'query_audit_logs',
        metadata: {
          user_id: req.user._id,
          filter_applied: Object.keys(filter),
          results_count: logs.length,
          total_records: total,
          page: pageNum,
          limit: limitNum
        }
      });

      res.json({
        success: true,
        data: sanitizedLogs,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: totalPages,
          has_next: pageNum < totalPages,
          has_prev: pageNum > 1
        },
        metadata: {
          query_executed_at: new Date().toISOString(),
          filter_summary: {
            date_range: start_date || end_date ? { start_date, end_date } : null,
            user_filter: user_id || user_email || null,
            operation_filter: operation_type || null,
            resource_filter: resource_type || null,
            organization_filter: organization_id || null
          }
        }
      });

    } catch (error) {
      logger.error('Failed to query audit logs', error, {
        component: 'audit-controller',
        operation: 'query_audit_logs_error',
        metadata: {
          user_id: req.user._id,
          query_params: req.query
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve audit logs',
        error: {
          code: 'AUDIT_QUERY_ERROR',
          details: 'An error occurred while querying audit logs'
        }
      });
    }
  }

  /**
   * Get specific audit log entry by operation ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAuditLogEntry(req, res) {
    try {
      const { operation_id } = req.params;

      const auditConnection = auditDatabase.getConnection();
      if (!auditConnection) {
        return res.status(503).json({
          success: false,
          message: 'Audit database not available'
        });
      }

      const AuditLog = auditConnection.model('AuditLog', require('../../../models/AuditLog').schema);

      const log = await AuditLog.findOne({ operation_id }).lean();

      if (!log) {
        return res.status(404).json({
          success: false,
          message: 'Audit log entry not found',
          error: {
            code: 'AUDIT_LOG_NOT_FOUND',
            details: `No audit log found with operation ID: ${operation_id}`
          }
        });
      }

      // Sanitize for response
      const sanitized = { ...log };
      delete sanitized.request_data?.password;
      delete sanitized.response_data?.access_token;
      delete sanitized.response_data?.refresh_token;

      logger.info('Audit log entry retrieved', {
        component: 'audit-controller',
        operation: 'get_audit_log_entry',
        metadata: {
          operation_id,
          requested_by: req.user._id
        }
      });

      res.json({
        success: true,
        data: sanitized,
        metadata: {
          retrieved_at: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to retrieve audit log entry', error, {
        component: 'audit-controller',
        operation: 'get_audit_log_entry_error',
        metadata: {
          operation_id: req.params.operation_id,
          requested_by: req.user._id
        }
      });

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve audit log entry',
        error: {
          code: 'AUDIT_RETRIEVAL_ERROR',
          details: 'An error occurred while retrieving the audit log entry'
        }
      });
    }
  }

  /**
   * Get audit log statistics and summary
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAuditStatistics(req, res) {
    try {
      const auditConnection = auditDatabase.getConnection();
      if (!auditConnection) {
        return res.status(503).json({
          success: false,
          message: 'Audit database not available'
        });
      }

      const AuditLog = auditConnection.model('AuditLog', require('../../../models/AuditLog').schema);

      const { start_date, end_date } = req.query;

      // Build date filter
      const dateFilter = {};
      if (start_date || end_date) {
        dateFilter.timestamp = {};
        if (start_date) dateFilter.timestamp.$gte = new Date(start_date);
        if (end_date) dateFilter.timestamp.$lte = new Date(end_date);
      }

      // Aggregate statistics
      const [
        totalLogs,
        operationTypeStats,
        resourceTypeStats,
        statusCodeStats,
        topUsers,
        topEndpoints
      ] = await Promise.all([
        AuditLog.countDocuments(dateFilter),

        AuditLog.aggregate([
          { $match: dateFilter },
          { $group: { _id: '$operation_type', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),

        AuditLog.aggregate([
          { $match: dateFilter },
          { $group: { _id: '$resource_type', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),

        AuditLog.aggregate([
          { $match: dateFilter },
          { $group: { _id: '$response_status', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),

        AuditLog.aggregate([
          { $match: { ...dateFilter, user_email: { $exists: true, $ne: null } } },
          { $group: { _id: '$user_email', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]),

        AuditLog.aggregate([
          { $match: dateFilter },
          { $group: { _id: '$endpoint', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ])
      ]);

      res.json({
        success: true,
        data: {
          summary: {
            total_logs: totalLogs,
            date_range: {
              start: start_date || null,
              end: end_date || null
            }
          },
          operation_types: operationTypeStats,
          resource_types: resourceTypeStats,
          status_codes: statusCodeStats,
          top_users: topUsers,
          top_endpoints: topEndpoints
        },
        metadata: {
          generated_at: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to generate audit log statistics', error, {
        component: 'audit-controller',
        operation: 'get_audit_stats_error'
      });

      res.status(500).json({
        success: false,
        message: 'Failed to generate audit statistics'
      });
    }
  }

  /**
   * Get audit logs for specific user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getUserAuditLogs(req, res) {
    try {
      const { userIdOrEmail } = req.params;
      const { page = 1, limit = 50, start_date, end_date } = req.query;

      const auditConnection = auditDatabase.getConnection();
      if (!auditConnection) {
        return res.status(503).json({
          success: false,
          message: 'Audit database not available'
        });
      }

      const AuditLog = auditConnection.model('AuditLog', require('../../../models/AuditLog').schema);

      // Build filter for user
      const filter = {
        $or: [
          { user_id: userIdOrEmail },
          { user_email: userIdOrEmail }
        ]
      };

      // Add date filter if provided
      if (start_date || end_date) {
        filter.timestamp = {};
        if (start_date) filter.timestamp.$gte = new Date(start_date);
        if (end_date) filter.timestamp.$lte = new Date(end_date);
      }

      const pageNum = Math.max(1, parseInt(page));
      const limitNum = Math.min(1000, Math.max(1, parseInt(limit)));
      const skip = (pageNum - 1) * limitNum;

      const [logs, total] = await Promise.all([
        AuditLog.find(filter)
          .sort({ timestamp: -1 })
          .skip(skip)
          .limit(limitNum)
          .lean(),
        AuditLog.countDocuments(filter)
      ]);

      // Sanitize logs
      const sanitizedLogs = logs.map(log => {
        const sanitized = { ...log };
        delete sanitized.request_data?.password;
        delete sanitized.response_data?.access_token;
        return sanitized;
      });

      res.json({
        success: true,
        data: sanitizedLogs,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });

    } catch (error) {
      logger.error('Failed to retrieve user audit logs', error, {
        component: 'audit-controller',
        operation: 'get_user_audit_logs_error'
      });

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve user audit logs'
      });
    }
  }
}

module.exports = new AuditController();
