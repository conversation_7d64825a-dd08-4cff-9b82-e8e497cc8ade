#!/bin/bash

# Script to switch to local development environment

echo "🔄 Switching to LOCAL development environment..."

# Copy local environment file to .env
if [ -f ".env.local" ]; then
    cp .env.local .env
    echo "✅ Copied .env.local to .env"
    echo "🏠 Now using LOCAL development configuration"
    echo ""
    echo "📋 Local Environment Settings:"
    echo "   - Server: http://localhost:3000"
    echo "   - Database: dev_db"
    echo "   - CORS: Permissive for localhost"
    echo "   - HTTPS: Disabled"
    echo "   - Subdomain routing: Disabled"
    echo ""
    echo "🚀 You can now run: npm start"
else
    echo "❌ Error: .env.local file not found!"
    exit 1
fi
