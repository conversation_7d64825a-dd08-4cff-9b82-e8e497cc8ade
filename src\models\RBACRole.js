// models/RBACRole.js
const mongoose = require('mongoose');

/**
 * Dedicated RBAC Roles Database - Centralized Source of Truth
 *
 * This collection serves as the authoritative source for all role definitions,
 * permissions, and hierarchical relationships in the system.
 */

// Permission schema for granular access control
const permissionSchema = new mongoose.Schema({
  resource: {
    type: String,
    required: true,
    enum: [
      // Wildcard for super users
      '*',
      // Core resources
      'user_object', 'org_object', 'role_object',
      // Business resources
      'event', 'ticket', 'auth_prev', 'audit_log',
      'settings', 'billing', 'analytics', 'integration',
      'notification', 'file_storage', 'workflow', 'api_access',
      // Legacy compatibility
      'user', 'org', 'role', 'config'
    ]
  },
  actions: [{
    type: String,
    required: true,
    enum: [
      'create', 'read', 'update', 'delete', 'assign',
      'approve', 'export', 'import', 'configure', 'monitor',
      'audit', 'backup', 'restore', 'publish', 'moderate', '*'
    ]
  }],
  constraints: {
    scope: {
      type: String,
      enum: ['self', 'team', 'department', 'organization', 'system'],
      default: 'organization'
    },
    conditions: {
      timeRestrictions: {
        allowedHours: {
          start: { type: Number, min: 0, max: 23 },
          end: { type: Number, min: 0, max: 23 }
        },
        allowedDays: [{ type: Number, min: 0, max: 6 }], // 0=Sunday, 6=Saturday
        timezone: { type: String, default: 'UTC' }
      },
      resourceLimits: {
        maxItems: Number,
        maxFileSize: Number,
        allowedFileTypes: [String],
        rateLimits: {
          requestsPerHour: Number,
          requestsPerDay: Number
        }
      },
      ipRestrictions: [String],
      requireMFA: { type: Boolean, default: false }
    }
  }
}, { _id: false });

// Hierarchy schema for role relationships
const hierarchySchema = new mongoose.Schema({
  level: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['system', 'organization', 'custom', 'temporary'],
    index: true
  },
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RBACRole',
    default: null
  },
  children: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RBACRole'
  }],
  inheritPermissions: {
    type: Boolean,
    default: true
  }
}, { _id: false });

// Audit schema for role usage tracking
const auditSchema = new mongoose.Schema({
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  usageCount: {
    type: Number,
    default: 0
  },
  lastUsed: Date,
  changeHistory: [{
    action: {
      type: String,
      enum: ['created', 'updated', 'deleted', 'assigned', 'unassigned']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    changes: mongoose.Schema.Types.Mixed,
    reason: String
  }]
}, { _id: false });

// Main RBAC Role schema
const rbacRoleSchema = new mongoose.Schema({
  // Basic role information
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  displayName: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },

  // Scope and organization
  scope: {
    type: String,
    required: true,
    enum: ['system', 'organization', 'global'],
    default: 'organization',
    index: true
  },
  organization: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: function() { return this.scope === 'organization'; },
    index: true
  },

  // Role hierarchy and relationships
  hierarchy: {
    type: hierarchySchema,
    required: true
  },

  // Permissions and access control
  permissions: [permissionSchema],

  // Role metadata
  metadata: {
    isActive: {
      type: Boolean,
      default: true,
      index: true
    },
    isSystem: {
      type: Boolean,
      default: false,
      index: true
    },
    isBuiltIn: {
      type: Boolean,
      default: false
    },
    category: {
      type: String,
      enum: ['administrative', 'operational', 'viewer', 'custom'],
      default: 'custom'
    },
    tags: [String],
    expiresAt: Date
  },

  // Audit and tracking
  audit: auditSchema

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance optimization
rbacRoleSchema.index({ scope: 1, organization: 1, 'metadata.isActive': 1 });
rbacRoleSchema.index({ name: 1, scope: 1, organization: 1 }, { unique: true });
rbacRoleSchema.index({ 'hierarchy.level': 1, 'hierarchy.type': 1 });
rbacRoleSchema.index({ 'metadata.isSystem': 1, 'metadata.isActive': 1 });
rbacRoleSchema.index({ 'permissions.resource': 1, 'permissions.actions': 1 });

// Virtual for effective permissions (including inherited)
rbacRoleSchema.virtual('effectivePermissions').get(function() {
  // This will be populated by the RBAC engine
  return this._effectivePermissions || this.permissions;
});

// Instance methods
rbacRoleSchema.methods.hasPermission = function(resource, action) {
  return this.permissions.some(permission => {
    return permission.resource === resource &&
           (permission.actions.includes(action) || permission.actions.includes('*'));
  });
};

rbacRoleSchema.methods.isHigherThan = function(otherRole) {
  return this.hierarchy.level < otherRole.hierarchy.level;
};

rbacRoleSchema.methods.getHierarchyLevel = function() {
  return this.hierarchy.level;
};

rbacRoleSchema.methods.updateUsage = function() {
  this.audit.usageCount = (this.audit.usageCount || 0) + 1;
  this.audit.lastUsed = new Date();
  return this.save();
};

// Static methods for role management
rbacRoleSchema.statics.getSystemRoles = function() {
  return [
    {
      name: 'god_super_user',
      displayName: 'God Super User',
      description: 'System-wide unrestricted access',
      scope: 'system',
      hierarchy: { level: 0, type: 'system' },
      permissions: [{ resource: '*', actions: ['*'] }],
      metadata: { isSystem: true, isBuiltIn: true, category: 'administrative' }
    },
    {
      name: 'org_super_user',
      displayName: 'Organization Super User',
      description: 'Complete access within organization',
      scope: 'organization',
      hierarchy: { level: 1, type: 'organization' },
      permissions: [{ resource: '*', actions: ['*'] }],
      metadata: { isSystem: true, isBuiltIn: true, category: 'administrative' }
    },
    {
      name: 'view_manager',
      displayName: 'View Manager',
      description: 'Read-only access to all organization data',
      scope: 'organization',
      hierarchy: { level: 2, type: 'organization' },
      permissions: [
        { resource: 'user_object', actions: ['read'] },
        { resource: 'org_object', actions: ['read'] },
        { resource: 'role_object', actions: ['read'] },
        { resource: 'event', actions: ['read'] },
        { resource: 'ticket', actions: ['read'] },
        { resource: 'audit_log', actions: ['read'] }
      ],
      metadata: { isSystem: true, isBuiltIn: true, category: 'viewer' }
    },
    {
      name: 'admin',
      displayName: 'Administrator',
      description: 'Create, read, update permissions with limited delete',
      scope: 'organization',
      hierarchy: { level: 3, type: 'organization' },
      permissions: [
        { resource: 'user_object', actions: ['create', 'read', 'update'] },
        { resource: 'org_object', actions: ['read', 'update'] },
        { resource: 'role_object', actions: ['read', 'assign'] },
        { resource: 'event', actions: ['create', 'read', 'update'] },
        { resource: 'ticket', actions: ['create', 'read', 'update'] }
      ],
      metadata: { isSystem: true, isBuiltIn: true, category: 'administrative' }
    },
    {
      name: 'manager',
      displayName: 'Manager',
      description: 'Mid-level access with read and update for specific resources',
      scope: 'organization',
      hierarchy: { level: 4, type: 'organization' },
      permissions: [
        { resource: 'user_object', actions: ['read'] },
        { resource: 'event', actions: ['create', 'read', 'update'] },
        { resource: 'ticket', actions: ['create', 'read', 'update'] }
      ],
      metadata: { isSystem: true, isBuiltIn: true, category: 'operational' }
    },
    {
      name: 'member',
      displayName: 'Member',
      description: 'Basic user access with read and limited update',
      scope: 'organization',
      hierarchy: { level: 5, type: 'organization' },
      permissions: [
        { resource: 'user_object', actions: ['read'], constraints: { scope: 'self' } },
        { resource: 'event', actions: ['read'] },
        { resource: 'ticket', actions: ['create', 'read'] }
      ],
      metadata: { isSystem: true, isBuiltIn: true, category: 'operational' }
    },
    {
      name: 'guest',
      displayName: 'Guest',
      description: 'Minimal read-only access to public resources',
      scope: 'organization',
      hierarchy: { level: 6, type: 'organization' },
      permissions: [
        { resource: 'event', actions: ['read'], constraints: { scope: 'organization' } }
      ],
      metadata: { isSystem: true, isBuiltIn: true, category: 'viewer' }
    }
  ];
};

rbacRoleSchema.statics.findByNameAndScope = function(name, scope, organization = null) {
  const query = { name, scope, 'metadata.isActive': true };
  if (scope === 'organization' && organization) {
    query.organization = organization;
  }
  return this.findOne(query);
};

rbacRoleSchema.statics.getHierarchicalRoles = function(organization = null) {
  const query = { 'metadata.isActive': true };
  if (organization) {
    query.$or = [
      { scope: 'system' },
      { scope: 'global' },
      { scope: 'organization', organization }
    ];
  }
  return this.find(query).sort({ 'hierarchy.level': 1 });
};

module.exports = mongoose.models.RBACRole || mongoose.model('RBACRole', rbacRoleSchema);
