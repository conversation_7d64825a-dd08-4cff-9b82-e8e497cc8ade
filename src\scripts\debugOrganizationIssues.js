#!/usr/bin/env node

/**
 * Debug Script: Organization Endpoints Issues
 * 
 * This script helps debug why organization endpoints are returning "Organization not found"
 * when the user clearly has organizations in their profile.
 * 
 * Usage: node src/scripts/debugOrganizationIssues.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Organization = require('../models/Organization');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Debug user data
const debugUserData = async (userId) => {
  console.log(`\n🔍 Debugging user: ${userId}`);
  
  try {
    const user = await User.findById(userId)
      .populate('roles.org')
      .populate('roles.role');
    
    if (!user) {
      console.log('❌ User not found');
      return null;
    }
    
    console.log(`👤 User: ${user.email}`);
    console.log(`🆔 User ID: ${user._id}`);
    console.log(`🔥 Firebase UID: ${user.firebase_uid}`);
    
    console.log('\n📋 User Roles:');
    if (user.roles && user.roles.length > 0) {
      user.roles.forEach((role, index) => {
        console.log(`   ${index + 1}. Org: ${role.org?.name || 'Unknown'} (${role.org?._id})`);
        console.log(`      Role: ${role.role?.name || 'Unknown'}`);
        console.log(`      Assigned: ${role.assignedAt}`);
      });
    } else {
      console.log('   No roles found');
    }
    
    console.log('\n🏢 Testing getOrganizationsWithRoles method:');
    try {
      const organizationsWithRoles = await user.getOrganizationsWithRoles();
      console.log(`   Found ${organizationsWithRoles.length} organizations`);
      organizationsWithRoles.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.organization.name} (${item.organization._id})`);
        console.log(`      Role: ${item.role}`);
        console.log(`      Status: ${item.status}`);
      });
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    return user;
  } catch (error) {
    console.error('❌ Error debugging user:', error);
    return null;
  }
};

// Debug organization data
const debugOrganizationData = async (orgId) => {
  console.log(`\n🏢 Debugging organization: ${orgId}`);
  
  try {
    const org = await Organization.findById(orgId);
    
    if (!org) {
      console.log('❌ Organization not found');
      return null;
    }
    
    console.log(`🏢 Organization: ${org.name}`);
    console.log(`🆔 Org ID: ${org._id}`);
    console.log(`🌐 Subdomain: ${org.subdomain}`);
    console.log(`📊 Status: ${org.status}`);
    console.log(`👥 Members count: ${org.members?.length || 0}`);
    
    console.log('\n👥 Organization Members:');
    if (org.members && org.members.length > 0) {
      for (const member of org.members) {
        const user = await User.findById(member.user);
        console.log(`   - ${user?.email || 'Unknown'} (${member.user})`);
        console.log(`     Role: ${member.role}`);
        console.log(`     Status: ${member.status}`);
        console.log(`     Joined: ${member.joinedAt}`);
      }
    } else {
      console.log('   No members found');
    }
    
    return org;
  } catch (error) {
    console.error('❌ Error debugging organization:', error);
    return null;
  }
};

// Test the specific user from the API response
const testSpecificUser = async () => {
  console.log('\n🎯 Testing specific user from API response...');
  
  // This is the user ID from the /users/me response
  const userId = '68330577e2576bff416a2ae7';
  const orgId = '6838bfd6278ccc88e4179049';
  
  const user = await debugUserData(userId);
  const org = await debugOrganizationData(orgId);
  
  if (user && org) {
    console.log('\n🔄 Checking bidirectional relationship:');
    
    // Check if user is in organization members
    const userInOrgMembers = org.members?.find(m => m.user.toString() === userId);
    console.log(`   User in org.members: ${userInOrgMembers ? '✅' : '❌'}`);
    
    // Check if organization is in user roles
    const orgInUserRoles = user.roles?.find(r => r.org && r.org._id.toString() === orgId);
    console.log(`   Org in user.roles: ${orgInUserRoles ? '✅' : '❌'}`);
    
    if (userInOrgMembers && !orgInUserRoles) {
      console.log('\n⚠️  ISSUE FOUND: User is in organization members but not in user roles!');
      console.log('   This explains why getMyOrganizations might be failing.');
      console.log('   Run the user-organization sync fix script.');
    } else if (!userInOrgMembers && orgInUserRoles) {
      console.log('\n⚠️  ISSUE FOUND: Organization is in user roles but user not in organization members!');
      console.log('   This is the opposite problem.');
    } else if (!userInOrgMembers && !orgInUserRoles) {
      console.log('\n❌ MAJOR ISSUE: No bidirectional relationship found!');
    } else {
      console.log('\n✅ Bidirectional relationship looks correct.');
    }
  }
};

// Test the controller methods directly
const testControllerMethods = async () => {
  console.log('\n🧪 Testing controller methods directly...');
  
  const userId = '68330577e2576bff416a2ae7';
  const user = await User.findById(userId);
  
  if (!user) {
    console.log('❌ User not found for controller testing');
    return;
  }
  
  const OrganizationController = require('../api/v1/controllers/organizationController');
  const controller = new OrganizationController();
  
  // Test getMyOrganizations
  console.log('\n📋 Testing getMyOrganizations controller method...');
  
  const mockReq = {
    user: { uid: user.firebase_uid, _id: user._id }
  };
  
  let responseData = null;
  let statusCode = null;
  
  const mockRes = {
    json: (data) => {
      responseData = data;
      console.log('📤 Response:', JSON.stringify(data, null, 2));
      return mockRes;
    },
    status: (code) => {
      statusCode = code;
      console.log(`📊 Status: ${code}`);
      return mockRes;
    }
  };
  
  try {
    await controller.getMyOrganizations(mockReq, mockRes);
    
    if (statusCode === 200 && responseData) {
      console.log('✅ getMyOrganizations worked correctly');
    } else {
      console.log(`❌ getMyOrganizations failed with status ${statusCode}`);
    }
  } catch (error) {
    console.log(`❌ getMyOrganizations threw error: ${error.message}`);
  }
};

// Check database consistency
const checkDatabaseConsistency = async () => {
  console.log('\n🔍 Checking database consistency...');
  
  const totalUsers = await User.countDocuments();
  const totalOrgs = await Organization.countDocuments();
  const usersWithRoles = await User.countDocuments({ 'roles.0': { $exists: true } });
  const orgsWithMembers = await Organization.countDocuments({ 'members.0': { $exists: true } });
  
  console.log(`📊 Database Stats:`);
  console.log(`   Total Users: ${totalUsers}`);
  console.log(`   Total Organizations: ${totalOrgs}`);
  console.log(`   Users with Roles: ${usersWithRoles}`);
  console.log(`   Organizations with Members: ${orgsWithMembers}`);
  
  // Find inconsistencies
  const organizations = await Organization.find({});
  let inconsistencies = 0;
  
  for (const org of organizations) {
    for (const member of org.members || []) {
      const user = await User.findById(member.user);
      if (user) {
        const hasRole = user.roles?.some(r => 
          r.org && r.org.toString() === org._id.toString()
        );
        if (!hasRole) {
          inconsistencies++;
          console.log(`⚠️  Inconsistency: ${user.email} in ${org.name} members but no role`);
        }
      }
    }
  }
  
  if (inconsistencies === 0) {
    console.log('✅ No inconsistencies found');
  } else {
    console.log(`❌ Found ${inconsistencies} inconsistencies`);
  }
};

// Main execution function
const main = async () => {
  console.log('🚀 Starting Organization Issues Debug...');
  
  try {
    await connectDB();
    
    await testSpecificUser();
    await testControllerMethods();
    await checkDatabaseConsistency();
    
    console.log('\n🎉 Debug completed!');
    console.log('\n💡 Recommendations:');
    console.log('   1. If inconsistencies found, run: node src/scripts/fixUserOrganizationSync.js');
    console.log('   2. Check server logs for more detailed error information');
    console.log('   3. Test endpoints again after running sync fix');
    
  } catch (error) {
    console.error('\n❌ Debug failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n👋 Database connection closed');
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, debugUserData, debugOrganizationData, testSpecificUser };
