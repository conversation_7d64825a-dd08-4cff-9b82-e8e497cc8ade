// api/index.js
require('dotenv').config();
const serverless = require('serverless-http');
const app        = require('../src/index'); 
const mongoose   = require('mongoose');


// ensure mongoose connection in serverless environment
let conn = null;
async function ensureDb() {
  if (conn) return conn;
  conn = mongoose.connect(process.env.MONGO_URI);
  return conn;
}

module.exports = async (req, res) => {
  await ensureDb();
  return serverless(app)(req, res);
};
