// src/config/productionConfig.js
const session = require('express-session');
const MongoStore = require('connect-mongo');
const cookieParser = require('cookie-parser');

/**
 * Production Configuration for SSL, Sessions, and Security
 * Handles domain-specific settings for digimeet.live
 */
class ProductionConfig {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.rootDomain = process.env.ROOT_DOMAIN || 'digimeet.live';
    this.cookieDomain = process.env.COOKIE_DOMAIN || '.digimeet.live';
    this.sessionDomain = process.env.SESSION_DOMAIN || '.digimeet.live';
    this.forceHttps = process.env.FORCE_HTTPS === 'true';
    this.secureCookies = process.env.SECURE_COOKIES === 'true';
    this.trustProxy = process.env.TRUST_PROXY === 'true';
  }

  /**
   * Configure Express app for production
   */
  configureApp(app) {
    // Trust proxy for SSL termination (important for Vercel/Cloudflare)
    if (this.trustProxy) {
      app.set('trust proxy', 1);
    }

    // Force HTTPS redirect middleware
    if (this.forceHttps && this.isProduction) {
      app.use(this.httpsRedirectMiddleware());
    }

    // Security headers middleware
    app.use(this.securityHeadersMiddleware());

    // Cookie parser with domain configuration
    app.use(cookieParser());

    // Session configuration for cross-subdomain support
    if (this.isProduction) {
      app.use(this.getSessionConfig());
    }

    return app;
  }

  /**
   * HTTPS redirect middleware
   */
  httpsRedirectMiddleware() {
    return (req, res, next) => {
      // Skip for OPTIONS requests (CORS preflight)
      if (req.method === 'OPTIONS') {
        return next();
      }

      // Check if request is not secure
      if (!req.secure && req.get('x-forwarded-proto') !== 'https') {
        // Redirect to HTTPS
        const httpsUrl = `https://${req.get('host')}${req.url}`;
        return res.redirect(301, httpsUrl);
      }
      next();
    };
  }

  /**
   * Security headers middleware
   */
  securityHeadersMiddleware() {
    return (req, res, next) => {
      // Security headers
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

      // HSTS (HTTP Strict Transport Security)
      if (this.isProduction) {
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
      }

      // Content Security Policy
      res.setHeader('Content-Security-Policy', this.getCSPHeader());

      next();
    };
  }

  /**
   * Get Content Security Policy header
   */
  getCSPHeader() {
    // Get Auth0 domain for CSP
    const auth0Domain = process.env.AUTH0_ISSUER_BASE_URL || '';
    const auth0Host = auth0Domain.replace('https://', '');

    // Development vs Production CSP
    const connectSrc = [
      "'self'",
      `https://${this.rootDomain}`,
      `https://*.${this.rootDomain}`,
      'https://accounts.google.com',
      'https://oauth2.googleapis.com',
      'https://www.googleapis.com',
      'https://validator.swagger.io'
    ];

    // Add Auth0 domain if configured
    if (auth0Host) {
      connectSrc.push(`https://${auth0Host}`);
    }

    // Add development origins if not in production
    if (!this.isProduction) {
      connectSrc.push('http://localhost:*', 'http://127.0.0.1:*');
    }

    return [
      "default-src 'self'",
      `connect-src ${connectSrc.join(' ')}`,
      `script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://unpkg.com ${auth0Host ? `https://${auth0Host}` : ''}`,
      `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com`,
      `font-src 'self' https://fonts.gstatic.com`,
      `img-src 'self' data: https://${this.rootDomain} https://*.${this.rootDomain} https://www.googleapis.com https://validator.swagger.io`,
      `frame-src 'self' https://accounts.google.com ${auth0Host ? `https://${auth0Host}` : ''}`,
      "object-src 'none'",
      "base-uri 'self'",
      `report-uri https://${this.rootDomain}/api/v1/csp-report`
    ].join('; ');
  }

  /**
   * Session configuration for cross-subdomain support
   */
  getSessionConfig() {
    return session({
      secret: process.env.SESSION_SECRET || 'fallback-secret-change-in-production',
      name: 'digimeet.sid',
      resave: false,
      saveUninitialized: false,
      store: MongoStore.create({
        mongoUrl: process.env.MONGO_URI,
        touchAfter: 24 * 3600, // lazy session update
        ttl: 14 * 24 * 60 * 60 // 14 days
      }),
      cookie: {
        secure: this.secureCookies && this.isProduction,
        httpOnly: true,
        maxAge: 14 * 24 * 60 * 60 * 1000, // 14 days
        domain: this.sessionDomain,
        sameSite: process.env.SESSION_SAME_SITE || 'lax'
      }
    });
  }

  /**
   * Get cookie options for JWT tokens
   */
  getCookieOptions() {
    return {
      secure: this.secureCookies && this.isProduction,
      httpOnly: true,
      domain: this.cookieDomain,
      sameSite: process.env.SESSION_SAME_SITE || 'lax',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    };
  }

  /**
   * Validate domain configuration
   */
  validateConfiguration() {
    const issues = [];

    if (!process.env.ROOT_DOMAIN || process.env.ROOT_DOMAIN === 'yourdomain.com') {
      issues.push('ROOT_DOMAIN must be set to your actual domain (digimeet.live)');
    }

    if (!process.env.SESSION_SECRET || process.env.SESSION_SECRET.length < 32) {
      issues.push('SESSION_SECRET must be set and at least 32 characters long');
    }

    if (this.isProduction && !this.secureCookies) {
      issues.push('SECURE_COOKIES should be true in production');
    }

    if (this.isProduction && !this.forceHttps) {
      issues.push('FORCE_HTTPS should be true in production');
    }

    return issues;
  }

  /**
   * Get environment-specific server URL
   */
  getServerUrl(subdomain = null) {
    if (process.env.NODE_ENV === 'development') {
      return process.env.SERVER_URL || 'http://localhost:3000';
    }

    if (subdomain) {
      return `https://${subdomain}.${this.rootDomain}`;
    }

    return process.env.PRODUCTION_SERVER_URL || `https://${this.rootDomain}`;
  }

  /**
   * Check if request is from valid subdomain
   */
  isValidSubdomain(host) {
    if (!host) return false;

    // Allow localhost in development
    if (process.env.NODE_ENV === 'development' &&
        (host.includes('localhost') || host.includes('127.0.0.1'))) {
      return true;
    }

    // Check if it's our root domain or a subdomain
    return host === this.rootDomain || host.endsWith(`.${this.rootDomain}`);
  }

  /**
   * Extract subdomain from host
   */
  extractSubdomain(host) {
    if (!host || !this.isValidSubdomain(host)) {
      return null;
    }

    if (host === this.rootDomain) {
      return null; // Root domain
    }

    const parts = host.split('.');
    if (parts.length >= 3) {
      const potentialRootDomain = parts.slice(-2).join('.');
      if (potentialRootDomain === this.rootDomain) {
        return parts.slice(0, -2).join('.');
      }
    }

    return null;
  }
}

// Export singleton instance
module.exports = new ProductionConfig();
