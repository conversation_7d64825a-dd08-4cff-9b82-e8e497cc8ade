// models/Organization.js
const mongoose = require('mongoose');

// Member schema for organization members with roles
const memberSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  role: {
    type: String,
    enum: ['super_user', 'admin', 'member'],
    required: true,
    default: 'member'
  },
  joinedAt: { type: Date, default: Date.now },
  addedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending'],
    default: 'active'
  }
}, { _id: false });

// Team member invitation schema
const teamInvitationSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      },
      message: 'Invalid email format'
    }
  },
  role: {
    type: String,
    enum: ['admin', 'member'],
    default: 'member'
  },
  status: {
    type: String,
    enum: ['pending', 'sent', 'accepted', 'declined', 'expired'],
    default: 'pending'
  },
  invitedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  invitedAt: { type: Date, default: Date.now },
  expiresAt: {
    type: Date,
    default: function() {
      return new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
    }
  },
  token: { type: String }, // Invitation token for verification
}, { _id: false });

const organizationSchema = new mongoose.Schema({
  // Basic organization information
  name: { type: String, required: true, trim: true }, // Display Name
  legalName: { type: String, trim: true }, // Legal Name (Optional)
  subdomain: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    match: /^[a-z0-9-]+$/
  },

  // Contact and industry information
  contactEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      },
      message: 'Invalid contact email format'
    }
  },
  industryTag: {
    type: String,
    required: true,
    trim: true
  },

  // Organization roles/capabilities
  organizationRoles: [{
    type: String,
    enum: ['sponsor', 'organizer'],
    required: true
  }],

  // Branding and visual identity
  branding: {
    logoUrl: String,
    primaryColor: String,
  },

  // Enhanced member management
  members: [memberSchema],

  // Team member invitations
  teamInvitations: [teamInvitationSchema],

  // Organization metadata
  status: {
    type: String,
    enum: ['pending', 'active', 'inactive', 'suspended'],
    default: 'pending'
  },

  // Creation tracking
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  createdByRole: {
    type: String,
    enum: ['super_user', 'org_super_user'],
    default: 'super_user'
  },

  // Approval tracking
  approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  approvedAt: { type: Date },

  // Settings
  settings: {
    allowSelfJoin: { type: Boolean, default: false },
    requireApprovalForMembers: { type: Boolean, default: true },
    maxMembers: { type: Number, default: 1000 }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
organizationSchema.index({ subdomain: 1 }, { unique: true });
organizationSchema.index({ contactEmail: 1 });
organizationSchema.index({ industryTag: 1 });
organizationSchema.index({ organizationRoles: 1 });
organizationSchema.index({ status: 1 });
organizationSchema.index({ createdBy: 1 });
organizationSchema.index({ 'members.user': 1 });
organizationSchema.index({ 'members.role': 1 });
organizationSchema.index({ 'teamInvitations.email': 1 });
organizationSchema.index({ 'teamInvitations.status': 1 });

// Instance methods
organizationSchema.methods.getSuperUser = function() {
  return this.members.find(member => member.role === 'super_user' || member.role === 'org_super_user');
};

organizationSchema.methods.hasSuperUser = function() {
  return this.members.some(member => member.role === 'super_user' || member.role === 'org_super_user');
};

organizationSchema.methods.getMemberRole = function(userId) {
  const member = this.members.find(member =>
    member.user.toString() === userId.toString()
  );
  return member ? member.role : null;
};

organizationSchema.methods.isMember = function(userId) {
  return this.members.some(member =>
    member.user.toString() === userId.toString() &&
    member.status === 'active'
  );
};

organizationSchema.methods.addMember = async function(userId, role = 'member', addedBy = null) {
  // Check if user is already a member
  const existingMember = this.members.find(member =>
    member.user.toString() === userId.toString()
  );

  if (existingMember) {
    throw new Error('User is already a member of this organization');
  }

  // Ensure only one super_user per organization
  if ((role === 'super_user' || role === 'org_super_user') && this.hasSuperUser()) {
    throw new Error('Organization can only have one super_user');
  }

  this.members.push({
    user: userId,
    role: role,
    addedBy: addedBy,
    joinedAt: new Date(),
    status: 'active'
  });

  // Also add to user's roles for bidirectional relationship
  const User = require('./User');
  const RBACRole = require('./RBACRole');

  try {
    const user = await User.findById(userId);
    if (user) {
      // Find the appropriate role based on the member role
      let roleName;
      switch (role) {
        case 'super_user':
        case 'org_super_user':
          roleName = 'org_super_user';
          break;
        case 'admin':
          roleName = 'org_admin';
          break;
        case 'member':
        default:
          roleName = 'org_member';
          break;
      }

      const rbacRole = await RBACRole.findOne({
        name: roleName,
        scope: 'organization',
        organization: this._id
      });

      if (rbacRole) {
        // Check if user already has a role in this organization
        const existingRole = user.roles.find(r =>
          r.org && r.org.toString() === this._id.toString()
        );

        if (!existingRole) {
          user.roles.push({
            org: this._id,
            role: rbacRole._id,
            assignedAt: new Date(),
            assignedBy: addedBy
          });
          await user.save();
        }
      }
    }
  } catch (error) {
    // Log error but don't fail the operation
    console.error('Failed to update user roles when adding member:', error);
  }

  return this;
};

organizationSchema.methods.removeMember = async function(userId) {
  const memberIndex = this.members.findIndex(member =>
    member.user.toString() === userId.toString()
  );

  if (memberIndex === -1) {
    throw new Error('User is not a member of this organization');
  }

  // Prevent removing the super_user
  if (this.members[memberIndex].role === 'super_user') {
    throw new Error('Cannot remove super_user from organization');
  }

  this.members.splice(memberIndex, 1);

  // Also remove from user's roles for bidirectional relationship
  const User = require('./User');

  try {
    const user = await User.findById(userId);
    if (user) {
      // Remove the role for this organization
      const roleIndex = user.roles.findIndex(r =>
        r.org && r.org.toString() === this._id.toString()
      );

      if (roleIndex !== -1) {
        user.roles.splice(roleIndex, 1);
        await user.save();
      }
    }
  } catch (error) {
    // Log error but don't fail the operation
    console.error('Failed to update user roles when removing member:', error);
  }

  return this;
};

organizationSchema.methods.updateMemberRole = function(userId, newRole) {
  const member = this.members.find(member =>
    member.user.toString() === userId.toString()
  );

  if (!member) {
    throw new Error('User is not a member of this organization');
  }

  // Prevent changing super_user role
  if (member.role === 'super_user' || newRole === 'super_user') {
    throw new Error('Cannot change super_user role');
  }

  member.role = newRole;
  return this;
};

// Static methods
organizationSchema.statics.findByUserRole = function(userId, role) {
  return this.find({
    'members.user': userId,
    'members.role': role,
    'members.status': 'active'
  });
};

organizationSchema.statics.findPendingOrganizations = function() {
  return this.find({ status: 'pending' })
    .populate('createdBy', 'name email')
    .sort({ createdAt: -1 });
};

module.exports = mongoose.models.Organization || mongoose.model('Organization', organizationSchema);
