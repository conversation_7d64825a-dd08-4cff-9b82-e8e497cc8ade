//src/api/v1/routes/auth.js
require('dotenv').config();
const express = require('express');
const { authenticate } = require('../../../middleware/auth');
const authController = require('../controllers/authController');
const rateLimitMiddleware = require('../../../middleware/rateLimitMiddleware');
const router = express.Router();

/**
 * OAuth2 compatibility middleware
 * Maps OAuth2PasswordBearer fields (username/password) to API format (identifier/password)
 */
const oauth2CompatibilityMiddleware = (req, res, next) => {
  // Check if this is an OAuth2 request (form data with username/password)
  if (req.body && req.body.username && req.body.password && !req.body.identifier) {
    // Map OAuth2 fields to API expected format
    req.body.identifier = req.body.username;
    // password field is already correct, no mapping needed

    // Log the OAuth2 compatibility mapping
    console.log('OAuth2 compatibility: Mapped username to identifier for Swagger UI authentication');
  }
  next();
};

/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: authentication flows Login
 */

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: Log in with email or phone number (custom auth)
 *     tags: [Auth]
 *     description: |
 *       Logs in a user using their email or phone number and password. Returns user info and tokens.
 *
 *       **OAuth2 Compatibility**: This endpoint supports both direct API calls and Swagger UI OAuth2 authentication.
 *       - For direct API calls: Use JSON with `identifier` and `password` fields
 *       - For Swagger UI OAuth2: Use the "Authorize" button - enter your email/phone as username and your password
 *
 *       Note: AuthBridge is used for credential logic. User is used for profile info.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [identifier, password]
 *             properties:
 *               identifier:
 *                 type: string
 *                 description: Email address or phone number
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 description: The user's password
 *                 example: "SuperSecret123"
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required: [username, password]
 *             properties:
 *               username:
 *                 type: string
 *                 description: Email address or phone number (OAuth2 compatibility - mapped to identifier)
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 description: The user's password
 *                 example: "SuperSecret123"
 *     responses:
 *       200:
 *         description: Login successful, returns user info and tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Login successful
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                     JWT_UID:
 *                       type: string
 *                     email:
 *                       type: string
 *                     name:
 *                       type: string
 *                     phone_number:
 *                       type: string
 *                 access_token:
 *                   type: string
 *                 refresh_token:
 *                   type: string
 *                 expires_in:
 *                   type: string
 *                 token_type:
 *                   type: string
 *       400:
 *         description: Missing fields or invalid password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid password
 *       403:
 *         description: User must use a different authentication method (e.g., Firebase)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Please use a different authentication method (e.g., Web/Firebase Login).
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: User not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Custom login failed
 *                 details:
 *                   type: string
 */
router.post('/login',
  rateLimitMiddleware.getMiddleware('auth'),
  express.urlencoded({ extended: false }),
  express.json(),
  oauth2CompatibilityMiddleware,
  authController.login
);

/**
 * @swagger
 * /api/v1/auth/request-reset:
 *   post:
 *     summary: Request password reset (send reset link to email)
 *     tags: [Auth]
 *     description: "Sends a password reset link to the user's email if registered. Stateless: does not store token in DB"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Reset link sent to registered user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Reset link sent to registered user.
 *       400:
 *         description: Email required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email required
 *       404:
 *         description: User not registered
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User not registered
 */
router.post('/request-reset',
  rateLimitMiddleware.getMiddleware('passwordReset'),
  authController.forgotPassword
);

/**
 * @swagger
 * /api/v1/auth/reset-password:
 *   post:
 *     summary: Reset password (stateless via JWT token)
 *     tags: [Auth]
 *     description: Resets the user's password using a JWT token from the reset link. Works with Firebase and local MongoDB password.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - newPassword
 *             properties:
 *               token:
 *                 type: string
 *                 description: JWT password reset token from email link
 *               newPassword:
 *                 type: string
 *                 description: New password
 *                 example: "MyNewSuperSecret123"
 *     responses:
 *       200:
 *         description: Password has been reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Password has been reset successfully
 *       400:
 *         description: Invalid or expired token, or bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
router.post('/reset-password',
  rateLimitMiddleware.getMiddleware('passwordReset'),
  authController.resetPassword
);

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     summary: Logout user and invalidate JWT token
 *     tags: [Auth]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     description: |
 *       Logs out the user by:
 *       1. Adding the current token to a blacklist
 *       2. Clearing client-side cookies (if using cookie-based auth)
 *       3. Optionally invalidating all user sessions
 *     parameters:
 *       - in: query
 *         name: all_devices
 *         schema:
 *           type: boolean
 *         description: If true, invalidates all active sessions for the user
 *         required: false
 *     responses:
 *       200:
 *         description: Successfully logged out
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Successfully logged out
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       500:
 *         description: Internal server error
 */
router.post('/logout', 
  rateLimitMiddleware.getMiddleware('logout', {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10 // limit each IP to 10 logout requests per windowMs
  }),
  authenticate, 
  authController.logout
);

module.exports = router;
