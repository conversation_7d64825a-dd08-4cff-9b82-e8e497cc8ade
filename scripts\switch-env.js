#!/usr/bin/env node

/**
 * Cross-platform environment switcher
 * Works on both Windows and Unix systems
 * 
 * Usage:
 * node scripts/switch-env.js local
 * node scripts/switch-env.js production
 */

const fs = require('fs');
const path = require('path');

// Get the environment argument
const targetEnv = process.argv[2];

if (!targetEnv) {
  console.error('❌ Error: Please specify environment (local or production)');
  console.log('Usage: node scripts/switch-env.js <local|production>');
  process.exit(1);
}

// Validate environment
if (!['local', 'production'].includes(targetEnv)) {
  console.error('❌ Error: Environment must be "local" or "production"');
  process.exit(1);
}

// Define file paths
const rootDir = path.join(__dirname, '..');
const sourceFile = path.join(rootDir, `.env.${targetEnv}`);
const targetFile = path.join(rootDir, '.env');

try {
  // Check if source file exists
  if (!fs.existsSync(sourceFile)) {
    console.error(`❌ Error: Source file .env.${targetEnv} not found`);
    process.exit(1);
  }

  // Copy the file
  fs.copyFileSync(sourceFile, targetFile);
  
  console.log(`✅ Switched to ${targetEnv.toUpperCase()} environment`);
  console.log(`📁 Copied .env.${targetEnv} → .env`);
  
} catch (error) {
  console.error('❌ Error switching environment:', error.message);
  process.exit(1);
}
