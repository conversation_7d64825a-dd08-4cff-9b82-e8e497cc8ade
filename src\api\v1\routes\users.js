require('dotenv').config();
const express = require('express');
const { rbac } = require('../../../middleware/rbac');
const { authenticate } = require('../../../middleware/auth');
const userController = require('../controllers/userController');
const rateLimitMiddleware = require('../../../middleware/rateLimitMiddleware');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   - name: Users
 *     description: User registration, profile, update & deletion
 */

/**
 * @swagger
 * /api/v1/users/register:
 *   post:
 *     summary: Register a new user (custom auth + Firebase)
 *     tags: [Users]
 *     description: Registers a new user with email (required), optional name & phone number.<br>
 *                  Creates user in both Firebase Auth and MongoDB, then returns custom JWTs.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [email, password]
 *             properties:
 *               name:
 *                 type: string
 *                 description: Optional; defaults to "User"
 *                 example: "<PERSON>"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               phone_number:
 *                 type: string
 *                 example: "+911234567890"
 *               password:
 *                 type: string
 *                 example: "SuperSecret123"
 *     responses:
 *       201:
 *         description: user_registered
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message: { type: string, example: user_registered }
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id: { type: string }
 *                     firebase_uid: { type: string }
 *                     email: { type: string }
 *                     name: { type: string }
 *                     phone_number: { type: string }
 *                     type: { type: string, enum: [individual, organization] }
 *                 idToken: { type: string }
 *                 refreshToken: { type: string }
 *                 expiresIn: { type: string }
 *       400:
 *         description: Invalid input or user exists
 *       500:
 *         description: Server error
 */
router.post('/register', userController.register);

/**
 * @swagger
 * /api/v1/users/:
 *   get:
 *     summary: Get all users with pagination (RBAC)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema: { type: integer, default: 1 }
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema: { type: integer, default: 10 }
 *         description: Number of users per page
 *     responses:
 *       200:
 *         description: Paginated list of users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 page: { type: integer }
 *                 limit: { type: integer }
 *                 total: { type: integer }
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       email: { type: string }
 *                       name: { type: string }
 *                       phone_number: { type: string }
 *       401:
 *         description: Unauthorized (no token)
 *       403:
 *         description: "Forbidden (RBAC: requires user:read)"
 */
router.get(
  '/',
  authenticate,
  rbac('user_object:read', { allowSystemWide: true, requireOrgContext: false }),
  userController.getUsers
);

/**
 * @swagger
 * /api/v1/users/update_user:
 *   put:
 *     summary: Update the current user's profile information
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Basic user name (optional - can use fullName instead)
 *                 example: "John Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address (optional)
 *                 example: "<EMAIL>"
 *               phone_number:
 *                 type: string
 *                 description: User's phone number (optional)
 *                 example: "+1234567890"
 *               type:
 *                 type: string
 *                 enum: [individual, organization]
 *                 description: User type for organization management (optional)
 *                 example: "individual"
 *               fullName:
 *                 type: string
 *                 description: Full display name (optional - can be different from 'name')
 *                 example: "John Michael Doe"
 *               jobTitle:
 *                 type: string
 *                 description: User's job title
 *                 example: "Senior Software Engineer"
 *               companyName:
 *                 type: string
 *                 description: User's company name
 *                 example: "Tech Corp Inc."
 *               bio:
 *                 type: string
 *                 description: One-sentence bio (max 200 characters)
 *                 maxLength: 200
 *                 example: "Passionate software engineer with 5+ years of experience in full-stack development."
 *               industryTags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Industry/Function tags
 *                 example: ["Technology", "Software Development", "AI/ML"]
 *               networkingGoal:
 *                 type: string
 *                 description: User's networking goal
 *                 example: "Looking to connect with other tech professionals and explore collaboration opportunities"
 *               delegateEmail:
 *                 type: string
 *                 format: email
 *                 description: Optional delegate email address
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: user_updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: user_updated
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id: { type: string }
 *                     name: { type: string }
 *                     email: { type: string }
 *                     phone_number: { type: string }
 *                     type: { type: string, enum: [individual, organization] }
 *                     profile:
 *                       type: object
 *                       properties:
 *                         fullName: { type: string }
 *                         jobTitle: { type: string }
 *                         companyName: { type: string }
 *                         bio: { type: string }
 *                         industryTags:
 *                           type: array
 *                           items:
 *                             type: string
 *                         networkingGoal: { type: string }
 *                         delegateEmail: { type: string }
 *                         avatar: { type: string }
 *                         timezone: { type: string }
 *                         language: { type: string }
 *       400:
 *         description: Validation error (bio too long, invalid email, etc.)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message: { type: string }
 *                 field: { type: string }
 *                 maxLength: { type: number }
 *                 currentLength: { type: number }
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: update_failed
 */
router.put(
  '/update_user',
  authenticate,
  userController.updateUserProfile
);

/**
 * @swagger
 * /api/v1/users/delete_user:
 *   delete:
 *     summary: Delete the current user (MongoDB + Firebase + remove from all orgs)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     responses:
 *       200:
 *         description: user_deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message: { type: string, example: user_deleted }
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: delete_failed
 */
router.delete(
  '/delete_user',
  authenticate,
  userController.deleteUser
);

/**
 * @swagger
 * /api/v1/users/me:
 *   get:
 *     summary: Get the current authenticated user's complete profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     responses:
 *       200:
 *         description: Current user's complete profile, including organizations and extended profile fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id: { type: string }
 *                 name: { type: string }
 *                 email: { type: string }
 *                 phone_number: { type: string }
 *                 type: { type: string, enum: [individual, organization] }
 *                 status: { type: string }
 *                 profile:
 *                   type: object
 *                   properties:
 *                     fullName:
 *                       type: string
 *                       description: Full display name
 *                     jobTitle:
 *                       type: string
 *                       description: User's job title
 *                     companyName:
 *                       type: string
 *                       description: User's company name
 *                     bio:
 *                       type: string
 *                       description: One-sentence bio (max 200 characters)
 *                     industryTags:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: Industry/Function tags
 *                     networkingGoal:
 *                       type: string
 *                       description: User's networking goal
 *                     delegateEmail:
 *                       type: string
 *                       description: Optional delegate email address
 *                     avatar: { type: string }
 *                     timezone: { type: string }
 *                     language: { type: string }
 *                     preferences: { type: object }
 *                 roles:
 *                   type: array
 *                   description: User's role assignments
 *                   items:
 *                     type: object
 *                     properties:
 *                       org: { type: string }
 *                       role: { type: string }
 *                       assignedAt: { type: string, format: date-time }
 *                 systemPrivileges:
 *                   type: array
 *                   description: System-level privileges
 *                   items:
 *                     type: object
 *                     properties:
 *                       level: { type: string }
 *                       grantedAt: { type: string, format: date-time }
 *                       grantedBy: { type: string }
 *                 organizations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id: { type: string }
 *                       name: { type: string }
 *                       subdomain: { type: string }
 *                       branding:
 *                         type: object
 *                         properties:
 *                           logoUrl: { type: string }
 *                           primaryColor: { type: string }
 *                 privilegeSummary:
 *                   type: object
 *                   properties:
 *                     isGodSuperUser: { type: boolean }
 *                     hasSystemPrivileges: { type: boolean }
 *                     highestPrivilegeLevel: { type: number }
 *                     organizationCount: { type: number }
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: fetch_failed
 */
router.get(
  '/me',
  authenticate,
  userController.getCurrentUser
);

/**
 * @swagger
 * /api/v1/users/status:
 *   put:
 *     summary: Update a user's status by email or phone number (RBAC)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [identifier, status]
 *             properties:
 *               identifier:
 *                 type: string
 *                 description: User's email or phone number
 *                 example: "<EMAIL>"
 *               status:
 *                 type: string
 *                 enum: [active, inactive, banned, blocked]
 *                 example: "inactive"
 *     responses:
 *       200:
 *         description: User status updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message: { type: string }
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id: { type: string }
 *                     email: { type: string }
 *                     phone_number: { type: string }
 *                     status: { type: string }
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: "Forbidden (RBAC: requires user:update)"
 *       404:
 *         description: User not found
 *       500:
 *         description: Update failed
 */
router.put(
  '/status',
  authenticate,
  rbac('user_object:update', { allowSystemWide: true, requireOrgContext: false }),
  userController.updateUserStatus
);

/**
 * @swagger
 * /api/v1/users/status:
 *   get:
 *     summary: Get a user's status by email or phone number (RBAC)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     parameters:
 *       - in: query
 *         name: identifier
 *         required: true
 *         schema:
 *           type: string
 *         description: User's email or phone number
 *         example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: User status found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id: { type: string }
 *                 email: { type: string }
 *                 phone_number: { type: string }
 *                 status: { type: string }
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: "Forbidden (RBAC: requires user:read)"
 *       404:
 *         description: User not found
 */
router.get(
  '/status',
  authenticate,
  rbac('user_object:read', { allowSystemWide: true, requireOrgContext: false }),
  userController.getUserStatus
);

/**
 * @swagger
 * /api/v1/users/default-organization:
 *   patch:
 *     summary: Update user's default organization
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     description: |
 *       Updates the authenticated user's default organization. The user must be a member
 *       of the specified organization to set it as their default.
 *
 *       **Organization Identification:**
 *       - Use either `organizationId` (MongoDB ObjectId) or `organizationSubdomain` (subdomain name)
 *       - If both are provided, `organizationId` takes priority
 *
 *       **Requirements:**
 *       - User must be authenticated
 *       - User must be a member of the specified organization
 *       - Organization must exist and be active
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organizationId:
 *                 type: string
 *                 format: objectId
 *                 description: MongoDB ObjectId of the organization to set as default
 *                 example: "507f1f77bcf86cd799439011"
 *               organizationSubdomain:
 *                 type: string
 *                 pattern: '^[a-z0-9-]+$'
 *                 description: Subdomain of the organization to set as default
 *                 example: "acme-corp"
 *             oneOf:
 *               - required: [organizationId]
 *               - required: [organizationSubdomain]
 *           examples:
 *             setDefaultById:
 *               summary: Set default organization by ID
 *               value:
 *                 organizationId: "507f1f77bcf86cd799439011"
 *             setDefaultBySubdomain:
 *               summary: Set default organization by subdomain
 *               value:
 *                 organizationSubdomain: "acme-corp"
 *     responses:
 *       200:
 *         description: Default organization updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Default organization updated to 'Acme Corporation'"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         _id:
 *                           type: string
 *                         email:
 *                           type: string
 *                         name:
 *                           type: string
 *                         defaultOrganization:
 *                           type: object
 *                           properties:
 *                             _id:
 *                               type: string
 *                             name:
 *                               type: string
 *                             subdomain:
 *                               type: string
 *                             branding:
 *                               type: object
 *                             status:
 *                               type: string
 *                     previousDefaultOrganization:
 *                       type: object
 *                       nullable: true
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Invalid input (missing organizationId, invalid format)
 *       403:
 *         description: User is not a member of the specified organization
 *       404:
 *         description: User or organization not found
 *       500:
 *         description: Server error
 */
router.patch(
  '/default-organization',
  rateLimitMiddleware.getMiddleware('general'),
  authenticate,
  userController.updateDefaultOrganization.bind(userController)
);

/**
 * @swagger
 * /api/v1/users/default-organization:
 *   delete:
 *     summary: Clear user's default organization
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *       - OAuth2PasswordBearer: []
 *     description: |
 *       Clears the authenticated user's default organization, setting it to null.
 *       This operation is always allowed for authenticated users.
 *     responses:
 *       200:
 *         description: Default organization cleared successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Default organization cleared successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         _id:
 *                           type: string
 *                         email:
 *                           type: string
 *                         name:
 *                           type: string
 *                         defaultOrganization:
 *                           type: null
 *                     previousDefaultOrganization:
 *                       type: object
 *                       nullable: true
 *                     clearedAt:
 *                       type: string
 *                       format: date-time
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.delete(
  '/default-organization',
  rateLimitMiddleware.getMiddleware('general'),
  authenticate,
  userController.clearDefaultOrganization.bind(userController)
);

module.exports = router;
