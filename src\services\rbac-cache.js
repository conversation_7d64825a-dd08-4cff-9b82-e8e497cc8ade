// services/rbac-cache.js
const Role = require('../models/Role');
const NodeCache = require('node-cache');
const rbacCache = new NodeCache({ stdTTL: 300 }); // 5 minutes

async function loadAllRoles() {
  const allRoles = await Role.find({});
  for (const role of allRoles) {
    rbacCache.set(`${role.org}_${role.name}`, role.permissions);
  }
}

// Call this on startup
loadAllRoles();

// Helper to get permissions
function getPermissions(orgId, roleName) {
  return rbacCache.get(`${orgId}_${roleName}`) || [];
}

// Helper to reload a role (after admin updates in DB)
async function reloadRole(orgId, roleName) {
  const role = await Role.findOne({ org: orgId, name: roleName });
  if (role) rbacCache.set(`${orgId}_${roleName}`, role.permissions);
}

module.exports = { getPermissions, reloadRole, loadAllRoles };
