{"description": "Test cases for the Available Subdomains API", "baseUrl": "http://localhost:3000/api/v1/organizations/subdomains", "authRequired": true, "testCases": [{"name": "Get all subdomains (default - ALL organizations)", "method": "GET", "url": "/api/v1/organizations/subdomains", "description": "Returns ALL organizations regardless of status by default", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "expectedResponse": {"success": true, "data": {"subdomains": [{"id": "507f1f77bcf86cd799439011", "name": "Acme Corporation", "subdomain": "acme-corp", "status": "active", "memberCount": 25, "createdAt": "2025-06-04T10:30:00.000Z", "updatedAt": "2025-06-04T10:30:00.000Z", "url": "https://acme-corp.digimeet.live"}], "pagination": {"page": 1, "limit": 50, "total": 150, "pages": 3, "hasNextPage": true, "hasPrevPage": false}, "metadata": {"totalOrganizations": 150, "statusDistribution": {"active": 120, "pending": 25, "inactive": 5}, "timestamp": "2025-06-04T10:30:00.000Z", "rootDomain": "digimeet.live"}}}}, {"name": "Get subdomains with pagination", "method": "GET", "url": "/api/v1/organizations/subdomains?page=2&limit=10", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Test pagination functionality"}, {"name": "Search subdomains by pattern", "method": "GET", "url": "/api/v1/organizations/subdomains?search=acme", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Test search functionality with pattern matching"}, {"name": "Filter by status", "method": "GET", "url": "/api/v1/organizations/subdomains?status=active", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Test status filtering"}, {"name": "Sort by name descending", "method": "GET", "url": "/api/v1/organizations/subdomains?sortBy=name&sortOrder=-1", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Test sorting functionality"}, {"name": "Get all organizations (default behavior)", "method": "GET", "url": "/api/v1/organizations/subdomains", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Default behavior - returns ALL organizations regardless of status"}, {"name": "Complex query with multiple filters", "method": "GET", "url": "/api/v1/organizations/subdomains?page=1&limit=20&status=active&search=corp&sortBy=createdAt&sortOrder=-1", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Test multiple query parameters together"}], "curlExamples": [{"description": "Basic request", "command": "curl -X GET 'http://localhost:3000/api/v1/organizations/subdomains' -H 'Authorization: Bearer YOUR_JWT_TOKEN' -H 'Content-Type: application/json'"}, {"description": "With search and pagination", "command": "curl -X GET 'http://localhost:3000/api/v1/organizations/subdomains?search=acme&page=1&limit=10' -H 'Authorization: Bearer YOUR_JWT_TOKEN' -H 'Content-Type: application/json'"}, {"description": "Filter active organizations only", "command": "curl -X GET 'http://localhost:3000/api/v1/organizations/subdomains?status=active&sortBy=subdomain' -H 'Authorization: Bearer YOUR_JWT_TOKEN' -H 'Content-Type: application/json'"}], "errorTestCases": [{"name": "No authentication", "method": "GET", "url": "/api/v1/organizations/subdomains", "headers": {"Content-Type": "application/json"}, "expectedStatus": 401, "expectedResponse": {"success": false, "message": "Authentication required"}}, {"name": "Invalid page number", "method": "GET", "url": "/api/v1/organizations/subdomains?page=0", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Should default to page 1"}, {"name": "Invalid limit (too high)", "method": "GET", "url": "/api/v1/organizations/subdomains?limit=200", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "description": "Should cap at maximum limit (100)"}], "useCases": [{"name": "Frontend Dropdown Population", "description": "Use this API to populate subdomain selection dropdowns in admin interfaces", "example": "GET /api/v1/organizations/subdomains?status=active&limit=100"}, {"name": "Subdomain Validation", "description": "Check if a subdomain already exists before creating new organizations", "example": "GET /api/v1/organizations/subdomains?search=new-subdomain"}, {"name": "System Overview Dashboard", "description": "Display organization statistics and subdomain distribution", "example": "GET /api/v1/organizations/subdomains?page=1&limit=50"}, {"name": "Bulk Operations", "description": "Get all organization subdomains for bulk operations or exports", "example": "GET /api/v1/organizations/subdomains?limit=100&includeInactive=true"}]}