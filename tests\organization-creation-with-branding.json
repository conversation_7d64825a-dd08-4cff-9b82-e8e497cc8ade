{"test_name": "Create Organization - With Branding", "description": "Test organization creation with branding information", "endpoint": "POST /api/v1/organizations", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"}, "request_body": {"name": "Acme Corporation", "subdomain": "acme-corp", "branding": {"logoUrl": "https://example.com/logo.png", "primaryColor": "#336699"}}, "expected_response": {"status": 201, "body": {"message": "Organization created successfully. Pending approval from system administrator.", "organization": {"_id": "string", "name": "Acme Corporation", "subdomain": "acme-corp", "status": "pending", "branding": {"logoUrl": "https://example.com/logo.png", "primaryColor": "#336699"}, "createdBy": "string", "createdByRole": "super_user", "createdAt": "string", "members": [{"user": "string", "role": "super_user", "joinedAt": "string", "status": "active"}]}}}, "curl_command": "curl -X POST 'https://your-domain.vercel.app/api/v1/organizations' -H 'Content-Type: application/json' -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE' -d '{\"name\":\"Acme Corporation\",\"subdomain\":\"acme-corp\",\"branding\":{\"logoUrl\":\"https://example.com/logo.png\",\"primaryColor\":\"#336699\"}}'"}