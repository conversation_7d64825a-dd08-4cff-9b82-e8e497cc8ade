const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const shortid = require('shortid');

const authBridgeSchema = new mongoose.Schema({
  firebase_uid: { type: String, unique: true, required: true },
  email:        { type: String, unique: true, required: true },
  password:     { type: String, required: true },
  JWT_UID:      { type: String, unique: true, required: true, default: shortid.generate }
});

authBridgeSchema.pre('save', async function(next) {
  if (this.isModified('password') && this.password) {
    this.password = await bcrypt.hash(this.password, 10);
  }
  next();
});

authBridgeSchema.methods.matchPassword = function(password) {
  return bcrypt.compare(password, this.password);
};

module.exports = mongoose.models.AuthBridge || mongoose.model('AuthBridge', authBridgeSchema);
