// migrations/002-centralized-rbac-database.js
const mongoose = require('mongoose');
const RBACRole = require('../models/RBACRole');
const Role = require('../models/Role');
const Organization = require('../models/Organization');
const User = require('../models/User');
const logger = require('../services/logger');

/**
 * Migration: Centralized RBAC Database
 * 
 * This migration creates and populates the dedicated RBAC roles database
 * that serves as the centralized source of truth for all role-based access control.
 */
class CentralizedRBACDatabaseMigration {
  constructor() {
    this.migrationName = 'centralized-rbac-database';
    this.version = '002';
  }

  /**
   * Run the migration
   * @param {Object} options - Migration options
   * @returns {Object} Migration result
   */
  async up(options = {}) {
    const { dryRun = false, force = false } = options;
    const correlationId = logger.generateCorrelationId();
    
    logger.info('Starting Centralized RBAC Database Migration', {
      component: 'migration',
      operation: 'centralized_rbac_migration',
      correlation_id: correlationId,
      metadata: {
        migration_name: this.migrationName,
        version: this.version,
        dry_run: dryRun,
        force: force
      }
    });

    const results = {
      success: false,
      dryRun,
      statistics: {
        systemRolesCreated: 0,
        legacyRolesMigrated: 0,
        organizationsProcessed: 0,
        userRolesMigrated: 0,
        errors: []
      },
      operations: []
    };

    try {
      // Step 1: Create system roles for all organizations
      const systemRolesResult = await this.createSystemRoles(dryRun, correlationId);
      results.operations.push(systemRolesResult);
      results.statistics.systemRolesCreated = systemRolesResult.rolesCreated;
      results.statistics.organizationsProcessed = systemRolesResult.organizationsProcessed;

      // Step 2: Migrate existing legacy roles
      const legacyMigrationResult = await this.migrateLegacyRoles(dryRun, correlationId);
      results.operations.push(legacyMigrationResult);
      results.statistics.legacyRolesMigrated = legacyMigrationResult.rolesMigrated;

      // Step 3: Update user role references (if needed)
      const userMigrationResult = await this.updateUserRoleReferences(dryRun, correlationId);
      results.operations.push(userMigrationResult);
      results.statistics.userRolesMigrated = userMigrationResult.usersUpdated;

      // Step 4: Create indexes for performance
      const indexResult = await this.createIndexes(dryRun, correlationId);
      results.operations.push(indexResult);

      results.success = true;
      
      logger.info('Centralized RBAC Database Migration completed successfully', {
        component: 'migration',
        operation: 'centralized_rbac_migration',
        correlation_id: correlationId,
        metadata: {
          migration_name: this.migrationName,
          statistics: results.statistics,
          dry_run: dryRun
        }
      });

      return results;

    } catch (error) {
      logger.error('Centralized RBAC Database Migration failed', error, {
        component: 'migration',
        operation: 'centralized_rbac_migration',
        correlation_id: correlationId,
        metadata: {
          migration_name: this.migrationName,
          dry_run: dryRun
        }
      });

      results.statistics.errors.push({
        step: 'migration_execution',
        error: error.message,
        stack: error.stack
      });

      throw error;
    }
  }

  /**
   * Create system roles for all organizations
   */
  async createSystemRoles(dryRun, correlationId) {
    logger.info('Creating system roles for all organizations', {
      component: 'migration',
      operation: 'create_system_roles',
      correlation_id: correlationId
    });

    const result = {
      step: 'create_system_roles',
      success: false,
      rolesCreated: 0,
      organizationsProcessed: 0,
      details: []
    };

    try {
      // Get all organizations
      const organizations = await Organization.find({});
      
      // Get system role templates
      const systemRoleTemplates = RBACRole.getSystemRoles();

      for (const org of organizations) {
        logger.info(`Processing organization: ${org.name}`, {
          component: 'migration',
          operation: 'create_system_roles',
          correlation_id: correlationId,
          metadata: {
            org_id: org._id,
            org_name: org.name
          }
        });

        for (const roleTemplate of systemRoleTemplates) {
          // Skip system-scoped roles for organization processing
          if (roleTemplate.scope === 'system') continue;

          const existingRole = await RBACRole.findOne({
            name: roleTemplate.name,
            scope: 'organization',
            organization: org._id
          });

          if (!existingRole) {
            const roleData = {
              ...roleTemplate,
              organization: org._id,
              audit: {
                createdBy: null, // System created
                createdAt: new Date(),
                usageCount: 0,
                changeHistory: [{
                  action: 'created',
                  timestamp: new Date(),
                  performedBy: null,
                  reason: 'System migration - centralized RBAC database'
                }]
              }
            };

            if (!dryRun) {
              const newRole = await RBACRole.create(roleData);
              result.details.push({
                action: 'created',
                role_name: newRole.name,
                org_id: org._id,
                org_name: org.name,
                role_id: newRole._id
              });
            } else {
              result.details.push({
                action: 'would_create',
                role_name: roleTemplate.name,
                org_id: org._id,
                org_name: org.name
              });
            }

            result.rolesCreated++;
          } else {
            result.details.push({
              action: 'skipped_existing',
              role_name: roleTemplate.name,
              org_id: org._id,
              org_name: org.name,
              existing_role_id: existingRole._id
            });
          }
        }

        result.organizationsProcessed++;
      }

      // Create global system roles
      for (const roleTemplate of systemRoleTemplates) {
        if (roleTemplate.scope === 'system') {
          const existingRole = await RBACRole.findOne({
            name: roleTemplate.name,
            scope: 'system'
          });

          if (!existingRole) {
            const roleData = {
              ...roleTemplate,
              audit: {
                createdBy: null,
                createdAt: new Date(),
                usageCount: 0,
                changeHistory: [{
                  action: 'created',
                  timestamp: new Date(),
                  performedBy: null,
                  reason: 'System migration - centralized RBAC database'
                }]
              }
            };

            if (!dryRun) {
              const newRole = await RBACRole.create(roleData);
              result.details.push({
                action: 'created',
                role_name: newRole.name,
                scope: 'system',
                role_id: newRole._id
              });
            } else {
              result.details.push({
                action: 'would_create',
                role_name: roleTemplate.name,
                scope: 'system'
              });
            }

            result.rolesCreated++;
          }
        }
      }

      result.success = true;
      return result;

    } catch (error) {
      logger.error('Failed to create system roles', error, {
        component: 'migration',
        operation: 'create_system_roles',
        correlation_id: correlationId
      });

      result.error = error.message;
      throw error;
    }
  }

  /**
   * Migrate existing legacy roles to centralized RBAC database
   */
  async migrateLegacyRoles(dryRun, correlationId) {
    logger.info('Migrating legacy roles to centralized RBAC database', {
      component: 'migration',
      operation: 'migrate_legacy_roles',
      correlation_id: correlationId
    });

    const result = {
      step: 'migrate_legacy_roles',
      success: false,
      rolesMigrated: 0,
      details: []
    };

    try {
      // Get all legacy roles that are not system roles
      const legacyRoles = await Role.find({
        name: { $nin: ['god_super_user', 'org_super_user', 'view_manager', 'admin', 'manager', 'member', 'guest'] }
      });

      for (const legacyRole of legacyRoles) {
        // Check if already migrated
        const existingRBACRole = await RBACRole.findOne({
          name: legacyRole.name,
          organization: legacyRole.org
        });

        if (!existingRBACRole) {
          const rbacRoleData = {
            name: legacyRole.name,
            displayName: legacyRole.name,
            description: legacyRole.description || `Migrated legacy role: ${legacyRole.name}`,
            scope: 'organization',
            organization: legacyRole.org,
            hierarchy: legacyRole.hierarchy || { level: 10, type: 'custom' },
            permissions: legacyRole.permissions || [],
            metadata: {
              isActive: true,
              isSystem: false,
              isBuiltIn: false,
              category: 'custom'
            },
            audit: {
              createdBy: null,
              createdAt: new Date(),
              usageCount: 0,
              changeHistory: [{
                action: 'created',
                timestamp: new Date(),
                performedBy: null,
                reason: 'Legacy role migration to centralized RBAC database',
                changes: {
                  migrated_from: legacyRole._id,
                  original_permissions: legacyRole.permissions
                }
              }]
            }
          };

          if (!dryRun) {
            const newRBACRole = await RBACRole.create(rbacRoleData);
            result.details.push({
              action: 'migrated',
              legacy_role_id: legacyRole._id,
              legacy_role_name: legacyRole.name,
              new_rbac_role_id: newRBACRole._id,
              org_id: legacyRole.org
            });
          } else {
            result.details.push({
              action: 'would_migrate',
              legacy_role_id: legacyRole._id,
              legacy_role_name: legacyRole.name,
              org_id: legacyRole.org
            });
          }

          result.rolesMigrated++;
        } else {
          result.details.push({
            action: 'skipped_existing',
            legacy_role_id: legacyRole._id,
            legacy_role_name: legacyRole.name,
            existing_rbac_role_id: existingRBACRole._id
          });
        }
      }

      result.success = true;
      return result;

    } catch (error) {
      logger.error('Failed to migrate legacy roles', error, {
        component: 'migration',
        operation: 'migrate_legacy_roles',
        correlation_id: correlationId
      });

      result.error = error.message;
      throw error;
    }
  }

  /**
   * Update user role references (if needed for future enhancements)
   */
  async updateUserRoleReferences(dryRun, correlationId) {
    logger.info('Updating user role references', {
      component: 'migration',
      operation: 'update_user_role_references',
      correlation_id: correlationId
    });

    const result = {
      step: 'update_user_role_references',
      success: true,
      usersUpdated: 0,
      details: []
    };

    // For now, we maintain backward compatibility by keeping existing role references
    // Future enhancements could update user role references to point to RBAC roles
    
    result.details.push({
      action: 'skipped',
      reason: 'Maintaining backward compatibility with existing role references'
    });

    return result;
  }

  /**
   * Create database indexes for performance
   */
  async createIndexes(dryRun, correlationId) {
    logger.info('Creating database indexes for RBAC roles', {
      component: 'migration',
      operation: 'create_indexes',
      correlation_id: correlationId
    });

    const result = {
      step: 'create_indexes',
      success: false,
      indexesCreated: 0,
      details: []
    };

    try {
      if (!dryRun) {
        // The indexes are already defined in the RBACRole model schema
        // This step ensures they are created
        await RBACRole.createIndexes();
        
        result.details.push({
          action: 'created',
          description: 'All RBAC role indexes created successfully'
        });
      } else {
        result.details.push({
          action: 'would_create',
          description: 'Would create all RBAC role indexes'
        });
      }

      result.indexesCreated = 1;
      result.success = true;
      return result;

    } catch (error) {
      logger.error('Failed to create indexes', error, {
        component: 'migration',
        operation: 'create_indexes',
        correlation_id: correlationId
      });

      result.error = error.message;
      throw error;
    }
  }

  /**
   * Rollback the migration
   */
  async down(options = {}) {
    const { dryRun = false } = options;
    const correlationId = logger.generateCorrelationId();
    
    logger.warn('Rolling back Centralized RBAC Database Migration', {
      component: 'migration',
      operation: 'centralized_rbac_rollback',
      correlation_id: correlationId,
      metadata: {
        migration_name: this.migrationName,
        dry_run: dryRun
      }
    });

    if (!dryRun) {
      // Remove all RBAC roles created by this migration
      await RBACRole.deleteMany({
        'audit.changeHistory.reason': { 
          $in: [
            'System migration - centralized RBAC database',
            'Legacy role migration to centralized RBAC database'
          ]
        }
      });
    }

    return {
      success: true,
      message: 'Centralized RBAC Database Migration rolled back successfully'
    };
  }
}

module.exports = CentralizedRBACDatabaseMigration;
