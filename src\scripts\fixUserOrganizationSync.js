#!/usr/bin/env node

/**
 * Data Migration Script: Fix User-Organization Bidirectional Relationship
 * 
 * This script fixes the inconsistency between user.roles and organization.members
 * by ensuring both systems are synchronized.
 * 
 * Usage: node src/scripts/fixUserOrganizationSync.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Organization = require('../models/Organization');
const Role = require('../models/Role');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Create required roles if they don't exist
const ensureRolesExist = async () => {
  console.log('\n🔧 Ensuring required roles exist...');
  
  const requiredRoles = [
    {
      name: 'org_super_user',
      displayName: 'Organization Super User',
      description: 'Super user role for organization management',
      scope: 'organization',
      hierarchy: { level: 1, parent: null },
      permissions: ['org:*']
    },
    {
      name: 'org_admin',
      displayName: 'Organization Admin',
      description: 'Admin role for organization management',
      scope: 'organization',
      hierarchy: { level: 2, parent: null },
      permissions: ['org:read', 'org:update', 'org:manage_members']
    },
    {
      name: 'org_member',
      displayName: 'Organization Member',
      description: 'Basic member role for organization',
      scope: 'organization',
      hierarchy: { level: 3, parent: null },
      permissions: ['org:read']
    }
  ];

  for (const roleData of requiredRoles) {
    const existingRole = await Role.findOne({ 
      name: roleData.name,
      scope: roleData.scope 
    });

    if (!existingRole) {
      const role = new Role(roleData);
      await role.save();
      console.log(`   ✅ Created role: ${roleData.name}`);
    } else {
      console.log(`   ℹ️  Role already exists: ${roleData.name}`);
    }
  }
};

// Fix organizations that have members but users don't have corresponding roles
const fixOrganizationToUserSync = async () => {
  console.log('\n🔄 Fixing Organization → User synchronization...');
  
  const organizations = await Organization.find({});
  let fixedCount = 0;
  
  for (const org of organizations) {
    console.log(`\n📋 Processing organization: ${org.name} (${org.subdomain})`);
    
    for (const member of org.members) {
      const user = await User.findById(member.user);
      if (!user) {
        console.log(`   ⚠️  User not found: ${member.user}`);
        continue;
      }

      // Check if user has a role for this organization
      const existingRole = user.roles.find(r => 
        r.org && r.org.toString() === org._id.toString()
      );

      if (!existingRole) {
        // Find the appropriate RBAC role
        let roleName;
        switch (member.role) {
          case 'super_user':
            roleName = 'org_super_user';
            break;
          case 'admin':
            roleName = 'org_admin';
            break;
          case 'member':
          default:
            roleName = 'org_member';
            break;
        }

        const rbacRole = await Role.findOne({ 
          name: roleName,
          scope: 'organization' 
        });

        if (rbacRole) {
          user.roles.push({
            org: org._id,
            role: rbacRole._id,
            assignedAt: member.joinedAt || new Date(),
            assignedBy: member.addedBy || org.createdBy
          });
          
          await user.save();
          fixedCount++;
          console.log(`   ✅ Added role ${roleName} to user ${user.email}`);
        } else {
          console.log(`   ❌ Role not found: ${roleName}`);
        }
      } else {
        console.log(`   ℹ️  User ${user.email} already has role for this org`);
      }
    }
  }
  
  console.log(`\n✅ Fixed ${fixedCount} user role assignments`);
};

// Fix users that have organization roles but organizations don't have them as members
const fixUserToOrganizationSync = async () => {
  console.log('\n🔄 Fixing User → Organization synchronization...');
  
  const users = await User.find({ 'roles.0': { $exists: true } })
    .populate('roles.org')
    .populate('roles.role');
  
  let fixedCount = 0;
  
  for (const user of users) {
    console.log(`\n👤 Processing user: ${user.email}`);
    
    for (const roleAssignment of user.roles) {
      if (!roleAssignment.org) continue;
      
      const org = await Organization.findById(roleAssignment.org);
      if (!org) {
        console.log(`   ⚠️  Organization not found: ${roleAssignment.org}`);
        continue;
      }

      // Check if organization has this user as a member
      const existingMember = org.members.find(m => 
        m.user.toString() === user._id.toString()
      );

      if (!existingMember) {
        // Determine member role from RBAC role
        let memberRole = 'member';
        if (roleAssignment.role && roleAssignment.role.name) {
          switch (roleAssignment.role.name) {
            case 'org_super_user':
              memberRole = 'super_user';
              break;
            case 'org_admin':
              memberRole = 'admin';
              break;
            case 'org_member':
            default:
              memberRole = 'member';
              break;
          }
        }

        org.members.push({
          user: user._id,
          role: memberRole,
          joinedAt: roleAssignment.assignedAt || new Date(),
          addedBy: roleAssignment.assignedBy || org.createdBy,
          status: 'active'
        });
        
        await org.save();
        fixedCount++;
        console.log(`   ✅ Added user ${user.email} as ${memberRole} to org ${org.name}`);
      } else {
        console.log(`   ℹ️  User ${user.email} already member of org ${org.name}`);
      }
    }
  }
  
  console.log(`\n✅ Fixed ${fixedCount} organization member assignments`);
};

// Generate summary report
const generateReport = async () => {
  console.log('\n📊 Generating synchronization report...');
  
  const totalUsers = await User.countDocuments();
  const totalOrgs = await Organization.countDocuments();
  const usersWithRoles = await User.countDocuments({ 'roles.0': { $exists: true } });
  const orgsWithMembers = await Organization.countDocuments({ 'members.0': { $exists: true } });
  
  console.log('\n📈 Summary Report:');
  console.log(`   Total Users: ${totalUsers}`);
  console.log(`   Total Organizations: ${totalOrgs}`);
  console.log(`   Users with Roles: ${usersWithRoles}`);
  console.log(`   Organizations with Members: ${orgsWithMembers}`);
  
  // Check for any remaining inconsistencies
  const organizations = await Organization.find({});
  let inconsistencies = 0;
  
  for (const org of organizations) {
    for (const member of org.members) {
      const user = await User.findById(member.user);
      if (user) {
        const hasRole = user.roles.some(r => 
          r.org && r.org.toString() === org._id.toString()
        );
        if (!hasRole) {
          inconsistencies++;
        }
      }
    }
  }
  
  if (inconsistencies === 0) {
    console.log('\n✅ All user-organization relationships are now synchronized!');
  } else {
    console.log(`\n⚠️  Found ${inconsistencies} remaining inconsistencies`);
  }
};

// Main execution function
const main = async () => {
  console.log('🚀 Starting User-Organization Synchronization Fix...');
  
  try {
    await connectDB();
    await ensureRolesExist();
    await fixOrganizationToUserSync();
    await fixUserToOrganizationSync();
    await generateReport();
    
    console.log('\n🎉 Migration completed successfully!');
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n👋 Database connection closed');
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
