// src/config/corsConfig.js
const logger = require('../services/logger');

/**
 * Enhanced CORS Configuration with Wildcard Domain Support
 * Handles multi-tenant subdomain CORS policies for organization-specific access
 */
class CorsConfig {
  constructor() {
    this.wildcardEnabled = process.env.CORS_WILDCARD_ENABLED === 'true';
    this.rootDomain = process.env.ROOT_DOMAIN || 'digimeet.live';
    this.subdomainPattern = process.env.CORS_SUBDOMAIN_PATTERN || '*.digimeet.live';

    // Production domains
    this.productionDomains = ['digimeet.live', 'digimeet.biz', 'digimeet.net'];
    this.isProduction = process.env.NODE_ENV === 'production';

    // Parse static CORS origins
    this.staticOrigins = (process.env.CORS_ORIGINS || '').split(',').filter(Boolean);

    // Compile regex patterns for wildcard matching
    this.wildcardPatterns = this.compileWildcardPatterns();
  }

  /**
   * Compile wildcard patterns into regex
   */
  compileWildcardPatterns() {
    const patterns = [];

    if (this.wildcardEnabled) {
      // Convert *.yourdomain.com to regex pattern
      const escapedDomain = this.rootDomain.replace(/\./g, '\\.');
      const subdomainRegex = new RegExp(`^https?://[a-zA-Z0-9-]+\\.${escapedDomain}$`);
      patterns.push(subdomainRegex);

      // Add root domain
      const rootRegex = new RegExp(`^https?://${escapedDomain}$`);
      patterns.push(rootRegex);
    }

    return patterns;
  }

  /**
   * CORS origin validation function
   */
  corsOrigin() {
    return (origin, callback) => {
      try {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) {
          return callback(null, true);
        }

        // Check static origins first
        if (this.staticOrigins.includes(origin)) {
          logger.debug('CORS: Static origin allowed', {
            component: 'cors-config',
            operation: 'static_origin_check',
            metadata: { origin }
          });
          return callback(null, true);
        }

        // Special case for Vercel deployments - allow any auth-03-0.vercel.app or similar
        if (origin.includes('auth-03-0') && origin.includes('vercel.app')) {
          logger.debug('CORS: Vercel deployment origin allowed', {
            component: 'cors-config',
            operation: 'vercel_origin_check',
            metadata: { origin }
          });
          return callback(null, true);
        }

        // Check production domains (digimeet.live, digimeet.biz, digimeet.net)
        const isProductionDomain = this.productionDomains.some(domain => origin.includes(domain));
        if (isProductionDomain) {
          logger.debug('CORS: Production domain origin allowed', {
            component: 'cors-config',
            operation: 'production_domain_check',
            metadata: { origin, domain_matched: this.productionDomains.find(d => origin.includes(d)) }
          });
          return callback(null, true);
        }

        // Check wildcard patterns if enabled
        if (this.wildcardEnabled) {
          for (const pattern of this.wildcardPatterns) {
            if (pattern.test(origin)) {
              logger.debug('CORS: Wildcard origin allowed', {
                component: 'cors-config',
                operation: 'wildcard_origin_check',
                metadata: { origin, pattern: pattern.source }
              });
              return callback(null, true);
            }
          }
        }

        // Development environment - allow localhost
        if (process.env.NODE_ENV === 'development') {
          if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
            logger.debug('CORS: Development origin allowed', {
              component: 'cors-config',
              operation: 'development_origin_check',
              metadata: { origin }
            });
            return callback(null, true);
          }
        }

        // Log rejected origin
        logger.warn('CORS: Origin rejected', {
          component: 'cors-config',
          operation: 'origin_rejected',
          metadata: {
            origin,
            static_origins: this.staticOrigins,
            wildcard_enabled: this.wildcardEnabled,
            root_domain: this.rootDomain
          }
        });

        // Reject origin
        callback(new Error('Not allowed by CORS'), false);
      } catch (error) {
        logger.error('CORS origin check error', {
          component: 'cors-config',
          operation: 'origin_check_error',
          error: error.message,
          metadata: { origin }
        });
        callback(error, false);
      }
    };
  }

  /**
   * Get complete CORS configuration
   */
  getCorsConfig() {
    return {
      origin: this.corsOrigin(),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Organization-Id',
        'X-Subdomain',
        'X-API-Key'
      ],
      exposedHeaders: [
        'X-Total-Count',
        'X-Page-Count',
        'X-Current-Page',
        'X-Organization-Context',
        'X-Rate-Limit-Remaining'
      ],
      maxAge: 86400, // 24 hours
      preflightContinue: false,
      optionsSuccessStatus: 204
    };
  }

  /**
   * Validate if origin matches subdomain pattern
   */
  isValidSubdomainOrigin(origin) {
    if (!this.wildcardEnabled || !origin) {
      return false;
    }

    try {
      const url = new URL(origin);
      const hostname = url.hostname;

      // Check if it's a subdomain of our root domain
      const parts = hostname.split('.');
      if (parts.length >= 3) {
        const rootDomain = parts.slice(-2).join('.');
        return rootDomain === this.rootDomain;
      }

      // Check if it's the root domain
      return hostname === this.rootDomain;
    } catch (error) {
      return false;
    }
  }

  /**
   * Extract subdomain from origin
   */
  extractSubdomainFromOrigin(origin) {
    if (!origin) return null;

    try {
      const url = new URL(origin);
      const hostname = url.hostname;
      const parts = hostname.split('.');

      if (parts.length >= 3) {
        const rootDomain = parts.slice(-2).join('.');
        if (rootDomain === this.rootDomain) {
          return parts.slice(0, -2).join('.');
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get CORS configuration statistics
   */
  getStats() {
    return {
      wildcard_enabled: this.wildcardEnabled,
      root_domain: this.rootDomain,
      static_origins_count: this.staticOrigins.length,
      wildcard_patterns_count: this.wildcardPatterns.length,
      static_origins: this.staticOrigins,
      subdomain_pattern: this.subdomainPattern
    };
  }

  /**
   * Add dynamic origin to allowed list
   */
  addDynamicOrigin(origin) {
    if (origin && !this.staticOrigins.includes(origin)) {
      this.staticOrigins.push(origin);
      logger.info('Dynamic CORS origin added', {
        component: 'cors-config',
        operation: 'add_dynamic_origin',
        metadata: { origin }
      });
    }
  }

  /**
   * Remove dynamic origin from allowed list
   */
  removeDynamicOrigin(origin) {
    const index = this.staticOrigins.indexOf(origin);
    if (index > -1) {
      this.staticOrigins.splice(index, 1);
      logger.info('Dynamic CORS origin removed', {
        component: 'cors-config',
        operation: 'remove_dynamic_origin',
        metadata: { origin }
      });
    }
  }

  /**
   * Middleware to add CORS headers for subdomain requests
   */
  subdomainCorsMiddleware() {
    return (req, res, next) => {
      const origin = req.get('origin');

      // Check if origin is a production domain
      const isProductionDomain = origin && this.productionDomains.some(domain => origin.includes(domain));
      if (isProductionDomain) {
        // Add subdomain-specific CORS headers for production domains
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Credentials', 'true');
        res.header('Vary', 'Origin');

        // Add organization context header if available
        if (req.organization) {
          res.header('X-Organization-Context', JSON.stringify({
            id: req.organization._id,
            name: req.organization.name,
            subdomain: req.organization.subdomain
          }));
        }

        logger.debug('Production subdomain CORS headers added', {
          component: 'cors-config',
          operation: 'production_subdomain_cors_headers',
          metadata: {
            origin,
            domain_matched: this.productionDomains.find(d => origin.includes(d)),
            organization_id: req.organization?._id
          }
        });
      } else if (origin && this.isValidSubdomainOrigin(origin)) {
        // Add subdomain-specific CORS headers for other valid subdomains
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Credentials', 'true');
        res.header('Vary', 'Origin');

        // Add organization context header if available
        if (req.organization) {
          res.header('X-Organization-Context', JSON.stringify({
            id: req.organization._id,
            name: req.organization.name,
            subdomain: req.organization.subdomain
          }));
        }

        logger.debug('Subdomain CORS headers added', {
          component: 'cors-config',
          operation: 'subdomain_cors_headers',
          metadata: {
            origin,
            organization_id: req.organization?._id
          }
        });
      }

      next();
    };
  }
}

// Export singleton instance
module.exports = new CorsConfig();
