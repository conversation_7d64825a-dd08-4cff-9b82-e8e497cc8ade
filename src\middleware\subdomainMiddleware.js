// src/middleware/subdomainMiddleware.js
const logger = require('../services/logger');
const Organization = require('../models/Organization');

/**
 * Subdomain Extraction and Organization Resolution Middleware
 * Handles multi-tenant subdomain routing for organization-specific access
 */
class SubdomainMiddleware {
  constructor() {
    this.isEnabled = process.env.SUBDOMAIN_EXTRACTION_ENABLED === 'true';
    this.rootDomain = process.env.ROOT_DOMAIN;
    this.defaultSubdomain = process.env.DEFAULT_ORGANIZATION_SUBDOMAIN;
    this.enableOrganizationMapping = process.env.SUBDOMAIN_ORGANIZATION_MAPPING === 'true';
    
    // Cache for organization lookups
    this.organizationCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Main subdomain middleware function
   */
  middleware() {
    return async (req, res, next) => {
      try {
        // Skip if subdomain extraction is disabled
        if (!this.isEnabled) {
          return next();
        }

        // Extract subdomain from request
        const subdomainInfo = this.extractSubdomain(req);
        
        // Add subdomain information to request object
        req.subdomain = subdomainInfo;
        
        // Resolve organization if subdomain mapping is enabled
        if (this.enableOrganizationMapping && subdomainInfo.subdomain) {
          await this.resolveOrganization(req, subdomainInfo.subdomain);
        }

        // Log subdomain extraction
        logger.info('Subdomain extracted', {
          component: 'subdomain-middleware',
          operation: 'extract_subdomain',
          metadata: {
            subdomain: subdomainInfo.subdomain,
            host: subdomainInfo.host,
            is_subdomain: subdomainInfo.isSubdomain,
            organization_resolved: !!req.organization
          }
        });

        next();
      } catch (error) {
        logger.error('Subdomain middleware error', {
          component: 'subdomain-middleware',
          operation: 'middleware_error',
          error: error.message,
          metadata: {
            host: req.get('host'),
            url: req.originalUrl
          }
        });
        
        // Continue without subdomain resolution on error
        next();
      }
    };
  }

  /**
   * Extract subdomain from request headers
   */
  extractSubdomain(req) {
    const host = req.get('host') || '';
    const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
    
    // Handle localhost and development environments
    if (host.includes('localhost') || host.includes('127.0.0.1')) {
      return {
        subdomain: null,
        host: host,
        fullUrl: `${protocol}://${host}`,
        isSubdomain: false,
        environment: 'development'
      };
    }

    // Handle Vercel preview deployments
    if (host.includes('.vercel.app')) {
      return {
        subdomain: null,
        host: host,
        fullUrl: `${protocol}://${host}`,
        isSubdomain: false,
        environment: 'preview'
      };
    }

    // Extract subdomain from production domain
    const parts = host.split('.');
    
    // Check if it's a subdomain of our root domain
    if (parts.length >= 3) {
      const potentialRootDomain = parts.slice(-2).join('.');
      
      if (potentialRootDomain === this.rootDomain) {
        const subdomain = parts.slice(0, -2).join('.');
        
        return {
          subdomain: subdomain,
          host: host,
          fullUrl: `${protocol}://${host}`,
          isSubdomain: true,
          rootDomain: this.rootDomain,
          environment: 'production'
        };
      }
    }

    // Root domain access
    if (host === this.rootDomain) {
      return {
        subdomain: this.defaultSubdomain,
        host: host,
        fullUrl: `${protocol}://${host}`,
        isSubdomain: false,
        isRootDomain: true,
        environment: 'production'
      };
    }

    // Unknown domain
    return {
      subdomain: null,
      host: host,
      fullUrl: `${protocol}://${host}`,
      isSubdomain: false,
      environment: 'unknown'
    };
  }

  /**
   * Resolve organization from subdomain
   */
  async resolveOrganization(req, subdomain) {
    try {
      // Check cache first
      const cacheKey = `org_${subdomain}`;
      const cached = this.organizationCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        req.organization = cached.organization;
        req.organizationContext = {
          source: 'subdomain',
          subdomain: subdomain,
          cached: true
        };
        return;
      }

      // Query database for organization
      const organization = await Organization.findOne({ 
        subdomain: subdomain.toLowerCase() 
      }).select('_id name subdomain branding status');

      if (organization) {
        // Cache the result
        this.organizationCache.set(cacheKey, {
          organization: organization,
          timestamp: Date.now()
        });

        // Add to request
        req.organization = organization;
        req.organizationContext = {
          source: 'subdomain',
          subdomain: subdomain,
          cached: false,
          organization_id: organization._id,
          organization_name: organization.name
        };

        logger.info('Organization resolved from subdomain', {
          component: 'subdomain-middleware',
          operation: 'resolve_organization',
          metadata: {
            subdomain: subdomain,
            organization_id: organization._id,
            organization_name: organization.name
          }
        });
      } else {
        logger.warn('Organization not found for subdomain', {
          component: 'subdomain-middleware',
          operation: 'organization_not_found',
          metadata: {
            subdomain: subdomain
          }
        });
      }
    } catch (error) {
      logger.error('Failed to resolve organization from subdomain', {
        component: 'subdomain-middleware',
        operation: 'resolve_organization_error',
        error: error.message,
        metadata: {
          subdomain: subdomain
        }
      });
    }
  }

  /**
   * Clear organization cache
   */
  clearCache() {
    this.organizationCache.clear();
    logger.info('Organization cache cleared', {
      component: 'subdomain-middleware',
      operation: 'clear_cache'
    });
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.organizationCache.size,
      timeout: this.cacheTimeout,
      enabled: this.isEnabled
    };
  }

  /**
   * Helper method to build organization-specific URLs
   */
  static buildOrganizationUrl(subdomain, path = '', protocol = 'https') {
    const rootDomain = process.env.ROOT_DOMAIN;
    
    if (!subdomain || subdomain === process.env.DEFAULT_ORGANIZATION_SUBDOMAIN) {
      return `${protocol}://${rootDomain}${path}`;
    }
    
    return `${protocol}://${subdomain}.${rootDomain}${path}`;
  }

  /**
   * Helper method to extract subdomain from URL
   */
  static extractSubdomainFromUrl(url) {
    try {
      const urlObj = new URL(url);
      const host = urlObj.hostname;
      const rootDomain = process.env.ROOT_DOMAIN;
      
      const parts = host.split('.');
      if (parts.length >= 3) {
        const potentialRootDomain = parts.slice(-2).join('.');
        if (potentialRootDomain === rootDomain) {
          return parts.slice(0, -2).join('.');
        }
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }
}

// Export singleton instance
module.exports = new SubdomainMiddleware();
