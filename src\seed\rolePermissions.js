// seed/rolePermissions.js
module.exports = [
  {
    role: 'superadmin',
    permissions: [
      'org:create', 'org:delete', 'org:update', 'user:manage', 'settings:all',
      'event:create', 'event:edit', 'event:delete', 'event:view'
    ]
  },
  {
    role: 'orgadmin',
    permissions: [
      'user:add', 'user:remove', 'user:changeRole', 'event:create', 'event:edit', 'event:delete', 'event:view'
    ]
  },
  {
    role: 'orgmanager',
    permissions: ['event:create', 'event:edit', 'event:view', 'user:view']
  },
  {
    role: 'orgmember',
    permissions: ['event:view', 'event:register']
  },
  {
    role: 'guest',
    permissions: ['event:view']
  }
];
